This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
miniprogram/
  components/
    date_select.js
    date_select.json
    date_select.wxml
    date_select.wxss
    time_table.js
    time_table.json
    time_table.wxml
    time_table.wxss
  images/
    bg-meetings.svg
    icon-index-bg.svg
    icon-index-build.svg
    icon-index-not.svg
    icon-meetings-not.svg
  pages/
    meeting/
      detail.js
      detail.json
      detail.wxml
      detail.wxss
      edit.js
      edit.json
      edit.wxml
      edit.wxss
      meeting.js
      meeting.json
      meeting.wxml
      meeting.wxss
      reserve.js
      reserve.json
      reserve.wxml
      reserve.wxss
    my/
      my.json
    room/
      detail.js
      detail.json
      detail.wxml
      detail.wxss
      edit.js
      edit.json
      edit.wxml
      edit.wxss
      history.js
      history.json
      history.wxml
      history.wxss
      list.js
      list.json
      list.wxml
      list.wxss
      meeting.js
      meeting.json
      meeting.wxml
      meeting.wxss
  utils/
    api.js
    apiviewws.js
    http-cookie.js
    meetings.js
    request.js
    set-cookie.js
    time.js
    timeUtils.wxs
  weui/
    weui.wxss
  app.js
  app.json
  app.wxss
  project.config.json
  project.private.config.json
  sitemap.json
```

# Files

## File: miniprogram/components/date_select.js
```javascript
// components/date_select.js
const weekStr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
  },

  /**
   * 组件的初始数据
   */
  data: {
    start_date: '',
    end_date: '',
    select_date: '',
    date_range: []
  },
  lifetimes: {
    // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
    attached: function () {
      
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    setDateRange: function(start_date, end_date){
      let select_date_ok = false
      start_date = new Date(start_date)
      if (isNaN(start_date)) {
        start_date = app.nowDate()
      }
      end_date = new Date(end_date)
      if (isNaN(end_date)) {
        end_date = this.addDay(start_date, 19)
      }
      start_date = new Date(this.dateId(start_date))
      end_date = new Date(this.dateId(end_date))
      let date_range = []
      let current_date = start_date
      while (current_date <= end_date) {
        select_date_ok = select_date_ok || this.data.select_date == this.dateId(current_date)
        date_range.push({
          id: this.dateId(current_date),
          show: this.dateShow(current_date),
          desc: this.dateDesc(current_date),
        })
        current_date = this.addDay(current_date, 1)
      }
      let set_data = { date_range: date_range, start_date: this.dateId(start_date), end_date: this.dateId(end_date)}
      if (!select_date_ok) {
        set_data.select_date = this.dateId(start_date)
      }
      this.setData(set_data)
    },
    change: function(){
      this.triggerEvent('change', { select_date: this.data.select_date }, { })
    },
    tap: function(e){
      this.setData({select_date: e.currentTarget.id})
      this.change()
    },
    addDay: function(date, day){
      return new Date(Date.parse(date) + 86400000 * day)
    },
    formatNumber: function(n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    },
    dateId: function(date){
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return [year, month, day].map(this.formatNumber).join('-')
    },
    dateShow: function (date) {
      const month = date.getMonth() + 1
      const day = date.getDate()
      return [month, day].map(this.formatNumber).join('/')
    },
    dateDesc: function (date) {
      const now = app.nowDate()
      if(this.dateId(now) == this.dateId(date)){
        return "今天"
      }
      return weekStr[date.getDay()]
    }
  }
})
```

## File: miniprogram/components/date_select.json
```json
{
  "component": true,
  "usingComponents": {}
}
```

## File: miniprogram/components/date_select.wxml
```
<!--components/date_select.wxml-->
<scroll-view scroll-x class="date_select" enable-flex="{{true}}">
  <view class="box box_{{index}} {{item.id == select_date ? 'select' : ''}}" wx:for="{{date_range}}" wx:key="id" id="{{item.id}}" bindtap='tap'>
    <view class="desc">{{item.desc}}</view>
    <view class="show">{{item.show}}</view>
  </view>
</scroll-view>
```

## File: miniprogram/components/date_select.wxss
```
/* components/date_select.wxss */
.date_select{
  white-space: nowrap;
}
.date_select .box{
  background: #f8f8f8;
  margin: 10rpx 10rpx 10rpx 0rpx;
  text-align: center;
  font-size: 24rpx;
  line-height: 48rpx;
  height: 132rpx;
  width: 96rpx;
  border-radius: 15rpx;
  display: inline-block;
}
.date_select .box.box_0{
  margin-left: 18rpx;
}

.date_select .box .show{
  margin-top: 3rpx;
  font-size: 30rpx;
}
.date_select .box .desc{
  margin-top: 12rpx;
  font-size: 24rpx;
}
.date_select .box.select {
    background: linear-gradient(to bottom right, rgba(32, 197, 98, 1), rgba(255, 255, 255, 0.5)); /* 从左上角到右下角渐变 */
  }
```

## File: miniprogram/components/time_table.js
```javascript
// components/time_table.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    no_title_desc: String,
    title_label: String
  },

  /**
   * 组件的初始数据
   */
  data: {
    titles: [],
    labels: [],
    td_data: {},
  },
  /**
   * 组件的方法列表
   */
  methods: {
    set_data: function({titles, labels, td_data} = {}){
      let data = {}
      if (titles !== undefined) {
        data['titles'] = titles
      }
      if (labels !== undefined) {
        data['labels'] = labels
      }
      if (td_data !== undefined) {
        data['td_data'] = td_data
      }
      
      this.setData(data)
    },
    event: function(event, data = {}){
      this.triggerEvent(event, data, {})
    },
    title_label_click: function(e){
      this.event('title_label_click')
    },
    title_click: function (e) {
      this.event('title_click', {
        title_id: e.currentTarget.id
      })
    },
    label_click: function (e) {
      this.event('label_click', {
        label_id: e.currentTarget.id
      })
    },
    data_click: function(e){
      this.event('data_click', {
        title_id: e.currentTarget.dataset.title,
        label_id: e.currentTarget.dataset.label,
        data_id: e.currentTarget.dataset.data,
      })
    }
  }
})
```

## File: miniprogram/components/time_table.json
```json
{
  "component": true,
  "usingComponents": {}
}
```

## File: miniprogram/components/time_table.wxml
```
<!--components/time_table.wxml-->
<view class='time_table'>
  <view class="table">
    <view class='thead'>
      <view class="tr">
        <view class="th label" bindtap='title_label_click'>
        {{title_label}}
        </view>
        <view class="th" wx:for="{{titles}}" wx:key="id" id="{{item.id}}" bindtap='title_click'>
          <text class="title_name">{{item.name}}</text>
        </view>
        <view class="th" wx:if='{{titles.length == 0}}'>
        {{no_title_desc}}
        </view>
      </view>
    </view>
    <scroll-view scroll-y class="tbody">
      <view class="tr" wx:for="{{labels}}" wx:key="id" wx:for-item="label">
        <view class="td label {{label.text ? 'top' : 'buttom'}}" id="{{label.id}}" bindtap='label_click'>
          <view class="label_item">
          {{label.text}}
          </view>
        </view>
        <view 
          class="td {{td_data[title.id][label.id].clazz}}"
          wx:for="{{titles}}" 
          wx:key="id" 
          wx:for-item="title" 
          bindtap="data_click" 
          data-title="{{title.id}}" 
          data-label="{{label.id}}" 
          data-data="{{td_data[title.id][label.id].meeting_id}}">
          {{td_data[title.id][label.id].text}}
        </view>
        <view class="td" wx:if='{{titles.length == 0}}'>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
```

## File: miniprogram/components/time_table.wxss
```
/* components/time_table.wxss */
.time_table{
  height: 100%;
}
.table {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  font-size: 32rpx;
  line-height: 32rpx;
  border-right: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
}
.table .thead{
  height: 48rpx;
}
.table .tbody{
  position: absolute;
  top: 49rpx;
  bottom: 0;
  width: 100%;
  overflow: auto;
}
.table .tr{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.tr{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.table .th, .table .td{
  -webkit-box-flex:1;
  -webkit-flex:1;
  flex:1;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  margin-right:-1rpx;
  margin-bottom:-1rpx;
}
.table .th {
  font-weight: bold;
  border: 1rpx solid #ddd;
  background-color: #f5fafe;
  line-height: 48rpx;
  height: 48rpx;
}

.table .td {
  border-left: 1rpx solid #ddd;
  border-right: 1rpx solid #ddd;
  height: 36rpx;
}
.table .td.expire{
  border-top: 1rpx solid #f5f5f5;
  background-color: #f5f5f5;
}
.table .td.in_use{
  border-top: 1rpx solid #fcf8e3;
  background-color: #fcf8e3;
}
.table .td.selected{
  border-top: 1rpx solid #dfffd8;
  background-color: #dfffd8;
}
.table .td.top{
  border-top: 1rpx solid #ddd;
}
.table .td.bottom{
  border-bottom: 1rpx solid #ddd;
}
.label_item{
  font-size: 28rpx;
}
view.label{
  -webkit-box-flex:none!important;
  -webkit-flex:none!important;
  flex:none!important;
  width: 100rpx;
  background-color: #f5fafe;
  float: left;
}
```

## File: miniprogram/images/bg-meetings.svg
```
<svg id="图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 4000 3000"><defs><style>.cls-1,.cls-5,.cls-6{fill:#22c678;}.cls-1,.cls-10,.cls-14,.cls-2,.cls-3,.cls-9{stroke:#3d3d3c;stroke-linecap:round;stroke-linejoin:round;stroke-width:4px;}.cls-17,.cls-2{fill:#4f4f5b;}.cls-12,.cls-3{fill:#f5f6f7;}.cls-23,.cls-4{fill:#cfcfd0;}.cls-5{opacity:0.17;}.cls-7{clip-path:url(#clip-path);}.cls-8{fill:#14a35b;}.cls-9{fill:none;}.cls-10,.cls-11{fill:#fff;}.cls-13{clip-path:url(#clip-path-2);}.cls-14{fill:#ecedee;}.cls-15{clip-path:url(#clip-path-3);}.cls-16{clip-path:url(#clip-path-4);}.cls-18{clip-path:url(#clip-path-5);}.cls-19{clip-path:url(#clip-path-6);}.cls-20{clip-path:url(#clip-path-7);}.cls-21{fill:#3e404c;}.cls-22{clip-path:url(#clip-path-8);}.cls-23{opacity:0.27;}.cls-24,.cls-26{fill:#3d3d3c;}.cls-25{clip-path:url(#clip-path-9);}.cls-26{opacity:0.29;}</style><clipPath id="clip-path"><path class="cls-1" d="M3332.4529,1337.2454H2690.5822c-87.606,0-158.6246-71.2691-158.6246-159.1839V533.9274c0-87.9148,71.0186-159.1838,158.6246-159.1838h641.8707c87.606,0,158.6246,71.269,158.6246,159.1838v644.1341C3491.0775,1265.9763,3420.0589,1337.2454,3332.4529,1337.2454Z"/></clipPath><clipPath id="clip-path-2"><path class="cls-3" d="M3023.7764,1744.6614c.4178-4.6643-7.1659,41.81-6.7984,37.455,8.1747-96.8795,21.3665-152.3874,79.6387-165.59,67.4055-15.2723,146.7389-166.1927,110.8043-171.121s-36.7418,89.4287-112.7467,78.4693c-56.7639-8.185-168.7209-12.6964-188.145,151.9082s117.6649,64.2144,117.6649,64.2144"/></clipPath><clipPath id="clip-path-3"><path class="cls-1" d="M2142.4083,2468.1329l-47.1034,46.0907s-505.0264-40.1768-496.2856-111.144-48.66-741.792,80.084-757.42c391.2548-47.4946,837.9552,44.9083,861.2641,59.6931s104.4907,348.4946,104.4907,348.4946l254.2979-440.36,160.969,215.652s-209.4547,659.2221-273.3225,685.0843-256.398-60.8756-256.398-60.8756Z"/></clipPath><clipPath id="clip-path-4"><path class="cls-3" d="M1986.353,1381.4713c48.3779,294.6582-46.3135,305.4434-46.3135,305.4434l170.6919,232.1993L2239.81,1731.5387s-101.0635-187.8719-67.9048-265.7911C2187.0682,1430.1168,1986.353,1381.4713,1986.353,1381.4713Z"/></clipPath><clipPath id="clip-path-5"><path class="cls-2" d="M3144.3129,2318.19c18.5769,4.5717,53.2623,7.65,93.0175,7.65s74.4412-3.0779,93.0178-7.65c9.1464-2.2509,14.4071-4.8619,14.4071-7.6495v276.3055c0,8.45-48.0957,15.2993-107.4249,15.2993s-107.4247-6.85-107.4247-15.2993V2310.54C3129.9057,2313.328,3135.1669,2315.939,3144.3129,2318.19Z"/></clipPath><clipPath id="clip-path-6"><polygon class="cls-3" points="2064.19 2420.568 2287.314 2450.594 2266.009 2540.152 2177.144 2551.195 2005.24 2532.303 2064.19 2420.568"/></clipPath><clipPath id="clip-path-7"><polygon class="cls-2" points="2953.806 2590.126 2204.421 2590.126 2342.277 2040.807 3091.661 2040.807 2953.806 2590.126"/></clipPath><clipPath id="clip-path-8"><path class="cls-3" d="M2254.8238,1252.7663c-6.8277,98.4942-17.5966,122.7441,12.6028,190.4161,46.887,105.0664-56.6057,191.8375-189.2408,58.87-114.763-115.05-19.4373-211.53-19.4373-211.53Z"/></clipPath><clipPath id="clip-path-9"><path class="cls-4" d="M2224.24,1458.0635c-12.5417,63.22-71.7581,36.0169-72.3551-3.37a4.8526,4.8526,0,0,1,3.1846-4.5555c8.0535-3.041,31.4282-9.3672,65.8988,2.3083A4.9649,4.9649,0,0,1,2224.24,1458.0635Z"/></clipPath></defs><title>商务协作描边矢量插画人物场景插画6001线条绿</title><path class="cls-5" d="M1169.4876,1774.5587c89.3835,46.2248,202.8405,72.7586,327.2408,73.98.0748.4379.1261.8764.2021,1.3144,52.98,305.2962,480.0468,486.13,953.8809,403.9032,451.29-78.3144,782.2267-367.0081,766.9258-658.0621,187.673-101.9912,300.3331-260.23,273.4136-415.3544-17.1008-98.5441-87.6791-179.283-190.9785-233.4943,48.0811-86.7448,67.746-179.2008,51.8388-270.8667-52.98-305.2963-480.0463-486.13-953.8807-403.9035-300.4476,52.138-547.5041,197.5361-674.6139,374.7838-117.5426-14.8643-245.74-12.3756-377.5728,10.502C872.11,739.5876,530.94,1053.7369,583.9192,1359.0332,622.5444,1581.6121,860.029,1737.9987,1169.4876,1774.5587Z"/><path class="cls-6" d="M3332.4529,1337.2454H2690.5822c-87.606,0-158.6246-71.2691-158.6246-159.1839V533.9274c0-87.9148,71.0186-159.1838,158.6246-159.1838h641.8707c87.606,0,158.6246,71.269,158.6246,159.1838v644.1341C3491.0775,1265.9763,3420.0589,1337.2454,3332.4529,1337.2454Z"/><g class="cls-7"><path class="cls-8" d="M3459.7342,348.8637c123.444,1010.39-694.3724,977.9868-975.979,988.3817S3417.3,1622.4974,3417.3,1622.4974l142.7321-77.4245,23.1458-1170.3293"/></g><path class="cls-9" d="M3332.4529,1337.2454H2690.5822c-87.606,0-158.6246-71.2691-158.6246-159.1839V533.9274c0-87.9148,71.0186-159.1838,158.6246-159.1838h641.8707c87.606,0,158.6246,71.269,158.6246,159.1838v644.1341C3491.0775,1265.9763,3420.0589,1337.2454,3332.4529,1337.2454Z"/><path class="cls-10" d="M2947.7075,976.4483l-146.89-137.067a38.3511,38.3511,0,0,1-1.9553-54.07l.0054-.0057a38.0274,38.0274,0,0,1,53.873-1.9621l109.7136,102.3753a12.3624,12.3624,0,0,0,17.557-.6852l188.2124-205.2a38.0275,38.0275,0,0,1,53.8646-2.24h0a38.3508,38.3508,0,0,1,2.2322,54.0574l-222.6052,242.69A38.0277,38.0277,0,0,1,2947.7075,976.4483Z"/><path class="cls-9" d="M3230.559,959.5l-23.4367,26.8427"/><path class="cls-11" d="M2033.0785,497.2361H527.066a77.002,77.002,0,0,0-76.864,77.1379v691.7423a77.002,77.002,0,0,0,76.864,77.1379h984.4921c39.68,50.27,84.945,107.5386,117.8459,149.1379,15.2026,19.225,45.8477,8.19,45.8477-16.3538V1343.2542h357.8268a77.002,77.002,0,0,0,76.864-77.1379V574.374A77.002,77.002,0,0,0,2033.0785,497.2361Z"/><path class="cls-9" d="M2033.0785,497.2361H527.066a77.002,77.002,0,0,0-76.864,77.1379v691.7423a77.002,77.002,0,0,0,76.864,77.1379h984.4921c39.68,50.27,84.945,107.5386,117.8459,149.1379,15.2026,19.225,45.8477,8.19,45.8477-16.3538V1343.2542h357.8268a77.002,77.002,0,0,0,76.864-77.1379V574.374A77.002,77.002,0,0,0,2033.0785,497.2361Z"/><ellipse class="cls-10" cx="785.3155" cy="897.1125" rx="76.7761" ry="77.0468"/><ellipse class="cls-10" cx="1106.2684" cy="897.1125" rx="76.7761" ry="77.0468"/><ellipse class="cls-10" cx="1427.2212" cy="897.1125" rx="76.7761" ry="77.0468"/><ellipse class="cls-10" cx="1748.1741" cy="897.1125" rx="76.7761" ry="77.0468"/><path class="cls-9" d="M842.0888,596.9433l-67.1381,50.51"/><path class="cls-9" d="M1877.5449,1085.716a176.8869,176.8869,0,0,1-51.0143,61.2348"/><path class="cls-12" d="M3023.7764,1744.6614c.4178-4.6643-7.1659,41.81-6.7984,37.455,8.1747-96.8795,21.3665-152.3874,79.6387-165.59,67.4055-15.2723,146.7389-166.1927,110.8043-171.121s-36.7418,89.4287-112.7467,78.4693c-56.7639-8.185-168.7209-12.6964-188.145,151.9082s117.6649,64.2144,117.6649,64.2144"/><g class="cls-13"><path class="cls-4" d="M2920.5728,1613.8117c12.97-28.0159,103.4024,75.5319,130.8514,67.8447s-8.9369,40.9985-8.9369,40.9985l-55.1359,8.9888-57.8521-28.8474"/></g><path class="cls-9" d="M3023.7764,1744.6614c.4178-4.6643-7.1659,41.81-6.7984,37.455,8.1747-96.8795,21.3665-152.3874,79.6387-165.59,67.4055-15.2723,146.7389-166.1927,110.8043-171.121s-36.7418,89.4287-112.7467,78.4693c-56.7639-8.185-168.7209-12.6964-188.145,151.9082s117.6649,64.2144,117.6649,64.2144"/><polygon class="cls-14" points="2921.899 1593.333 2879.318 1682.682 3024.027 1893.378 3048.056 1722.977 2921.899 1593.333"/><rect class="cls-1" x="1101.7702" y="2484.789" width="2448.0278" height="140.4674"/><path class="cls-6" d="M2142.4083,2468.1329l-47.1034,46.0907s-505.0264-40.1768-496.2856-111.144-48.66-741.792,80.084-757.42c391.2548-47.4946,837.9552,44.9083,861.2641,59.6931s104.4907,348.4946,104.4907,348.4946l254.2979-440.36,160.969,215.652s-209.4547,659.2221-273.3225,685.0843-256.398-60.8756-256.398-60.8756Z"/><path class="cls-9" d="M2142.4083,2468.1329l-47.1034,46.0907s-505.0264-40.1768-496.2856-111.144-48.66-741.792,80.084-757.42c391.2548-47.4946,837.9552,44.9083,861.2641,59.6931s104.4907,348.4946,104.4907,348.4946l254.2979-440.36,160.969,215.652s-209.4547,659.2221-273.3225,685.0843-256.398-60.8756-256.398-60.8756Z"/><path class="cls-12" d="M1986.353,1381.4713c48.3779,294.6582-46.3135,305.4434-46.3135,305.4434l170.6919,232.1993L2239.81,1731.5387s-101.0635-187.8719-67.9048-265.7911C2187.0682,1430.1168,1986.353,1381.4713,1986.353,1381.4713Z"/><g class="cls-16"><path class="cls-4" d="M2239.81,1632.53c-152.2953,38.8764-184.1635-141.17-184.1635-141.17l135.9178,46.8016,48.2457,68.1327"/></g><path class="cls-9" d="M1986.353,1381.4713c48.3779,294.6582-46.3135,305.4434-46.3135,305.4434l170.6919,232.1993L2239.81,1731.5387s-101.0635-187.8719-67.9048-265.7911C2187.0682,1430.1168,1986.353,1381.4713,1986.353,1381.4713Z"/><path class="cls-14" d="M1981.2079,1701.0812s156.83,675.3857,161.2,767.0517H2365.3s-110.0161-688.4418-136.94-733.328l-110.4664,6.1861Z"/><path class="cls-2" d="M3254.0029,2448.6929c0-56.4941,45.2877-102.455,100.9536-102.455s100.9522,45.9609,100.9522,102.455-45.2864,102.4551-100.9522,102.4551S3254.0029,2505.187,3254.0029,2448.6929Zm32.9482,0c0,38.0558,30.5069,69.0166,68.0054,69.0166s68.004-30.9608,68.004-69.0166-30.5069-69.0166-68.004-69.0166S3286.9511,2410.6372,3286.9511,2448.6929Z"/><path class="cls-2" d="M3237.33,2295.2409c59.3292,0,107.4246,6.85,107.4249,15.3,0,2.7876-5.2607,5.3986-14.4071,7.6495-18.5766-4.5715-53.2624-7.6495-93.0178-7.6495s-74.4406,3.078-93.0175,7.6495c-9.146-2.2509-14.4072-4.8619-14.4072-7.6495C3129.9061,2302.0906,3178.0018,2295.2409,3237.33,2295.2409Z"/><path class="cls-17" d="M3144.3129,2318.19c18.5769,4.5717,53.2623,7.65,93.0175,7.65s74.4412-3.0779,93.0178-7.65c9.1464-2.2509,14.4071-4.8619,14.4071-7.6495v276.3055c0,8.45-48.0957,15.2993-107.4249,15.2993s-107.4247-6.85-107.4247-15.2993V2310.54C3129.9057,2313.328,3135.1669,2315.939,3144.3129,2318.19Z"/><g class="cls-18"><path class="cls-6" d="M3091.6612,2568.814c264.354,39.4287,253.0941-258.2736,253.0941-258.2736l49.73,236.8768-28.9386,97.1931-296.1946-19.3539"/></g><path class="cls-9" d="M3144.3129,2318.19c18.5769,4.5717,53.2623,7.65,93.0175,7.65s74.4412-3.0779,93.0178-7.65c9.1464-2.2509,14.4071-4.8619,14.4071-7.6495v276.3055c0,8.45-48.0957,15.2993-107.4249,15.2993s-107.4247-6.85-107.4247-15.2993V2310.54C3129.9057,2313.328,3135.1669,2315.939,3144.3129,2318.19Z"/><path class="cls-14" d="M3248.5449,1868.8291c-72.6478-39.75-88.8719,145.8537-21.4582,210.3007s26.5777,158.5735,0,185.709-13.6351,56.445,30.7393-21.9943c41.7814-73.8559,49.2076-150.5756.7455-202.0372S3303.9744,1899.1578,3248.5449,1868.8291Z"/><ellipse class="cls-14" cx="3295.3693" cy="2022.7976" rx="17.7457" ry="18.0098"/><path class="cls-2" d="M1985.2784,1647.0348c4.4831,22.2875,95.4879,91.7127,150.3462,92.6983l-31.8217,95.6087s-141.6384-127.15-144.5094-160.6619S1985.2784,1647.0348,1985.2784,1647.0348Z"/><path class="cls-2" d="M2187.4877,1821.5426s-48.4492-61.1107-51.8631-81.81c0,0,76.0166-51.3339,57.5365-89.906l57.5365,88.9862S2215.7454,1802.6094,2187.4877,1821.5426Z"/><polygon class="cls-1" points="1879.093 1632.53 1985.549 1633.12 2065.84 2076.017 1953.023 1784.638 1979.246 1749.155 1879.093 1632.53"/><polygon class="cls-1" points="2309.649 1659.867 2191.565 1646.136 2295.617 2053.847 2314.311 1789.509 2282.262 1765.853 2309.649 1659.867"/><polygon class="cls-12" points="2064.19 2420.568 2287.314 2450.594 2266.009 2540.152 2177.144 2551.195 2005.24 2532.303 2064.19 2420.568"/><g class="cls-19"><path class="cls-4" d="M2103.459,2414.0773c5.76,87.1218,41.5754,100.8142,42.8182,140.9454s-116.433-20.09-116.433-20.09l41.8825-101.21"/></g><polygon class="cls-9" points="2064.19 2420.568 2287.314 2450.594 2266.009 2540.152 2177.144 2551.195 2005.24 2532.303 2064.19 2420.568"/><polygon class="cls-14" points="2040.851 2391.684 2113.609 2397.325 2092.149 2547.417 2029.844 2534.932 2040.851 2391.684"/><path class="cls-6" d="M1803.7376,1818.1516c36.99,207.688-14.1113,528.8627-14.1113,528.8627l313.8327,13.6807-38.63,199.7956s-578.6352,52.5886-616.7525-38.7493c-20.992-50.3018,97.4325-881.1318,235.0762-876.1048"/><path class="cls-9" d="M1803.7376,1818.1516c36.99,207.688-14.1113,528.8627-14.1113,528.8627l313.8327,13.6807-38.63,199.7956s-578.6352,52.5886-616.7525-38.7493c-20.992-50.3018,97.4325-881.1318,235.0762-876.1048"/><path class="cls-2" d="M1937.89,2540.628h986.7165a0,0,0,0,1,0,0v49.4981a0,0,0,0,1,0,0h-1024.45a0,0,0,0,1,0,0v-11.7644A37.7337,37.7337,0,0,1,1937.89,2540.628Z"/><polygon class="cls-17" points="2942.546 2590.126 2193.161 2590.126 2331.017 2040.807 3080.401 2040.807 2942.546 2590.126"/><polygon class="cls-9" points="2942.546 2590.126 2193.161 2590.126 2331.017 2040.807 3080.401 2040.807 2942.546 2590.126"/><polygon class="cls-17" points="2953.806 2590.126 2204.421 2590.126 2342.277 2040.807 3091.661 2040.807 2953.806 2590.126"/><g class="cls-20"><path class="cls-21" d="M3061.6716,2004.7878C2917.85,2768.46,2193.1611,2590.126,2193.1611,2590.126l727.4117,203.958,171.1879-402.4,52.5522-416.6443Z"/></g><polygon class="cls-9" points="2953.806 2590.126 2204.421 2590.126 2342.277 2040.807 3091.661 2040.807 2953.806 2590.126"/><path class="cls-14" d="M1265.4644,2484.8573c-14.7811,0-26.7636,12.335-26.7636,27.5509v35.1353c0,15.216,11.9825,27.551,26.7636,27.551h493.7112v-90.2372Z"/><path class="cls-2" d="M1238.7008,2547.5435v-35.1353a27.3513,27.3513,0,0,1,27.147-27.5509h490.0534a10.8118,10.8118,0,0,0,10.731-10.8907h0a10.8117,10.8117,0,0,0-10.731-10.8907H1259.0038c-28.2941,0-51.2311,23.2782-51.2311,51.9934v29.8134c0,28.715,22.9369,51.9932,51.2309,51.9932h496.8976a10.8117,10.8117,0,0,0,10.731-10.8907h0a10.8117,10.8117,0,0,0-10.731-10.8907H1265.8478A27.3513,27.3513,0,0,1,1238.7008,2547.5435Z"/><path class="cls-2" d="M1995.38,1096.5124c62.4689,23.5489,94.5445-7.6188,94.5445-7.6188S2050.2946,1105.5164,1995.38,1096.5124Z"/><path class="cls-12" d="M2254.8238,1252.7663c-6.8277,98.4942-17.5966,122.7441,12.6028,190.4161,46.887,105.0664-56.6057,191.8375-189.2408,58.87-114.763-115.05-19.4373-211.53-19.4373-211.53Z"/><g class="cls-22"><ellipse class="cls-23" cx="2268.3564" cy="1443.3715" rx="36.4466" ry="27.8513" transform="translate(-11.5355 18.2908) rotate(-0.4608)"/><ellipse class="cls-23" cx="2096.2452" cy="1444.7423" rx="36.4466" ry="27.8513" transform="matrix(1, -0.008, 0.008, 1, -11.5521, 16.9066)"/><path class="cls-24" d="M2227.8158,1359.1683h0a12.9581,12.9581,0,0,1,12.9581,12.9581v22.0516a12.958,12.958,0,0,1-12.958,12.958h0a12.9583,12.9583,0,0,1-12.9583-12.9583v-22.0512a12.9582,12.9582,0,0,1,12.9582-12.9582Z" transform="translate(4559.06 2583.8073) rotate(175.4154)"/><path class="cls-24" d="M2138.7525,1359.532h0a12.958,12.958,0,0,1,12.958,12.958v22.0516a12.9581,12.9581,0,0,1-12.9581,12.9581h0a12.9582,12.9582,0,0,1-12.9582-12.9582V1372.49A12.9583,12.9583,0,0,1,2138.7525,1359.532Z" transform="translate(4381.2384 2591.669) rotate(175.4158)"/><path class="cls-24" d="M2226.7018,1328.4343c26.9747,5.6017,20.863,20.6027,3.7731,18.2046S2209.0781,1324.7745,2226.7018,1328.4343Z"/><path class="cls-24" d="M2135.1151,1327.9777c-25.746,9.8294-26.9209,22.7661-10.4273,17.6749S2151.936,1321.5557,2135.1151,1327.9777Z"/><path class="cls-4" d="M2172.8077,1409.7562c-.2931,8.7873,7.32,5.5555,16.5687,5.8662s16.6293,4.0463,16.9225-4.741-6.9665-16.1628-16.2148-16.4735S2173.1009,1400.9688,2172.8077,1409.7562Z"/><path class="cls-4" d="M2224.24,1458.0635c-12.5417,63.22-71.7581,36.0169-72.3551-3.37a4.8526,4.8526,0,0,1,3.1846-4.5555c8.0535-3.041,31.4282-9.3672,65.8988,2.3083A4.9649,4.9649,0,0,1,2224.24,1458.0635Z"/><g class="cls-25"><ellipse class="cls-26" cx="2178.6499" cy="1492.4585" rx="28.8304" ry="32.2149" transform="translate(19.282 3012.6817) rotate(-69.5587)"/><path class="cls-14" d="M2213.2844,1447.7171c-.1541,4.1894-.7551,9.8676-1.88,10.8656-6.93,6.1482-39.3724,2.201-43.6962-1.5967-1.2035-1.0571-.91-14.5707-.91-14.5707l41.6132,2.8564"/></g></g><path class="cls-9" d="M2254.8238,1252.7663c-6.8277,98.4942-17.5966,122.7441,12.6028,190.4161,46.887,105.0664-56.6057,191.8375-189.2408,58.87-114.763-115.05-19.4373-211.53-19.4373-211.53Z"/><path class="cls-2" d="M2076.9,1389.5341c-15.7367-86.0678,4.8981-115.8378,50.9969-97.37s33.589-51.7094,87.345-17.2365c50.01,32.0709,120.4281,7.97,119.43-58.5214-.7171-47.8047-45.0824-35.4824-64.4925-81.036s-78.0611-108.3656-152.018-41.9687c-70.9759,63.7206-56.8254,26.4655-141.9057,33.7849-71.5555,6.1559-88.5582,76.333-48.5251,125.58s-97.0544,91.6467-42.4588,163.6325c33.9675,44.787,91.1326,11.06,122.8429,58.18S2076.9,1389.5341,2076.9,1389.5341Z"/><path class="cls-2" d="M2071.5838,1344.0205c6.47,100.1357,37.3072,146.7733,98.7723,99.1678,24.4474-18.9348,46.3272-23.21,63.8143-21.4922,22.1011,2.1713,41.6631,15.47,52.7989,34.9656,27.1056,47.4554,33.8224,193.3416-138.6087,136.8092-87.9264-28.8272-102.5925-116.032-100.62-179.816"/><path class="cls-3" d="M2066.836,1413.64c-29.9238-50.8886-119.52-98.3685-122.19-32.1688-3.1121,77.177,112.1731,128.0436,112.1731,128.0436Z"/><ellipse class="cls-1" cx="2150.2512" cy="1858.1399" rx="13.6181" ry="13.6662"/><ellipse class="cls-1" cx="2173.2538" cy="1961.3731" rx="13.6181" ry="13.6662"/><ellipse class="cls-1" cx="2979.1317" cy="1947.7069" rx="11.9905" ry="12.0328"/><polygon class="cls-14" points="2704.775 2223.385 2506.025 2420.793 2752.203 2402.252 2704.775 2223.385"/><ellipse class="cls-2" cx="2546.2801" cy="2207.3641" rx="106.9333" ry="66.3308" transform="translate(-792.5184 1750.8299) rotate(-33.1358)"/><polygon class="cls-2" points="2914.251 2237.513 2786.294 2237.513 2844.385 2120.22 2972.342 2120.22 2914.251 2237.513"/><polygon class="cls-14" points="2335.586 1964.531 2342.483 1988.635 2499.092 1988.635 2487.684 1967.119 2335.586 1964.531"/><path class="cls-9" d="M2156.5609,1481.49a54.29,54.29,0,0,0,36.0822,6.7977"/><path class="cls-9" d="M2011.829,1197.2981l-16.8412,17.261"/><path class="cls-9" d="M1777.8119,2346.2619a61.4148,61.4148,0,0,1-48.0826,12.69"/><path class="cls-9" d="M2461.5217,1833.1244l-27.4822,57.9637"/><path class="cls-9" d="M2403.6832,2485.6777a28.7366,28.7366,0,0,1-16.896,10.5361"/><path class="cls-9" d="M3278.7294,2383.2433a239.6267,239.6267,0,0,1-64.7732,40.79"/><path class="cls-9" d="M1997.3529,1392.4386c-2.0859,4.38-4.2181,8.9646-4.1244,13.8186s3.1643,10.0215,7.9434,10.7759"/></svg>
```

## File: miniprogram/images/icon-index-bg.svg
```
<svg id="图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 4000 3000"><defs><style>.cls-1,.cls-7{fill:#fff;}.cls-1,.cls-10,.cls-2,.cls-22,.cls-3{stroke:#3d3d3c;stroke-linecap:round;stroke-linejoin:round;stroke-width:4px;}.cls-2,.cls-4,.cls-5,.cls-6{fill:#22c678;}.cls-20,.cls-3{fill:#4f4f5b;}.cls-4{opacity:0.2;}.cls-6{opacity:0.14;}.cls-8{clip-path:url(#clip-path);}.cls-9{fill:#cfcfd0;}.cls-10{fill:none;}.cls-11{clip-path:url(#clip-path-2);}.cls-12{fill:#14a35b;}.cls-13{clip-path:url(#clip-path-3);}.cls-14{clip-path:url(#clip-path-4);}.cls-15{clip-path:url(#clip-path-5);}.cls-16{clip-path:url(#clip-path-6);}.cls-17{clip-path:url(#clip-path-7);}.cls-18{clip-path:url(#clip-path-8);}.cls-19{clip-path:url(#clip-path-9);}.cls-21{opacity:0.27;}.cls-22,.cls-25{fill:#f5f6f7;}.cls-23{clip-path:url(#clip-path-10);}.cls-24{clip-path:url(#clip-path-11);}.cls-26{clip-path:url(#clip-path-12);}.cls-27{clip-path:url(#clip-path-13);}.cls-28{clip-path:url(#clip-path-14);}.cls-29{clip-path:url(#clip-path-15);}.cls-30{clip-path:url(#clip-path-16);}.cls-31{clip-path:url(#clip-path-17);}.cls-32{clip-path:url(#clip-path-18);}.cls-33{clip-path:url(#clip-path-19);}.cls-34{clip-path:url(#clip-path-20);}.cls-35{clip-path:url(#clip-path-21);}</style><clipPath id="clip-path"><path class="cls-1" d="M1898.1873,2183.8624s141.58,90.5359,156.756,119.4248l84.4256-127.417-203.4945-108.1311Z"/></clipPath><clipPath id="clip-path-2"><path class="cls-2" d="M1929.3837,2207.8c101.6837-12.35,101.6837-187.8891,101.6837-187.8891l-377.8409-205.4329-109.2951-527.804-268.0594,58.014,189.42,534.9785a146.2836,146.2836,0,0,0,63.0861,82.162S1870.8508,2214.91,1929.3837,2207.8Z"/></clipPath><clipPath id="clip-path-3"><polygon class="cls-1" points="1258.991 523.884 1267.178 760.554 1371.882 668.111 1331.288 486.319 1258.991 523.884"/></clipPath><clipPath id="clip-path-4"><path class="cls-1" d="M920.868,826.6875c-27.6839-21.8619-16.889,19.07-16.889,19.07s-17.4911-4.8329-30.6127.3394-15.5072,71.3777,0,80.6879,63.8754,12.5981,63.8754,12.5981l336.5809,125.855,12.8294-118.2894s-340.1921-68.9478-343.7707-74.12S920.868,826.6875,920.868,826.6875Z"/></clipPath><clipPath id="clip-path-5"><path class="cls-1" d="M771.9187,2254.42s-57.7648,167.2172-83.39,187.6409l121.7805,46.8465,91.9124-224.2865Z"/></clipPath><clipPath id="clip-path-6"><path class="cls-2" d="M739.8,2293.5011c14.1952,84.8,201.1713,109.6792,201.1713,109.6792l209.9978-551.9291,345.6618-361.3043c90.0668-96.9226,86.9384-250.4763-6.997-343.4365l-35.5109-35.1423L1263.5325,1193.9,993.27,1774.6892S735.75,2269.3076,739.8,2293.5011Z"/></clipPath><clipPath id="clip-path-7"><path class="cls-1" d="M1242.5694,1211.01c52.5291,7.469,276.86-92.2247,276.86-92.2247L1459.3381,989.141a209.6238,209.6238,0,0,1-19.4265-86.23l-2.0574-224.3836-71.7285-66.9229-111.1965,61.9762-86.8305,92.1449c-80.8019,94.7672,41.2582,170.2026,41.2582,170.2026Z"/></clipPath><clipPath id="clip-path-8"><path class="cls-1" d="M1444.06,1143.76c-20.737,8.6117-23.7621-11.9494-30.349-8.9488-31.2348,14.2287-30.3422,89.0265,43.5626,81.1067,48.8987-5.24,268.9771-235.5777,268.9771-235.5777a66.2551,66.2551,0,0,0-3.7192-104.7946L1457.2734,692.6939l-81.2078,63.8577-19.66,57.48,243.8812,115.9591S1468.5148,1133.6042,1444.06,1143.76Z"/></clipPath><clipPath id="clip-path-9"><path class="cls-1" d="M1325.9407,796.8545,1515.4,940.2367s77.8617-61.11,104.0482-139.4105L1418.6522,660.6119"/></clipPath><clipPath id="clip-path-10"><path class="cls-1" d="M2121.0359,1427.451c.106-.8234.1129-1.61.1927-2.4246.1162-1.1884.22-2.3722.28-3.54.0308-.5816.1231-1.1838.14-1.7608.0268-.9466-.0547-1.8544-.0644-2.7873-.0114-1.0948-.0136-2.192-.0746-3.2663-.0251-.4288.0023-.8781-.03-1.3046-.0827-1.0926-.2844-2.135-.4189-3.2047-.1254-.9968-.22-2.0049-.39-2.9834-.049-.2829-.06-.5817-.1129-.8645-.2313-1.2317-.59-2.4109-.8919-3.6153-.2262-.9055-.404-1.8292-.67-2.7165-.05-.1665-.0741-.3421-.1254-.5087a60.1638,60.1638,0,0,0-2.2728-6.1561l-42.7953-98.939a60.3618,60.3618,0,0,1,2.3976,6.6648c.265.885.4428,1.8065.6685,2.71.371,1.48.742,2.9629,1.0065,4.4888.1687.9762.2644,1.9821.3887,2.9766.1875,1.4894.3624,2.9834.449,4.5139.0616,1.0743.0639,2.1714.0753,3.2663.0159,1.5008.004,3.0108-.0753,4.5481-.06,1.1678-.1652,2.3493-.2809,3.5377q-.2214,2.2683-.5773,4.5846-.2992,1.9365-.6965,3.9026c-.3,1.494-.6536,3.0017-1.0383,4.5208-.3819,1.51-.8007,3.029-1.2664,4.5641-.6753,2.2239-1.362,4.4546-2.2157,6.7309-.04.1072-.0719.21-.1117.3171q-1.7457,4.6154-3.9825,9.3654c-1.1854,2.5113-2.47,5.018-3.8332,7.5224-.6474,1.1884-1.3661,2.37-2.0523,3.556-.7129,1.2317-1.3945,2.4656-2.1491,3.695-.7289,1.1861-1.5273,2.3676-2.2944,3.5514-.7933,1.2249-1.557,2.4543-2.3913,3.6746q-2.3551,3.4521-4.91,6.8769c-.1436.1916-.3026.3809-.4474.5748q-2.3688,3.1442-4.8995,6.2634c-.6246.7686-1.29,1.5282-1.93,2.2945-1.2492,1.4963-2.5,2.9926-3.8058,4.4775-.7853.8941-1.6083,1.7814-2.4135,2.6709-1.2367,1.3663-2.4734,2.7348-3.7557,4.092-.7016.7435-1.4242,1.48-2.14,2.2216q-2.038,2.1075-4.1427,4.1991-1.172,1.16-2.3651,2.3174-2.1722,2.1008-4.419,4.1832c-.7557.7025-1.5028,1.4073-2.27,2.1053q-2.8023,2.5522-5.7093,5.0659c-.4121.3581-.8087.7207-1.2236,1.0766q-3.7059,3.1818-7.5649,6.3067c-.5.4037-1.0236.7983-1.5274,1.202-2.0841,1.67-4.1848,3.33-6.3311,4.97-.9574.73-1.9445,1.4461-2.9133,2.1714-1.73,1.2956-3.4593,2.5911-5.2261,3.8662-1.117.8074-2.26,1.5989-3.39,2.3972-1.6362,1.1542-3.2718,2.3083-4.9371,3.4465-1.6345,1.1176-3.3009,2.2147-4.9627,3.3141-1.39.9192-2.77,1.8476-4.178,2.7554-1.71,1.1016-3.4519,2.1828-5.1879,3.2662-1.3392.8348-2.6671,1.6788-4.0212,2.5022q-6.6883,4.0714-13.6219,7.94-1.5815.8862-3.1755,1.7609-5.4753,3.0108-11.0687,5.8938c-.5665.292-1.1227.5954-1.6915.885q-5.9608,3.0348-12.0523,5.9007c-1.1649.552-2.3418,1.0812-3.5123,1.624q-4.2238,1.95-8.4945,3.8274-2.0712.91-4.1523,1.7974-4.0237,1.7141-8.0881,3.3506c-1.3.5246-2.5942,1.0606-3.8993,1.5738q-4.8744,1.9194-9.7961,3.7179c-.6708.2464-1.3358.5109-2.0078.755q-5.88,2.1248-11.8107,4.0806c-1.1079.3672-2.2226.7048-3.3333,1.0629-2.8832.926-5.77,1.8361-8.6666,2.7028-1.4282.4266-2.8586.83-4.2891,1.2431q-4.011,1.1531-8.0334,2.2285-2.2158.5919-4.4338,1.1587-4.3881,1.1153-8.7828,2.1235c-1.2384.2874-2.4757.5885-3.7141.8622q-6.5089,1.44-13.0172,2.6572c-.7187.1323-1.4356.2418-2.1537.37q-6.0584,1.0881-12.1008,1.9571c-2.2728.3307-4.5472.68-6.8155.9625-4.6578.5839-9.221,1.0469-13.709,1.421-.917.0776-1.8049.1186-2.7145.187q-4.9863.37-9.83.5748c-1.3193.0548-2.6239.0936-3.9255.1278-2.7378.0752-5.4272.098-8.0892.0889-1.5057-.0023-3.0194,0-4.5012-.03-2.7526-.0571-5.4426-.1779-8.1086-.3216-1.18-.0639-2.3884-.1-3.5505-.18-3.7773-.2646-7.4783-.6045-11.07-1.0447-.3944-.0479-.7625-.1209-1.1546-.1711q-4.78-.6157-9.3362-1.4369c-1.1421-.2076-2.2358-.4539-3.3556-.68-2.2352-.4517-4.4464-.9215-6.5915-1.4484-1.2151-.2988-2.3993-.62-3.5859-.942q-2.9723-.8075-5.8244-1.7084c-1.1524-.365-2.2979-.7368-3.4194-1.1245-1.8984-.6592-3.7363-1.364-5.5452-2.0916-.9871-.3969-1.9947-.7778-2.9555-1.1929-2.0927-.9078-4.0988-1.8772-6.0616-2.8762-.5756-.2943-1.19-.5589-1.7536-.86q-3.7245-1.9845-7.1129-4.1946c-.5882-.3832-1.1193-.8006-1.6921-1.193-1.6328-1.12-3.2285-2.2626-4.7342-3.46-.7374-.5862-1.4259-1.202-2.1326-1.8087-1.2549-1.0744-2.4762-2.1692-3.6325-3.3-.6908-.6752-1.3576-1.3663-2.013-2.062-1.0805-1.1473-2.1052-2.3265-3.0883-3.5285-.5818-.7117-1.17-1.421-1.7171-2.1532-.9916-1.3229-1.8978-2.6869-2.7731-4.0691-.4149-.6569-.8663-1.2933-1.255-1.9616a60.3066,60.3066,0,0,1-3.2581-6.38l42.7958,98.9389a60.5737,60.5737,0,0,0,3.2582,6.382c.3887.666.84,1.3047,1.2549,1.9593.876,1.3822,1.7821,2.7485,2.7726,4.0714.5483.73,1.1353,1.4393,1.7177,2.1509.9831,1.202,2.0072,2.3813,3.0889,3.5286.6554.6979,1.321,1.3868,2.0118,2.0619,1.1569,1.1336,2.3776,2.2262,3.6325,3.3005.7073.6067,1.3952,1.2226,2.1338,1.8087,1.5028,1.1952,3.0951,2.3357,4.7245,3.4533.5767.3946,1.1113.8166,1.704,1.2021q3.387,2.2068,7.1073,4.1923c.57.3056,1.1928.5725,1.7752.869,1.9559.9945,3.954,1.9616,6.04,2.8648.962.4174,1.9707.7984,2.9606,1.1975,1.81.7253,3.6474,1.43,5.5469,2.0916,1.12.3878,2.2631.7573,3.4138,1.1222q2.8465.9033,5.8187,1.7061c1.1911.3239,2.38.6478,3.5995.9466,2.1417.5246,4.3467.9945,6.5767,1.4438,1.1233.2281,2.2192.4745,3.3647.682q4.5719.828,9.3784,1.4438c.3761.0479.7289.1164,1.1067.1643,3.5939.4424,7.296.78,11.0756,1.0446,1.1586.0821,2.3628.1163,3.5391.18,2.6728.1437,5.3685.2669,8.1285.3216,1.4744.0319,2.9778.0274,4.4766.0319,1.4864.0046,2.9316.05,4.4419.03,1.2031-.016,2.4534-.0867,3.6713-.1209,1.2983-.0342,2.5976-.073,3.9135-.1277q4.8394-.1984,9.8326-.5748c.91-.0684,1.7975-.11,2.7156-.187.2673-.0206.5238-.03.7916-.0525q6.3447-.544,12.9175-1.3663,2.385-.3011,4.7746-.6341c.6788-.0935,1.361-.23,2.04-.3284q6.0421-.8793,12.1-1.9593c.5414-.0958,1.0806-.162,1.622-.2623.1778-.032.3556-.0753.5328-.1072q6.503-1.2078,13.007-2.6527c.3248-.073.6508-.1278.9757-.2007.9243-.2053,1.8465-.4562,2.7708-.6706,2.9214-.6752,5.84-1.3754,8.7572-2.119.6788-.1711,1.3592-.317,2.0374-.4927.8024-.2075,1.6-.4516,2.4022-.666q4.0143-1.0709,8.0174-2.2239c.9152-.2646,1.8362-.4949,2.7509-.7641.5215-.1528,1.0378-.3307,1.5587-.4858,2.8923-.8645,5.7743-1.7723,8.6529-2.6983.8964-.29,1.7992-.5452,2.6933-.8417.216-.0707.43-.15.6457-.2212q5.931-1.96,11.8142-4.0851c.0855-.03.1715-.0571.2564-.0867.5779-.21,1.1467-.4425,1.7234-.6546q4.9343-1.8133,9.8252-3.7293c.3944-.1551.7956-.2965,1.1894-.4516.9056-.36,1.794-.7527,2.6968-1.1177q4.0665-1.6422,8.0977-3.3552c.6868-.2942,1.3861-.5634,2.0711-.8576.7-.3034,1.3826-.6318,2.0807-.9375q4.2735-1.8749,8.4944-3.83c.8287-.3832,1.6744-.739,2.5-1.1268.3414-.1619.672-.3353,1.0122-.4949q6.0883-2.8775,12.0615-5.9076c.1236-.0616.2519-.1186.375-.1825.4342-.2212.852-.4607,1.2851-.6842q5.61-2.8842,11.0915-5.9076c.5853-.3216,1.1917-.62,1.7752-.9443.4753-.2646.9261-.55,1.4-.8165q6.921-3.88,13.6293-7.9422c.1886-.1163.3887-.2212.5773-.3375,1.16-.7071,2.2842-1.4439,3.4326-2.16,1.7348-1.0834,3.4747-2.16,5.1838-3.2617,1.4185-.9146,2.8079-1.8475,4.2088-2.7758,1.6538-1.0949,3.3117-2.1874,4.9388-3.2982.3567-.2441.7272-.479,1.0816-.7231,1.3051-.8986,2.57-1.8178,3.8572-2.7257,1.1307-.7983,2.2733-1.59,3.39-2.3949,1.7678-1.2773,3.4963-2.5729,5.226-3.8684.97-.7254,1.957-1.4416,2.9145-2.1714,2.1189-1.62,4.1916-3.2595,6.25-4.9063.5329-.4265,1.0851-.8439,1.614-1.27q3.7468-3.0313,7.3535-6.122c.07-.0593.1356-.1186.2051-.18.4149-.3558.8116-.7185,1.2236-1.0766q2.9023-2.5148,5.7088-5.0659c.7682-.6979,1.5148-1.4027,2.2705-2.1053q2.2431-2.0835,4.4213-4.1854,1.19-1.153,2.3571-2.3106,2.115-2.0938,4.1546-4.21c.7136-.7368,1.4339-1.4735,2.1338-2.2148.306-.3239.6291-.6432.9335-.9671.9637-1.031,1.8727-2.0756,2.8107-3.1112.81-.8964,1.6379-1.7882,2.429-2.6869,1.2959-1.4757,2.5372-2.9606,3.7784-4.4477.6486-.7756,1.3234-1.5465,1.9565-2.3243q2.5005-3.0758,4.8311-6.179c.1664-.2189.35-.44.5152-.6614q2.4654-3.2948,4.7416-6.6147c.0581-.0866.1083-.171.1659-.2554.8337-1.2226,1.5974-2.45,2.3918-3.6768.7666-1.1838,1.565-2.3631,2.2933-3.5514.7546-1.2294,1.4362-2.4634,2.15-3.6951.6429-1.1108,1.329-2.217,1.9377-3.33.041-.0753.0741-.1506.1146-.2258q2.0455-3.7533,3.8326-7.5225.9719-2.0562,1.85-4.0919,1.1481-2.6584,2.1331-5.2735c.0405-.1072.0718-.21.1112-.3147.5756-1.5351,1.1227-3.0633,1.6168-4.5733.2393-.7321.379-1.4324.5989-2.16.4662-1.535.8851-3.0541,1.2675-4.5641.1955-.7732.4753-1.5715.6491-2.34.1676-.7367.2411-1.4461.3887-2.1782q.3958-1.9708.697-3.905C2120.7618,1428.8948,2120.9453,1428.1581,2121.0359,1427.451Z"/></clipPath><clipPath id="clip-path-11"><path class="cls-2" d="M954.5643,1557.1161c-6.2573-24.8785-41.2084-37.3159-103.88-36.9538-9.13.0525-18.7181.4044-28.6353.9933a183.802,183.802,0,0,1,15.2213,25.5212c57.0845-1.8349,89.4168,6.605,91.9846,16.8135s-21.9193,32.9562-73.07,58.3778c-40.501,20.1287-97.7079,41.9305-171.5162,60.5176s-134.5133,26.4788-179.7129,27.9322c-57.0834,1.8362-89.4155-6.6049-91.9849-16.8121-2.5665-10.2085,21.9195-32.9575,73.07-58.3792a183.9519,183.9519,0,0,1,1.3343-29.6894c-9.0137,4.1774-17.6272,8.4093-25.6928,12.6885-55.3769,29.3656-80.2795,56.8747-74.0209,81.7531s41.2095,37.3156,103.881,36.9535c9.13-.052,18.718-.4029,28.6339-.9933,50.1775-2.9823,109.6412-12.7114,170.8625-28.1287s118.2-35.0129,163.8125-56.1515c9.0137-4.1784,17.6259-8.41,25.6928-12.6885C935.92,1609.5035,960.8227,1581.9947,954.5643,1557.1161Z"/></clipPath><clipPath id="clip-path-12"><path class="cls-2" d="M3318.6965,1964.8932c-4.9811-19.8043-32.8037-29.7051-82.6931-29.4169-7.2676.0418-14.9.3219-22.795.7908a146.3079,146.3079,0,0,1,12.1168,20.316c45.4419-1.4606,71.18,5.2579,73.224,13.3843s-17.4488,26.2346-58.1673,46.4714c-32.2407,16.0234-77.78,33.3785-136.5347,48.1747s-107.0787,21.0784-143.06,22.2353c-45.4409,1.4618-71.1787-5.2578-73.2241-13.3832-2.0431-8.1264,17.4489-26.2356,58.1675-46.4725a146.4215,146.4215,0,0,1,1.0621-23.634c-7.1753,3.3253-14.0321,6.6941-20.4526,10.1006-44.0825,23.3763-63.9062,45.2748-58.9241,65.0791s32.8047,29.705,82.694,29.4167c7.2676-.0414,14.9005-.3207,22.794-.7907,39.9435-2.3741,87.2794-10.1189,136.0143-22.3917s94.0925-27.8719,130.4022-44.6991c7.1753-3.3262,14.031-6.6952,20.4526-10.1007C3303.8549,2006.596,3323.6785,1984.6977,3318.6965,1964.8932Z"/></clipPath><clipPath id="clip-path-13"><polygon class="cls-1" points="3345.363 898.046 3489.251 1007.416 3298.19 1120.536 3154.302 1011.166 3345.363 898.046"/></clipPath><clipPath id="clip-path-14"><path class="cls-1" d="M3460.9969,1271.4591l-56.0531,26.2187s-78.43-25.4856-91.0333-16.68L3125.9433,1412.33l-124.5871-92.9709,323.8746-187.334Z"/></clipPath><clipPath id="clip-path-15"><path class="cls-1" d="M1793.6523,2508.0026l26.1585-60.3012s77.6052-33.6875,81.5669-49.6185l59.0884-237.6048,157.8929-10.7213-127.5044,377.6415Z"/></clipPath><clipPath id="clip-path-16"><path class="cls-3" d="M2144.284,2391.797s-109.8881,6.6411-212.4176-129.6841l140.7071-679.8987a83.0785,83.0785,0,0,1,14.0975-32.4373l437.9337-596.6163,95.7826,112.2367c69.2243,81.1161,74.7581,201.6274,12.3051,267.9721l-370.0935,393.155Z"/></clipPath><clipPath id="clip-path-17"><path class="cls-1" d="M1562.4739,837.0925c-23.2529,2.5486,4.3309,45.84,4.3309,45.84s-68.43-27.0416-76.8444-33.9936c-9.89-8.1715-10.21,8.48-3.4273,12.54,0,0-17.1013-6-20.9007-10.9089-1.0253-1.3248-9.1943,14.2895,9.2478,26.7648,0,0-2.3186,16.05,15.7762,25.3622,0,0-11.4627,2.1039,2.7451,15.6965s77.9961,41.2385,93.1582,40.3383l116.0717,49.4268-1.9857-82.0161s-94.6368-35.6846-102.1637-40.5419C1574.0422,869.83,1562.4739,837.0925,1562.4739,837.0925Z"/></clipPath><clipPath id="clip-path-18"><polygon class="cls-1" points="2091.223 686.85 2207.941 879.665 2313.163 770.967 2215.49 672.773 2091.223 686.85"/></clipPath><clipPath id="clip-path-19"><path class="cls-1" d="M2890.5986,1220.8288c26.5356.5931,23.1388-43.4553,23.1388-43.4553s45.3284,74.1713,46.156,75.3205c5.0338,6.9906,15.6991-3.8308,15.115-9.522,0,0,11.1134,22.5718,12.8032,21.886s16.9291-7.4195,7.3186-29.2761c0,0,16.984-8.8691,5.8215-27.699,0,0,14.6587,1.0376,9.6112-19.3566s-36.5507-75.43-65.3031-90.5792l-75.5385-109.5082-53.7408,72.3808s67.9419,86.252,70.1467,95.5657C2893.5626,1187.9952,2890.5986,1220.8288,2890.5986,1220.8288Z"/></clipPath><clipPath id="clip-path-20"><path class="cls-2" d="M2810.9523,955.92c44.67-5.9145,8.5055,348.2978-337.4887,320.39,0,0-86.301-84.0106-166.4824-184.518-70.8807-88.8491-136.9792-190.59-134.45-258.5826l104.9349-108.8882C2473.9111,678.3226,2530.7507,993.02,2810.9523,955.92Z"/></clipPath><clipPath id="clip-path-21"><path class="cls-1" d="M2049.4265,539.79,2258.819,666.9313a12.9535,12.9535,0,0,0,17.63-4.0677l8.8215-13.9746a12.4207,12.4207,0,0,0-4.1474-17.2908l-48.03-29.163-161.3629-97.9779a12.9535,12.9535,0,0,0-17.63,4.0677L2045.279,522.5A12.4208,12.4208,0,0,0,2049.4265,539.79Z"/></clipPath></defs><title>Title</title><path class="cls-4" d="M1043.8648,1922.9641c98.39,52.951,225.0595,85.69,365.49,91.6914.06.4725.0938.9448.1554,1.4174,42.9279,329.4529,515.2449,539.5187,1054.9489,469.195,514.0257-66.9778,903.7753-364.1434,902.613-676.8993,217.607-102.3155,353.6091-267.79,331.797-435.1889-13.8564-106.3415-89.0965-195.6024-202.761-257.6443,59.107-91.2273,86.4368-189.6521,73.5476-288.5711-42.9278-329.4529-515.2442-539.519-1054.9486-469.1953C2172.4931,402.36,1885.4142,548.9929,1732.04,734.3112c-131.93-20.3763-276.8539-22.5421-427.0138-2.9762-539.704,70.3237-942.4205,394.4066-899.4927,723.86C436.83,1695.3851,696.3856,1872.0791,1043.8648,1922.9641Z"/><circle class="cls-4" cx="1646" cy="573" r="64"/><circle class="cls-5" cx="828.5" cy="2110.5" r="79.5"/><circle class="cls-6" cx="3515" cy="2111" r="162"/><path class="cls-7" d="M1898.1873,2183.8624s141.58,90.5359,156.756,119.4248l84.4256-127.417-203.4945-108.1311Z"/><g class="cls-8"><path class="cls-9" d="M1877.8254,2185.5132c126.1119,71.2319,210.9625-70.7079,210.9625-70.7079l-152.9135-47.0662-37.6871,59.251"/></g><path class="cls-10" d="M1898.1873,2183.8624s141.58,90.5359,156.756,119.4248l84.4256-127.417-203.4945-108.1311Z"/><path class="cls-3" d="M2040.0681,2530.7668l-.2353-.2358c-29.9661-10.6081-32.5971-52.555-27.1552-80.1,11.157-56.4736,21.5337-155.9759-8.74-193.6857,0,0,122.0939-28.58,97.7018-105.251l76.4114,39.8963c15.101,7.7174,21.7358,25.3857,14.6024,38.8858Z"/><path class="cls-5" d="M1929.3837,2207.8c101.6837-12.35,101.6837-187.8891,101.6837-187.8891l-377.8409-205.4329-109.2951-527.804-268.0594,58.014,189.42,534.9785a146.2836,146.2836,0,0,0,63.0861,82.162S1870.8508,2214.91,1929.3837,2207.8Z"/><g class="cls-11"><ellipse class="cls-12" cx="1464.4511" cy="1936.4364" rx="67.4024" ry="76.0602"/></g><path class="cls-10" d="M1929.3837,2207.8c101.6837-12.35,101.6837-187.8891,101.6837-187.8891l-377.8409-205.4329-109.2951-527.804-268.0594,58.014,189.42,534.9785a146.2836,146.2836,0,0,0,63.0861,82.162S1870.8508,2214.91,1929.3837,2207.8Z"/><polygon class="cls-7" points="1258.991 523.884 1267.178 760.554 1371.882 668.111 1331.288 486.319 1258.991 523.884"/><g class="cls-13"><path class="cls-9" d="M1237.6717,702.3158c28.8924-84.1067,78.749-129.0918,78.749-129.0918s-73.1871,3.71-74.114,8.3476S1237.6717,702.3158,1237.6717,702.3158Z"/></g><polygon class="cls-10" points="1258.991 523.884 1267.178 760.554 1371.882 668.111 1331.288 486.319 1258.991 523.884"/><path class="cls-7" d="M920.868,826.6875c-27.6839-21.8619-16.889,19.07-16.889,19.07s-17.4911-4.8329-30.6127.3394-15.5072,71.3777,0,80.6879,63.8754,12.5981,63.8754,12.5981l336.5809,125.855,12.8294-118.2894s-340.1921-68.9478-343.7707-74.12S920.868,826.6875,920.868,826.6875Z"/><g class="cls-14"><path class="cls-9" d="M1140.4564,885.482c72.0315,76.3787,30.8565,133.658,61.6726,189.7364s92.97,11.8585,92.97,11.8585L1242.5674,942.75Z"/></g><path class="cls-10" d="M920.868,826.6875c-27.6839-21.8619-16.889,19.07-16.889,19.07s-17.4911-4.8329-30.6127.3394-15.5072,71.3777,0,80.6879,63.8754,12.5981,63.8754,12.5981l336.5809,125.855,12.8294-118.2894s-340.1921-68.9478-343.7707-74.12S920.868,826.6875,920.868,826.6875Z"/><path class="cls-7" d="M771.9187,2254.42s-57.7648,167.2172-83.39,187.6409l121.7805,46.8465,91.9124-224.2865Z"/><g class="cls-15"><path class="cls-9" d="M771.9187,2254.42c-84.0168,30.972,17.9089,130.2665,98.5013,209.7781s31.8012-95.2636,31.8012-95.2636l-106.8465-85.98"/></g><path class="cls-10" d="M771.9187,2254.42s-57.7648,167.2172-83.39,187.6409l121.7805,46.8465,91.9124-224.2865Z"/><path class="cls-3" d="M459.1231,2454.6766l.1815-.2823c-13.8431-30.7313,43.8872-42.8085,70.7839-42.4353,55.1443.765,160.0354-5.3365,190.3354-43.0241,0,0,39.3119,121.2968,107.3423,82.2989l-24.7255,83.6127c-5.2818,17.8611-21.9343,28.3991-36.902,23.3522Z"/><path class="cls-5" d="M739.8,2293.5011c14.1952,84.8,201.1713,109.6792,201.1713,109.6792l209.9978-551.9291,345.6618-361.3043c90.0668-96.9226,86.9384-250.4763-6.997-343.4365l-35.5109-35.1423L1263.5325,1193.9,993.27,1774.6892S735.75,2269.3076,739.8,2293.5011Z"/><g class="cls-16"><path class="cls-12" d="M957.1887,1480.9814c421.36,46.5138,240.3517-334.7468,569.36-369.641s10.9082-137.2465,10.9082-137.2465l-327.34,68.2349-51.6325,43.8653"/><ellipse class="cls-12" cx="976.4041" cy="1731.9447" rx="67.4024" ry="76.0602"/></g><path class="cls-10" d="M739.8,2293.5011c14.1952,84.8,201.1713,109.6792,201.1713,109.6792l209.9978-551.9291,345.6618-361.3043c90.0668-96.9226,86.9384-250.4763-6.997-343.4365l-35.5109-35.1423L1263.5325,1193.9,993.27,1774.6892S735.75,2269.3076,739.8,2293.5011Z"/><path class="cls-7" d="M1242.5694,1211.01c52.5291,7.469,276.86-92.2247,276.86-92.2247L1459.3381,989.141a209.6238,209.6238,0,0,1-19.4265-86.23l-2.0574-224.3836-71.7285-66.9229-111.1965,61.9762-86.8305,92.1449c-80.8019,94.7672,41.2582,170.2026,41.2582,170.2026Z"/><g class="cls-17"><path class="cls-9" d="M1409.6357,731.5184l-83.695,65.3361s87.5232,34.5784,99.1449,178.7523-149.6732,255.3548-166.0947,255.6051,66.7754,24.5751,66.7754,24.5751l295.0958-1.6384,11.4619-291.6244Z"/><line class="cls-1" x1="1202.129" y1="703.5353" x2="1407.2648" y2="633.4889"/></g><path class="cls-10" d="M1242.5694,1211.01c52.5291,7.469,276.86-92.2247,276.86-92.2247L1459.3381,989.141a209.6238,209.6238,0,0,1-19.4265-86.23l-2.0574-224.3836-71.7285-66.9229-111.1965,61.9762-86.8305,92.1449c-80.8019,94.7672,41.2582,170.2026,41.2582,170.2026Z"/><path class="cls-7" d="M1444.06,1143.76c-20.737,8.6117-23.7621-11.9494-30.349-8.9488-31.2348,14.2287-30.3422,89.0265,43.5626,81.1067,48.8987-5.24,268.9771-235.5777,268.9771-235.5777a66.2551,66.2551,0,0,0-3.7192-104.7946L1457.2734,692.6939l-81.2078,63.8577-19.66,57.48,243.8812,115.9591S1468.5148,1133.6042,1444.06,1143.76Z"/><g class="cls-18"><path class="cls-9" d="M1539.6534,953.0726c60.6335-23.0818,79.7949-152.2464,79.7949-152.2464l-91.6864,68.1707V928.8"/></g><path class="cls-10" d="M1444.06,1143.76c-20.737,8.6117-23.7621-11.9494-30.349-8.9488-31.2348,14.2287-30.3422,89.0265,43.5626,81.1067,48.8987-5.24,268.9771-235.5777,268.9771-235.5777a66.2551,66.2551,0,0,0-3.7192-104.7946L1457.2734,692.6939l-81.2078,63.8577-19.66,57.48,243.8812,115.9591S1468.5148,1133.6042,1444.06,1143.76Z"/><path class="cls-7" d="M1325.9407,796.8545,1515.4,940.2367s77.8617-61.11,104.0482-139.4105L1418.6522,660.6119"/><g class="cls-19"><path class="cls-1" d="M1602.966,740.4689c-8.5918,95.5179-126.0125,187.2151-126.0125,187.2151l42.4754,39.1623,147.4979-178.6184"/></g><path class="cls-10" d="M1325.9407,796.8545,1515.4,940.2367s77.8617-61.11,104.0482-139.4105L1418.6522,660.6119"/><path class="cls-1" d="M1358.5609,534.8347l31.3149-87.3035-144.4671-86.3448S1198.1615,416.03,1192.2536,515.24c-4.1144,69.0946,69.7925,74.8022,115.0406,71.9006"/><path class="cls-3" d="M1302.0187,316.6819c-172.1972.0415-95.4865,181.3421,32.7448,169.9645l6.9351,66.93s134.4413,88.0009,162.9006-14.37C1530.6046,445.6628,1423.233,316.6527,1302.0187,316.6819Z"/><path class="cls-1" d="M1295.5341,492.4253a58.3455,58.3455,0,0,1,11.0436-16.053c17.334-17.9983,42.24-22.1231,55.63-9.213s10.1922,37.9661-7.1418,55.9644a57.0712,57.0712,0,0,1-19.8566,13.5428"/><ellipse class="cls-7" cx="1325.941" cy="497.6125" rx="11.7196" ry="11.7262"/><rect class="cls-7" x="1296.074" y="516.5478" width="40.6981" height="9.2344" transform="translate(311.2882 1518.3042) rotate(-66.3696)"/><path class="cls-20" d="M1222.8395,453.4505h14.0557a8.3141,8.3141,0,0,1,8.3141,8.3141v0a8.3142,8.3142,0,0,1-8.3142,8.3142h-14.0557a8.3141,8.3141,0,0,1-8.3141-8.3141v0a8.3141,8.3141,0,0,1,8.3141-8.3141Z" transform="translate(413.0659 1490.6649) rotate(-72.1345)"/><path class="cls-20" d="M1236.0555,418.4038c24.2444,6.6436,26.3727,18.2729,10.9464,15.1088S1220.2156,414.0633,1236.0555,418.4038Z"/><path class="cls-1" d="M1211.7776,454.95c-35.1371,9.2882-35.3645,38.1-9.6486,39.38"/><g class="cls-21"><ellipse class="cls-9" cx="1269.3894" cy="503.2925" rx="27.9209" ry="27.9367"/></g><path class="cls-1" d="M1234.389,505.7812c-14.8609,12.4431-30.3837,5.6884-30.3837,5.6884"/><path class="cls-10" d="M1303.2733,352.5836c-10.0218,7.6663-11.06,23.0927-5.784,34.5581s15.3241,19.8939,25.0579,27.9232"/><path class="cls-22" d="M1423.9337,466.8906a218.116,218.116,0,0,1,4.9321,42.174"/><path class="cls-22" d="M1237.8457,921.6431a32.1625,32.1625,0,0,1-18.6239,21.6541"/><path class="cls-22" d="M1602.7372,931.3055a82.438,82.438,0,0,1,26.8486,9.2994"/><path class="cls-22" d="M949.9429,914.1875l-5.7717,7.6081"/><path class="cls-22" d="M1120.3217,1831.0415a43.947,43.947,0,0,0,20.3821,12.7118"/><path class="cls-22" d="M1652.5222,1815.957l-40.4,25.9875"/><path class="cls-22" d="M671.113,2453.6617l11.4952,7.5814"/><path class="cls-22" d="M2060.9868,2316.8082a135.814,135.814,0,0,0,60.8721-16.2213"/><path class="cls-22" d="M680.0653,2345.0744a86.6213,86.6213,0,0,0,3.411,44.7477,257.7278,257.7278,0,0,0-91.3474-21.3631"/><path class="cls-22" d="M1967.5789,2313.7826l49.4252-9.15q-22.6272,40.0412-47.0327,79.0409"/><path class="cls-3" d="M2370.07,2096.6972c42.3621-13.683,154.1352-65.8818,115.95-88.4854s-89.3036,70.67-270.4383,83.9671-234.79,134.9262-137.6482,99.9938,59.1232,16.263,202.2223-29.3876C2404.4778,2123.1244,2370.07,2096.6972,2370.07,2096.6972Z"/><path class="cls-1" d="M2039.23,2478.0058c151.3646-10.8791,562.8654-117.02,452.7365-275.294s-350.5919,224.1466-963.7577,53.17-883.4332,344.5674-533.6575,302.5144,184.6226,154.9277,695.5027,120.9221C2133.9,2649.7746,2039.23,2478.0058,2039.23,2478.0058Z"/><path class="cls-1" d="M3038.3057,2223.1927c144.3366-46.6207,525.1712-224.4731,395.0643-301.4883s-304.2762,240.786-921.44,286.0936-799.9781,459.722-468.9964,340.7,201.445,55.4114,689.0142-100.13C3155.5415,2313.2358,3038.3057,2223.1927,3038.3057,2223.1927Z"/><path class="cls-1" d="M1529.9619,2152.3448c53.8839,7.462,204.4236,11.0871,174.0116-30.5057s-135.3318,26.0088-342.2336-51.0145-329.3613,17.6115-203.9508,31.1743,56.8246,45.7021,238.5478,71.4779C1554.2157,2195.87,1529.9619,2152.3448,1529.9619,2152.3448Z"/><path class="cls-22" d="M1240.64,2387.3a100.3942,100.3942,0,0,1-45.7712,35.6268"/><path class="cls-22" d="M2799.3693,2263.6742l-34.5838,41.5321"/><path class="cls-1" d="M1925.3492,1245.5587c115.7944-14.5094,179.8927,36.7678,143.1948,114.5123s-160.3079,152.5422-276.1024,167.0516c-115.8312,14.5141-179.9293-36.7645-143.232-114.5076S1809.518,1260.0727,1925.3492,1245.5587Z"/><path class="cls-7" d="M2121.0359,1427.451c.106-.8234.1129-1.61.1927-2.4246.1162-1.1884.22-2.3722.28-3.54.0308-.5816.1231-1.1838.14-1.7608.0268-.9466-.0547-1.8544-.0644-2.7873-.0114-1.0948-.0136-2.192-.0746-3.2663-.0251-.4288.0023-.8781-.03-1.3046-.0827-1.0926-.2844-2.135-.4189-3.2047-.1254-.9968-.22-2.0049-.39-2.9834-.049-.2829-.06-.5817-.1129-.8645-.2313-1.2317-.59-2.4109-.8919-3.6153-.2262-.9055-.404-1.8292-.67-2.7165-.05-.1665-.0741-.3421-.1254-.5087a60.1638,60.1638,0,0,0-2.2728-6.1561l-42.7953-98.939a60.3618,60.3618,0,0,1,2.3976,6.6648c.265.885.4428,1.8065.6685,2.71.371,1.48.742,2.9629,1.0065,4.4888.1687.9762.2644,1.9821.3887,2.9766.1875,1.4894.3624,2.9834.449,4.5139.0616,1.0743.0639,2.1714.0753,3.2663.0159,1.5008.004,3.0108-.0753,4.5481-.06,1.1678-.1652,2.3493-.2809,3.5377q-.2214,2.2683-.5773,4.5846-.2992,1.9365-.6965,3.9026c-.3,1.494-.6536,3.0017-1.0383,4.5208-.3819,1.51-.8007,3.029-1.2664,4.5641-.6753,2.2239-1.362,4.4546-2.2157,6.7309-.04.1072-.0719.21-.1117.3171q-1.7457,4.6154-3.9825,9.3654c-1.1854,2.5113-2.47,5.018-3.8332,7.5224-.6474,1.1884-1.3661,2.37-2.0523,3.556-.7129,1.2317-1.3945,2.4656-2.1491,3.695-.7289,1.1861-1.5273,2.3676-2.2944,3.5514-.7933,1.2249-1.557,2.4543-2.3913,3.6746q-2.3551,3.4521-4.91,6.8769c-.1436.1916-.3026.3809-.4474.5748q-2.3688,3.1442-4.8995,6.2634c-.6246.7686-1.29,1.5282-1.93,2.2945-1.2492,1.4963-2.5,2.9926-3.8058,4.4775-.7853.8941-1.6083,1.7814-2.4135,2.6709-1.2367,1.3663-2.4734,2.7348-3.7557,4.092-.7016.7435-1.4242,1.48-2.14,2.2216q-2.038,2.1075-4.1427,4.1991-1.172,1.16-2.3651,2.3174-2.1722,2.1008-4.419,4.1832c-.7557.7025-1.5028,1.4073-2.27,2.1053q-2.8023,2.5522-5.7093,5.0659c-.4121.3581-.8087.7207-1.2236,1.0766q-3.7059,3.1818-7.5649,6.3067c-.5.4037-1.0236.7983-1.5274,1.202-2.0841,1.67-4.1848,3.33-6.3311,4.97-.9574.73-1.9445,1.4461-2.9133,2.1714-1.73,1.2956-3.4593,2.5911-5.2261,3.8662-1.117.8074-2.26,1.5989-3.39,2.3972-1.6362,1.1542-3.2718,2.3083-4.9371,3.4465-1.6345,1.1176-3.3009,2.2147-4.9627,3.3141-1.39.9192-2.77,1.8476-4.178,2.7554-1.71,1.1016-3.4519,2.1828-5.1879,3.2662-1.3392.8348-2.6671,1.6788-4.0212,2.5022q-6.6883,4.0714-13.6219,7.94-1.5815.8862-3.1755,1.7609-5.4753,3.0108-11.0687,5.8938c-.5665.292-1.1227.5954-1.6915.885q-5.9608,3.0348-12.0523,5.9007c-1.1649.552-2.3418,1.0812-3.5123,1.624q-4.2238,1.95-8.4945,3.8274-2.0712.91-4.1523,1.7974-4.0237,1.7141-8.0881,3.3506c-1.3.5246-2.5942,1.0606-3.8993,1.5738q-4.8744,1.9194-9.7961,3.7179c-.6708.2464-1.3358.5109-2.0078.755q-5.88,2.1248-11.8107,4.0806c-1.1079.3672-2.2226.7048-3.3333,1.0629-2.8832.926-5.77,1.8361-8.6666,2.7028-1.4282.4266-2.8586.83-4.2891,1.2431q-4.011,1.1531-8.0334,2.2285-2.2158.5919-4.4338,1.1587-4.3881,1.1153-8.7828,2.1235c-1.2384.2874-2.4757.5885-3.7141.8622q-6.5089,1.44-13.0172,2.6572c-.7187.1323-1.4356.2418-2.1537.37q-6.0584,1.0881-12.1008,1.9571c-2.2728.3307-4.5472.68-6.8155.9625-4.6578.5839-9.221,1.0469-13.709,1.421-.917.0776-1.8049.1186-2.7145.187q-4.9863.37-9.83.5748c-1.3193.0548-2.6239.0936-3.9255.1278-2.7378.0752-5.4272.098-8.0892.0889-1.5057-.0023-3.0194,0-4.5012-.03-2.7526-.0571-5.4426-.1779-8.1086-.3216-1.18-.0639-2.3884-.1-3.5505-.18-3.7773-.2646-7.4783-.6045-11.07-1.0447-.3944-.0479-.7625-.1209-1.1546-.1711q-4.78-.6157-9.3362-1.4369c-1.1421-.2076-2.2358-.4539-3.3556-.68-2.2352-.4517-4.4464-.9215-6.5915-1.4484-1.2151-.2988-2.3993-.62-3.5859-.942q-2.9723-.8075-5.8244-1.7084c-1.1524-.365-2.2979-.7368-3.4194-1.1245-1.8984-.6592-3.7363-1.364-5.5452-2.0916-.9871-.3969-1.9947-.7778-2.9555-1.1929-2.0927-.9078-4.0988-1.8772-6.0616-2.8762-.5756-.2943-1.19-.5589-1.7536-.86q-3.7245-1.9845-7.1129-4.1946c-.5882-.3832-1.1193-.8006-1.6921-1.193-1.6328-1.12-3.2285-2.2626-4.7342-3.46-.7374-.5862-1.4259-1.202-2.1326-1.8087-1.2549-1.0744-2.4762-2.1692-3.6325-3.3-.6908-.6752-1.3576-1.3663-2.013-2.062-1.0805-1.1473-2.1052-2.3265-3.0883-3.5285-.5818-.7117-1.17-1.421-1.7171-2.1532-.9916-1.3229-1.8978-2.6869-2.7731-4.0691-.4149-.6569-.8663-1.2933-1.255-1.9616a60.3066,60.3066,0,0,1-3.2581-6.38l42.7958,98.9389a60.5737,60.5737,0,0,0,3.2582,6.382c.3887.666.84,1.3047,1.2549,1.9593.876,1.3822,1.7821,2.7485,2.7726,4.0714.5483.73,1.1353,1.4393,1.7177,2.1509.9831,1.202,2.0072,2.3813,3.0889,3.5286.6554.6979,1.321,1.3868,2.0118,2.0619,1.1569,1.1336,2.3776,2.2262,3.6325,3.3005.7073.6067,1.3952,1.2226,2.1338,1.8087,1.5028,1.1952,3.0951,2.3357,4.7245,3.4533.5767.3946,1.1113.8166,1.704,1.2021q3.387,2.2068,7.1073,4.1923c.57.3056,1.1928.5725,1.7752.869,1.9559.9945,3.954,1.9616,6.04,2.8648.962.4174,1.9707.7984,2.9606,1.1975,1.81.7253,3.6474,1.43,5.5469,2.0916,1.12.3878,2.2631.7573,3.4138,1.1222q2.8465.9033,5.8187,1.7061c1.1911.3239,2.38.6478,3.5995.9466,2.1417.5246,4.3467.9945,6.5767,1.4438,1.1233.2281,2.2192.4745,3.3647.682q4.5719.828,9.3784,1.4438c.3761.0479.7289.1164,1.1067.1643,3.5939.4424,7.296.78,11.0756,1.0446,1.1586.0821,2.3628.1163,3.5391.18,2.6728.1437,5.3685.2669,8.1285.3216,1.4744.0319,2.9778.0274,4.4766.0319,1.4864.0046,2.9316.05,4.4419.03,1.2031-.016,2.4534-.0867,3.6713-.1209,1.2983-.0342,2.5976-.073,3.9135-.1277q4.8394-.1984,9.8326-.5748c.91-.0684,1.7975-.11,2.7156-.187.2673-.0206.5238-.03.7916-.0525q6.3447-.544,12.9175-1.3663,2.385-.3011,4.7746-.6341c.6788-.0935,1.361-.23,2.04-.3284q6.0421-.8793,12.1-1.9593c.5414-.0958,1.0806-.162,1.622-.2623.1778-.032.3556-.0753.5328-.1072q6.503-1.2078,13.007-2.6527c.3248-.073.6508-.1278.9757-.2007.9243-.2053,1.8465-.4562,2.7708-.6706,2.9214-.6752,5.84-1.3754,8.7572-2.119.6788-.1711,1.3592-.317,2.0374-.4927.8024-.2075,1.6-.4516,2.4022-.666q4.0143-1.0709,8.0174-2.2239c.9152-.2646,1.8362-.4949,2.7509-.7641.5215-.1528,1.0378-.3307,1.5587-.4858,2.8923-.8645,5.7743-1.7723,8.6529-2.6983.8964-.29,1.7992-.5452,2.6933-.8417.216-.0707.43-.15.6457-.2212q5.931-1.96,11.8142-4.0851c.0855-.03.1715-.0571.2564-.0867.5779-.21,1.1467-.4425,1.7234-.6546q4.9343-1.8133,9.8252-3.7293c.3944-.1551.7956-.2965,1.1894-.4516.9056-.36,1.794-.7527,2.6968-1.1177q4.0665-1.6422,8.0977-3.3552c.6868-.2942,1.3861-.5634,2.0711-.8576.7-.3034,1.3826-.6318,2.0807-.9375q4.2735-1.8749,8.4944-3.83c.8287-.3832,1.6744-.739,2.5-1.1268.3414-.1619.672-.3353,1.0122-.4949q6.0883-2.8775,12.0615-5.9076c.1236-.0616.2519-.1186.375-.1825.4342-.2212.852-.4607,1.2851-.6842q5.61-2.8842,11.0915-5.9076c.5853-.3216,1.1917-.62,1.7752-.9443.4753-.2646.9261-.55,1.4-.8165q6.921-3.88,13.6293-7.9422c.1886-.1163.3887-.2212.5773-.3375,1.16-.7071,2.2842-1.4439,3.4326-2.16,1.7348-1.0834,3.4747-2.16,5.1838-3.2617,1.4185-.9146,2.8079-1.8475,4.2088-2.7758,1.6538-1.0949,3.3117-2.1874,4.9388-3.2982.3567-.2441.7272-.479,1.0816-.7231,1.3051-.8986,2.57-1.8178,3.8572-2.7257,1.1307-.7983,2.2733-1.59,3.39-2.3949,1.7678-1.2773,3.4963-2.5729,5.226-3.8684.97-.7254,1.957-1.4416,2.9145-2.1714,2.1189-1.62,4.1916-3.2595,6.25-4.9063.5329-.4265,1.0851-.8439,1.614-1.27q3.7468-3.0313,7.3535-6.122c.07-.0593.1356-.1186.2051-.18.4149-.3558.8116-.7185,1.2236-1.0766q2.9023-2.5148,5.7088-5.0659c.7682-.6979,1.5148-1.4027,2.2705-2.1053q2.2431-2.0835,4.4213-4.1854,1.19-1.153,2.3571-2.3106,2.115-2.0938,4.1546-4.21c.7136-.7368,1.4339-1.4735,2.1338-2.2148.306-.3239.6291-.6432.9335-.9671.9637-1.031,1.8727-2.0756,2.8107-3.1112.81-.8964,1.6379-1.7882,2.429-2.6869,1.2959-1.4757,2.5372-2.9606,3.7784-4.4477.6486-.7756,1.3234-1.5465,1.9565-2.3243q2.5005-3.0758,4.8311-6.179c.1664-.2189.35-.44.5152-.6614q2.4654-3.2948,4.7416-6.6147c.0581-.0866.1083-.171.1659-.2554.8337-1.2226,1.5974-2.45,2.3918-3.6768.7666-1.1838,1.565-2.3631,2.2933-3.5514.7546-1.2294,1.4362-2.4634,2.15-3.6951.6429-1.1108,1.329-2.217,1.9377-3.33.041-.0753.0741-.1506.1146-.2258q2.0455-3.7533,3.8326-7.5225.9719-2.0562,1.85-4.0919,1.1481-2.6584,2.1331-5.2735c.0405-.1072.0718-.21.1112-.3147.5756-1.5351,1.1227-3.0633,1.6168-4.5733.2393-.7321.379-1.4324.5989-2.16.4662-1.535.8851-3.0541,1.2675-4.5641.1955-.7732.4753-1.5715.6491-2.34.1676-.7367.2411-1.4461.3887-2.1782q.3958-1.9708.697-3.905C2120.7618,1428.8948,2120.9453,1428.1581,2121.0359,1427.451Z"/><g class="cls-23"><circle class="cls-1" cx="1370.7128" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1054.1937" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1119.2728" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1184.352" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1249.4311" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1314.5103" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1347.0499" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="989.1145" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1021.654" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1086.7332" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1151.8125" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1216.8916" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1281.9708" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1379.5895" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1412.1291" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1444.6686" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1477.2083" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1509.7479" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1542.2874" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1574.8271" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1607.3666" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1639.9062" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1672.4457" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1704.9854" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1737.5249" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1770.0645" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1802.6042" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1835.1437" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1867.6833" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1900.2229" r="3.9544"/><circle class="cls-1" cx="1370.7128" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1403.751" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1436.7891" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1469.8267" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1502.8649" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1535.903" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1568.9411" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1601.9793" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1635.0174" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1668.0555" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1701.0932" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1734.1313" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1767.1694" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1800.2076" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1833.2457" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1899.322" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1866.2833" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1932.3596" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1965.3977" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="1998.4359" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2031.474" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2064.5121" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2097.5498" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2130.5879" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2163.626" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2196.6642" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2229.7023" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2262.7404" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2295.7786" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2328.8162" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2361.8543" cy="1932.7625" r="3.9544"/><circle class="cls-1" cx="2394.8925" cy="1932.7625" r="3.9544"/></g><path class="cls-10" d="M2121.0359,1427.451c.106-.8234.1129-1.61.1927-2.4246.1162-1.1884.22-2.3722.28-3.54.0308-.5816.1231-1.1838.14-1.7608.0268-.9466-.0547-1.8544-.0644-2.7873-.0114-1.0948-.0136-2.192-.0746-3.2663-.0251-.4288.0023-.8781-.03-1.3046-.0827-1.0926-.2844-2.135-.4189-3.2047-.1254-.9968-.22-2.0049-.39-2.9834-.049-.2829-.06-.5817-.1129-.8645-.2313-1.2317-.59-2.4109-.8919-3.6153-.2262-.9055-.404-1.8292-.67-2.7165-.05-.1665-.0741-.3421-.1254-.5087a60.1638,60.1638,0,0,0-2.2728-6.1561l-42.7953-98.939a60.3618,60.3618,0,0,1,2.3976,6.6648c.265.885.4428,1.8065.6685,2.71.371,1.48.742,2.9629,1.0065,4.4888.1687.9762.2644,1.9821.3887,2.9766.1875,1.4894.3624,2.9834.449,4.5139.0616,1.0743.0639,2.1714.0753,3.2663.0159,1.5008.004,3.0108-.0753,4.5481-.06,1.1678-.1652,2.3493-.2809,3.5377q-.2214,2.2683-.5773,4.5846-.2992,1.9365-.6965,3.9026c-.3,1.494-.6536,3.0017-1.0383,4.5208-.3819,1.51-.8007,3.029-1.2664,4.5641-.6753,2.2239-1.362,4.4546-2.2157,6.7309-.04.1072-.0719.21-.1117.3171q-1.7457,4.6154-3.9825,9.3654c-1.1854,2.5113-2.47,5.018-3.8332,7.5224-.6474,1.1884-1.3661,2.37-2.0523,3.556-.7129,1.2317-1.3945,2.4656-2.1491,3.695-.7289,1.1861-1.5273,2.3676-2.2944,3.5514-.7933,1.2249-1.557,2.4543-2.3913,3.6746q-2.3551,3.4521-4.91,6.8769c-.1436.1916-.3026.3809-.4474.5748q-2.3688,3.1442-4.8995,6.2634c-.6246.7686-1.29,1.5282-1.93,2.2945-1.2492,1.4963-2.5,2.9926-3.8058,4.4775-.7853.8941-1.6083,1.7814-2.4135,2.6709-1.2367,1.3663-2.4734,2.7348-3.7557,4.092-.7016.7435-1.4242,1.48-2.14,2.2216q-2.038,2.1075-4.1427,4.1991-1.172,1.16-2.3651,2.3174-2.1722,2.1008-4.419,4.1832c-.7557.7025-1.5028,1.4073-2.27,2.1053q-2.8023,2.5522-5.7093,5.0659c-.4121.3581-.8087.7207-1.2236,1.0766q-3.7059,3.1818-7.5649,6.3067c-.5.4037-1.0236.7983-1.5274,1.202-2.0841,1.67-4.1848,3.33-6.3311,4.97-.9574.73-1.9445,1.4461-2.9133,2.1714-1.73,1.2956-3.4593,2.5911-5.2261,3.8662-1.117.8074-2.26,1.5989-3.39,2.3972-1.6362,1.1542-3.2718,2.3083-4.9371,3.4465-1.6345,1.1176-3.3009,2.2147-4.9627,3.3141-1.39.9192-2.77,1.8476-4.178,2.7554-1.71,1.1016-3.4519,2.1828-5.1879,3.2662-1.3392.8348-2.6671,1.6788-4.0212,2.5022q-6.6883,4.0714-13.6219,7.94-1.5815.8862-3.1755,1.7609-5.4753,3.0108-11.0687,5.8938c-.5665.292-1.1227.5954-1.6915.885q-5.9608,3.0348-12.0523,5.9007c-1.1649.552-2.3418,1.0812-3.5123,1.624q-4.2238,1.95-8.4945,3.8274-2.0712.91-4.1523,1.7974-4.0237,1.7141-8.0881,3.3506c-1.3.5246-2.5942,1.0606-3.8993,1.5738q-4.8744,1.9194-9.7961,3.7179c-.6708.2464-1.3358.5109-2.0078.755q-5.88,2.1248-11.8107,4.0806c-1.1079.3672-2.2226.7048-3.3333,1.0629-2.8832.926-5.77,1.8361-8.6666,2.7028-1.4282.4266-2.8586.83-4.2891,1.2431q-4.011,1.1531-8.0334,2.2285-2.2158.5919-4.4338,1.1587-4.3881,1.1153-8.7828,2.1235c-1.2384.2874-2.4757.5885-3.7141.8622q-6.5089,1.44-13.0172,2.6572c-.7187.1323-1.4356.2418-2.1537.37q-6.0584,1.0881-12.1008,1.9571c-2.2728.3307-4.5472.68-6.8155.9625-4.6578.5839-9.221,1.0469-13.709,1.421-.917.0776-1.8049.1186-2.7145.187q-4.9863.37-9.83.5748c-1.3193.0548-2.6239.0936-3.9255.1278-2.7378.0752-5.4272.098-8.0892.0889-1.5057-.0023-3.0194,0-4.5012-.03-2.7526-.0571-5.4426-.1779-8.1086-.3216-1.18-.0639-2.3884-.1-3.5505-.18-3.7773-.2646-7.4783-.6045-11.07-1.0447-.3944-.0479-.7625-.1209-1.1546-.1711q-4.78-.6157-9.3362-1.4369c-1.1421-.2076-2.2358-.4539-3.3556-.68-2.2352-.4517-4.4464-.9215-6.5915-1.4484-1.2151-.2988-2.3993-.62-3.5859-.942q-2.9723-.8075-5.8244-1.7084c-1.1524-.365-2.2979-.7368-3.4194-1.1245-1.8984-.6592-3.7363-1.364-5.5452-2.0916-.9871-.3969-1.9947-.7778-2.9555-1.1929-2.0927-.9078-4.0988-1.8772-6.0616-2.8762-.5756-.2943-1.19-.5589-1.7536-.86q-3.7245-1.9845-7.1129-4.1946c-.5882-.3832-1.1193-.8006-1.6921-1.193-1.6328-1.12-3.2285-2.2626-4.7342-3.46-.7374-.5862-1.4259-1.202-2.1326-1.8087-1.2549-1.0744-2.4762-2.1692-3.6325-3.3-.6908-.6752-1.3576-1.3663-2.013-2.062-1.0805-1.1473-2.1052-2.3265-3.0883-3.5285-.5818-.7117-1.17-1.421-1.7171-2.1532-.9916-1.3229-1.8978-2.6869-2.7731-4.0691-.4149-.6569-.8663-1.2933-1.255-1.9616a60.3066,60.3066,0,0,1-3.2581-6.38l42.7958,98.9389a60.5737,60.5737,0,0,0,3.2582,6.382c.3887.666.84,1.3047,1.2549,1.9593.876,1.3822,1.7821,2.7485,2.7726,4.0714.5483.73,1.1353,1.4393,1.7177,2.1509.9831,1.202,2.0072,2.3813,3.0889,3.5286.6554.6979,1.321,1.3868,2.0118,2.0619,1.1569,1.1336,2.3776,2.2262,3.6325,3.3005.7073.6067,1.3952,1.2226,2.1338,1.8087,1.5028,1.1952,3.0951,2.3357,4.7245,3.4533.5767.3946,1.1113.8166,1.704,1.2021q3.387,2.2068,7.1073,4.1923c.57.3056,1.1928.5725,1.7752.869,1.9559.9945,3.954,1.9616,6.04,2.8648.962.4174,1.9707.7984,2.9606,1.1975,1.81.7253,3.6474,1.43,5.5469,2.0916,1.12.3878,2.2631.7573,3.4138,1.1222q2.8465.9033,5.8187,1.7061c1.1911.3239,2.38.6478,3.5995.9466,2.1417.5246,4.3467.9945,6.5767,1.4438,1.1233.2281,2.2192.4745,3.3647.682q4.5719.828,9.3784,1.4438c.3761.0479.7289.1164,1.1067.1643,3.5939.4424,7.296.78,11.0756,1.0446,1.1586.0821,2.3628.1163,3.5391.18,2.6728.1437,5.3685.2669,8.1285.3216,1.4744.0319,2.9778.0274,4.4766.0319,1.4864.0046,2.9316.05,4.4419.03,1.2031-.016,2.4534-.0867,3.6713-.1209,1.2983-.0342,2.5976-.073,3.9135-.1277q4.8394-.1984,9.8326-.5748c.91-.0684,1.7975-.11,2.7156-.187.2673-.0206.5238-.03.7916-.0525q6.3447-.544,12.9175-1.3663,2.385-.3011,4.7746-.6341c.6788-.0935,1.361-.23,2.04-.3284q6.0421-.8793,12.1-1.9593c.5414-.0958,1.0806-.162,1.622-.2623.1778-.032.3556-.0753.5328-.1072q6.503-1.2078,13.007-2.6527c.3248-.073.6508-.1278.9757-.2007.9243-.2053,1.8465-.4562,2.7708-.6706,2.9214-.6752,5.84-1.3754,8.7572-2.119.6788-.1711,1.3592-.317,2.0374-.4927.8024-.2075,1.6-.4516,2.4022-.666q4.0143-1.0709,8.0174-2.2239c.9152-.2646,1.8362-.4949,2.7509-.7641.5215-.1528,1.0378-.3307,1.5587-.4858,2.8923-.8645,5.7743-1.7723,8.6529-2.6983.8964-.29,1.7992-.5452,2.6933-.8417.216-.0707.43-.15.6457-.2212q5.931-1.96,11.8142-4.0851c.0855-.03.1715-.0571.2564-.0867.5779-.21,1.1467-.4425,1.7234-.6546q4.9343-1.8133,9.8252-3.7293c.3944-.1551.7956-.2965,1.1894-.4516.9056-.36,1.794-.7527,2.6968-1.1177q4.0665-1.6422,8.0977-3.3552c.6868-.2942,1.3861-.5634,2.0711-.8576.7-.3034,1.3826-.6318,2.0807-.9375q4.2735-1.8749,8.4944-3.83c.8287-.3832,1.6744-.739,2.5-1.1268.3414-.1619.672-.3353,1.0122-.4949q6.0883-2.8775,12.0615-5.9076c.1236-.0616.2519-.1186.375-.1825.4342-.2212.852-.4607,1.2851-.6842q5.61-2.8842,11.0915-5.9076c.5853-.3216,1.1917-.62,1.7752-.9443.4753-.2646.9261-.55,1.4-.8165q6.921-3.88,13.6293-7.9422c.1886-.1163.3887-.2212.5773-.3375,1.16-.7071,2.2842-1.4439,3.4326-2.16,1.7348-1.0834,3.4747-2.16,5.1838-3.2617,1.4185-.9146,2.8079-1.8475,4.2088-2.7758,1.6538-1.0949,3.3117-2.1874,4.9388-3.2982.3567-.2441.7272-.479,1.0816-.7231,1.3051-.8986,2.57-1.8178,3.8572-2.7257,1.1307-.7983,2.2733-1.59,3.39-2.3949,1.7678-1.2773,3.4963-2.5729,5.226-3.8684.97-.7254,1.957-1.4416,2.9145-2.1714,2.1189-1.62,4.1916-3.2595,6.25-4.9063.5329-.4265,1.0851-.8439,1.614-1.27q3.7468-3.0313,7.3535-6.122c.07-.0593.1356-.1186.2051-.18.4149-.3558.8116-.7185,1.2236-1.0766q2.9023-2.5148,5.7088-5.0659c.7682-.6979,1.5148-1.4027,2.2705-2.1053q2.2431-2.0835,4.4213-4.1854,1.19-1.153,2.3571-2.3106,2.115-2.0938,4.1546-4.21c.7136-.7368,1.4339-1.4735,2.1338-2.2148.306-.3239.6291-.6432.9335-.9671.9637-1.031,1.8727-2.0756,2.8107-3.1112.81-.8964,1.6379-1.7882,2.429-2.6869,1.2959-1.4757,2.5372-2.9606,3.7784-4.4477.6486-.7756,1.3234-1.5465,1.9565-2.3243q2.5005-3.0758,4.8311-6.179c.1664-.2189.35-.44.5152-.6614q2.4654-3.2948,4.7416-6.6147c.0581-.0866.1083-.171.1659-.2554.8337-1.2226,1.5974-2.45,2.3918-3.6768.7666-1.1838,1.565-2.3631,2.2933-3.5514.7546-1.2294,1.4362-2.4634,2.15-3.6951.6429-1.1108,1.329-2.217,1.9377-3.33.041-.0753.0741-.1506.1146-.2258q2.0455-3.7533,3.8326-7.5225.9719-2.0562,1.85-4.0919,1.1481-2.6584,2.1331-5.2735c.0405-.1072.0718-.21.1112-.3147.5756-1.5351,1.1227-3.0633,1.6168-4.5733.2393-.7321.379-1.4324.5989-2.16.4662-1.535.8851-3.0541,1.2675-4.5641.1955-.7732.4753-1.5715.6491-2.34.1676-.7367.2411-1.4461.3887-2.1782q.3958-1.9708.697-3.905C2120.7618,1428.8948,2120.9453,1428.1581,2121.0359,1427.451Z"/><path class="cls-22" d="M1870.635,1379.0123a27.6514,27.6514,0,0,1-9.4721,18.7159"/><path class="cls-1" d="M856.1847,1621.8681a185.7294,185.7294,0,0,0-18.9143-75.1913,185.284,185.284,0,0,0-211.3558-97.8624c-76.185,19.1862-129.296,83.0609-138.5391,156.6228a183.9519,183.9519,0,0,0-1.3343,29.6894,185.7219,185.7219,0,0,0,18.9144,75.1913c45.2-1.4534,105.9047-9.3452,179.7129-27.9322S815.6837,1641.9968,856.1847,1621.8681Z"/><path class="cls-1" d="M520.1767,1735.839a185.1517,185.1517,0,0,0,196.1347,72.3417c76.1862-19.1852,129.2972-83.0614,138.54-156.6219-45.6126,21.1386-102.5914,40.7343-163.8125,56.1515S570.3542,1732.8567,520.1767,1735.839Z"/><path class="cls-5" d="M954.5643,1557.1161c-6.2573-24.8785-41.2084-37.3159-103.88-36.9538-9.13.0525-18.7181.4044-28.6353.9933a183.802,183.802,0,0,1,15.2213,25.5212c57.0845-1.8349,89.4168,6.605,91.9846,16.8135s-21.9193,32.9562-73.07,58.3778c-40.501,20.1287-97.7079,41.9305-171.5162,60.5176s-134.5133,26.4788-179.7129,27.9322c-57.0834,1.8362-89.4155-6.6049-91.9849-16.8121-2.5665-10.2085,21.9195-32.9575,73.07-58.3792a183.9519,183.9519,0,0,1,1.3343-29.6894c-9.0137,4.1774-17.6272,8.4093-25.6928,12.6885-55.3769,29.3656-80.2795,56.8747-74.0209,81.7531s41.2095,37.3156,103.881,36.9535c9.13-.052,18.718-.4029,28.6339-.9933,50.1775-2.9823,109.6412-12.7114,170.8625-28.1287s118.2-35.0129,163.8125-56.1515c9.0137-4.1784,17.6259-8.41,25.6928-12.6885C935.92,1609.5035,960.8227,1581.9947,954.5643,1557.1161Z"/><g class="cls-24"><path class="cls-12" d="M344.72,1628.4973c173.8481-27.402,157.1777,50.7285,157.1777,50.7285l42.8667-97.03-69.063-14.3164"/><path class="cls-12" d="M774.5777,1493.9109c86.924,11.8713,96.45,73.9682,96.45,73.9682l-73.8259,14.3164-23.8148-22.8"/></g><path class="cls-10" d="M954.5643,1557.1161c-6.2573-24.8785-41.2084-37.3159-103.88-36.9538-9.13.0525-18.7181.4044-28.6353.9933a183.802,183.802,0,0,1,15.2213,25.5212c57.0845-1.8349,89.4168,6.605,91.9846,16.8135s-21.9193,32.9562-73.07,58.3778c-40.501,20.1287-97.7079,41.9305-171.5162,60.5176s-134.5133,26.4788-179.7129,27.9322c-57.0834,1.8362-89.4155-6.6049-91.9849-16.8121-2.5665-10.2085,21.9195-32.9575,73.07-58.3792a183.9519,183.9519,0,0,1,1.3343-29.6894c-9.0137,4.1774-17.6272,8.4093-25.6928,12.6885-55.3769,29.3656-80.2795,56.8747-74.0209,81.7531s41.2095,37.3156,103.881,36.9535c9.13-.052,18.718-.4029,28.6339-.9933,50.1775-2.9823,109.6412-12.7114,170.8625-28.1287s118.2-35.0129,163.8125-56.1515c9.0137-4.1784,17.6259-8.41,25.6928-12.6885C935.92,1609.5035,960.8227,1581.9947,954.5643,1557.1161Z"/><path class="cls-22" d="M618.8,1541.8322l-8.2882,9.6751"/><path class="cls-22" d="M801.2843,1734.1885,769.481,1753.486"/><path class="cls-25" d="M2998.1647,1854.3644c313.75,18.5876,249.3328,190.7691,25.6125,226.4815s295.67-23.5686,295.67-23.5686l-8.4812-170.8976-237.9188-46.2326-63.5082,32.1"/><path class="cls-1" d="M3240.3819,2016.4388a147.8453,147.8453,0,0,0-15.0567-59.8557,147.4409,147.4409,0,0,0-279.5943,70.41,147.8409,147.8409,0,0,0,15.0566,59.8557c35.981-1.1569,84.305-7.4391,143.06-22.2353S3208.1412,2032.4622,3240.3819,2016.4388Z"/><path class="cls-1" d="M2972.9043,2107.1648a147.387,147.387,0,0,0,266.4165-67.0908c-36.31,16.8272-81.6674,32.4263-130.4022,44.6991S3012.8478,2104.7907,2972.9043,2107.1648Z"/><path class="cls-5" d="M3318.6965,1964.8932c-4.9811-19.8043-32.8037-29.7051-82.6931-29.4169-7.2676.0418-14.9.3219-22.795.7908a146.3079,146.3079,0,0,1,12.1168,20.316c45.4419-1.4606,71.18,5.2579,73.224,13.3843s-17.4488,26.2346-58.1673,46.4714c-32.2407,16.0234-77.78,33.3785-136.5347,48.1747s-107.0787,21.0784-143.06,22.2353c-45.4409,1.4618-71.1787-5.2578-73.2241-13.3832-2.0431-8.1264,17.4489-26.2356,58.1675-46.4725a146.4215,146.4215,0,0,1,1.0621-23.634c-7.1753,3.3253-14.0321,6.6941-20.4526,10.1006-44.0825,23.3763-63.9062,45.2748-58.9241,65.0791s32.8047,29.705,82.694,29.4167c7.2676-.0414,14.9005-.3207,22.794-.7907,39.9435-2.3741,87.2794-10.1189,136.0143-22.3917s94.0925-27.8719,130.4022-44.6991c7.1753-3.3262,14.031-6.6952,20.4526-10.1007C3303.8549,2006.596,3323.6785,1984.6977,3318.6965,1964.8932Z"/><g class="cls-26"><path class="cls-7" d="M2833.2329,2021.7159c138.3911-21.8132,125.1207,40.3822,125.1207,40.3822l34.1238-77.2405L2937.5,1973.4611"/><path class="cls-7" d="M3175.419,1914.579c69.1955,9.45,76.7786,58.8821,76.7786,58.8821l-58.7688,11.3965-18.9577-18.15"/></g><path class="cls-10" d="M3318.6965,1964.8932c-4.9811-19.8043-32.8037-29.7051-82.6931-29.4169-7.2676.0418-14.9.3219-22.795.7908a146.3079,146.3079,0,0,1,12.1168,20.316c45.4419-1.4606,71.18,5.2579,73.224,13.3843s-17.4488,26.2346-58.1673,46.4714c-32.2407,16.0234-77.78,33.3785-136.5347,48.1747s-107.0787,21.0784-143.06,22.2353c-45.4409,1.4618-71.1787-5.2578-73.2241-13.3832-2.0431-8.1264,17.4489-26.2356,58.1675-46.4725a146.4215,146.4215,0,0,1,1.0621-23.634c-7.1753,3.3253-14.0321,6.6941-20.4526,10.1006-44.0825,23.3763-63.9062,45.2748-58.9241,65.0791s32.8047,29.705,82.694,29.4167c7.2676-.0414,14.9005-.3207,22.794-.7907,39.9435-2.3741,87.2794-10.1189,136.0143-22.3917s94.0925-27.8719,130.4022-44.6991c7.1753-3.3262,14.031-6.6952,20.4526-10.1007C3303.8549,2006.596,3323.6785,1984.6977,3318.6965,1964.8932Z"/><path class="cls-22" d="M3045.7775,1951.81a12.1542,12.1542,0,0,1-5.6634,8.5336"/><path class="cls-22" d="M3194.9144,2112.416a38.5857,38.5857,0,0,1-17.366,9.1106"/><path class="cls-22" d="M3264.3464,2013.62a17.6193,17.6193,0,0,0-10.57,6.6062"/><polygon class="cls-1" points="3345.363 898.046 3154.302 1011.166 3123.625 827.868 3314.686 714.748 3345.363 898.046"/><polygon class="cls-7" points="3345.363 898.046 3489.251 1007.416 3298.19 1120.536 3154.302 1011.166 3345.363 898.046"/><g class="cls-27"><line class="cls-22" x1="3076.6275" y1="1215.7035" x2="3566.9255" y2="1215.7035"/><line class="cls-22" x1="3076.6275" y1="1196.9387" x2="3566.9255" y2="1196.9387"/><line class="cls-22" x1="3076.6275" y1="1178.1739" x2="3566.9255" y2="1178.1739"/><line class="cls-22" x1="3076.6275" y1="1159.4091" x2="3566.9255" y2="1159.4091"/><line class="cls-22" x1="3076.6275" y1="1140.6444" x2="3566.9255" y2="1140.6444"/><line class="cls-22" x1="3076.6275" y1="1121.8796" x2="3566.9255" y2="1121.8796"/><line class="cls-22" x1="3076.6275" y1="1103.1148" x2="3566.9255" y2="1103.1148"/><line class="cls-22" x1="3076.6275" y1="1084.35" x2="3566.9255" y2="1084.35"/><line class="cls-22" x1="3076.6275" y1="1065.5852" x2="3566.9255" y2="1065.5852"/><line class="cls-22" x1="3076.6275" y1="1046.8204" x2="3566.9255" y2="1046.8204"/><line class="cls-22" x1="3076.6275" y1="1028.0556" x2="3566.9255" y2="1028.0556"/><line class="cls-22" x1="3076.6275" y1="1009.2908" x2="3566.9255" y2="1009.2908"/><line class="cls-22" x1="3076.6275" y1="990.5261" x2="3566.9255" y2="990.5261"/><line class="cls-22" x1="3076.6275" y1="971.7613" x2="3566.9255" y2="971.7613"/><line class="cls-22" x1="3076.6275" y1="952.9965" x2="3566.9255" y2="952.9965"/><line class="cls-22" x1="3076.6275" y1="934.2317" x2="3566.9255" y2="934.2317"/><line class="cls-22" x1="3076.6275" y1="915.4669" x2="3566.9255" y2="915.4669"/><line class="cls-22" x1="3076.6275" y1="896.7021" x2="3566.9255" y2="896.7021"/><line class="cls-22" x1="3076.6275" y1="877.9373" x2="3566.9255" y2="877.9373"/><line class="cls-22" x1="3076.6275" y1="859.1725" x2="3566.9255" y2="859.1725"/><line class="cls-22" x1="3076.6275" y1="840.4078" x2="3566.9255" y2="840.4078"/><line class="cls-22" x1="3076.6275" y1="821.643" x2="3566.9255" y2="821.643"/><line class="cls-22" x1="3076.6275" y1="802.8782" x2="3566.9255" y2="802.8782"/></g><polygon class="cls-10" points="3345.363 898.046 3489.251 1007.416 3298.19 1120.536 3154.302 1011.166 3345.363 898.046"/><polygon class="cls-1" points="3314.686 714.748 3458.574 824.118 3489.251 1007.416 3345.363 898.046 3314.686 714.748"/><path class="cls-22" d="M3244.7167,832.23a346.6058,346.6058,0,0,1-37.8091,47.2881"/><ellipse class="cls-3" cx="2238.2195" cy="465.6543" rx="14.4946" ry="14.1999"/><path class="cls-7" d="M3460.9969,1271.4591l-56.0531,26.2187s-78.43-25.4856-91.0333-16.68L3125.9433,1412.33l-124.5871-92.9709,323.8746-187.334Z"/><g class="cls-28"><path class="cls-9" d="M3338.9215,1335.2789c-32.28-42.7614-176.3238-110.2451-176.3238-110.2451l.8284,52.4717,85.02,22.2905"/></g><path class="cls-10" d="M3460.9969,1271.4591l-56.0531,26.2187s-78.43-25.4856-91.0333-16.68L3125.9433,1412.33l-124.5871-92.9709,323.8746-187.334Z"/><path class="cls-3" d="M3295.1488,1150.4232l28.5817-27.8612a25.5157,25.5157,0,0,1,36.096.4712l253.0374,259.8733-7.7441,7.5489c-15.4219,15.0332-38.39,16.8485-52.3692,3.9911-39.3177-36.1626-116.8331-101.293-161.3569-97.4757C3391.3937,1296.9708,3472.4439,1246.6434,3295.1488,1150.4232Z"/><path class="cls-20" d="M3162.5977,1225.0338s151.3128,55.964,133.88,178.0278l-556.8785,364.0836c-32.1165,25.7165-74.6682,34.3371-117.764,23.8579h0c-64.0708-15.5795-118.8462-70.5873-138.5969-139.1852l-164.6647-571.91,281.84-45.2732,102.6252,457.5217Z"/><path class="cls-10" d="M3162.5977,1225.0338s151.3128,55.964,133.88,178.0278l-556.8785,364.0836c-32.1165,25.7165-74.6682,34.3371-117.764,23.8579h0c-64.0708-15.5795-118.8462-70.5873-138.5969-139.1852l-164.6647-571.91,281.84-45.2732,102.6252,457.5217Z"/><path class="cls-7" d="M1793.6523,2508.0026l26.1585-60.3012s77.6052-33.6875,81.5669-49.6185l59.0884-237.6048,157.8929-10.7213-127.5044,377.6415Z"/><g class="cls-29"><path class="cls-9" d="M2197.4592,2482.5468c-220-29.8843-265.5928-220.4339-265.5928-220.4339l283.0023,12.5543"/></g><path class="cls-10" d="M1793.6523,2508.0026l26.1585-60.3012s77.6052-33.6875,81.5669-49.6185l59.0884-237.6048,157.8929-10.7213-127.5044,377.6415Z"/><path class="cls-3" d="M2002.0391,2491.5086l-3.78,42.563c-1.3355,15.0372-14.0019,26.0879-28.2912,24.6825l-367.5406-36.1493,1.0242-11.5323c2.04-22.9659,18.6465-40.3958,37.9626-39.6212,54.3282,2.1787,157.371,1.4482,189.3668-32.5832C1830.7807,2438.8681,1801.1456,2536.1836,2002.0391,2491.5086Z"/><path class="cls-20" d="M2144.284,2391.797s-109.8881,6.6411-212.4176-129.6841l140.7071-679.8987a83.0785,83.0785,0,0,1,14.0975-32.4373l437.9337-596.6163,95.7826,112.2367c69.2243,81.1161,74.7581,201.6274,12.3051,267.9721l-370.0935,393.155Z"/><g class="cls-30"><ellipse class="cls-1" cx="2078.8519" cy="1569.8548" rx="88.3539" ry="37.6057"/></g><path class="cls-10" d="M2144.284,2391.797s-109.8881,6.6411-212.4176-129.6841l140.7071-679.8987a83.0785,83.0785,0,0,1,14.0975-32.4373l437.9337-596.6163,95.7826,112.2367c69.2243,81.1161,74.7581,201.6274,12.3051,267.9721l-370.0935,393.155Z"/><path class="cls-7" d="M1562.4739,837.0925c-23.2529,2.5486,4.3309,45.84,4.3309,45.84s-68.43-27.0416-76.8444-33.9936c-9.89-8.1715-10.21,8.48-3.4273,12.54,0,0-17.1013-6-20.9007-10.9089-1.0253-1.3248-9.1943,14.2895,9.2478,26.7648,0,0-2.3186,16.05,15.7762,25.3622,0,0-11.4627,2.1039,2.7451,15.6965s77.9961,41.2385,93.1582,40.3383l116.0717,49.4268-1.9857-82.0161s-94.6368-35.6846-102.1637-40.5419C1574.0422,869.83,1562.4739,837.0925,1562.4739,837.0925Z"/><g class="cls-31"><path class="cls-9" d="M1648.5279,925.8615l-6.2332-22.0917h0c-27.1079,78.5-47.6317,59.9758-47.6317,59.9758l21.4331,33.6881,37.2814-6.0654"/></g><path class="cls-10" d="M1562.4739,837.0925c-23.2529,2.5486,4.3309,45.84,4.3309,45.84s-68.43-27.0416-76.8444-33.9936c-9.89-8.1715-10.21,8.48-3.4273,12.54,0,0-17.1013-6-20.9007-10.9089-1.0253-1.3248-9.1943,14.2895,9.2478,26.7648,0,0-2.3186,16.05,15.7762,25.3622,0,0-11.4627,2.1039,2.7451,15.6965s77.9961,41.2385,93.1582,40.3383l116.0717,49.4268-1.9857-82.0161s-94.6368-35.6846-102.1637-40.5419C1574.0422,869.83,1562.4739,837.0925,1562.4739,837.0925Z"/><polygon class="cls-7" points="2091.223 686.85 2207.941 879.665 2313.163 770.967 2215.49 672.773 2091.223 686.85"/><g class="cls-32"><path class="cls-9" d="M2208.5317,663.5514c-114.56,101.9168,8.5276,206.6939,8.5276,206.6939L2032.18,789.8178l20.7657-101.7649,136.9081-26.244"/></g><polygon class="cls-10" points="2091.223 686.85 2207.941 879.665 2313.163 770.967 2215.49 672.773 2091.223 686.85"/><path class="cls-1" d="M2319.4048,738.53l-39.3683-12.8378a8.4828,8.4828,0,0,1-5.0031-4.3747l-4.2939-8.8749A11.03,11.03,0,0,0,2251.54,711.23L2181.3811,819a8.5167,8.5167,0,0,0,7.6758,13.168l22.8274-1.6"/><path class="cls-7" d="M2890.5986,1220.8288c26.5356.5931,23.1388-43.4553,23.1388-43.4553s45.3284,74.1713,46.156,75.3205c5.0338,6.9906,15.6991-3.8308,15.115-9.522,0,0,11.1134,22.5718,12.8032,21.886s16.9291-7.4195,7.3186-29.2761c0,0,16.984-8.8691,5.8215-27.699,0,0,14.6587,1.0376,9.6112-19.3566s-36.5507-75.43-65.3031-90.5792l-75.5385-109.5082-53.7408,72.3808s67.9419,86.252,70.1467,95.5657C2893.5626,1187.9952,2890.5986,1220.8288,2890.5986,1220.8288Z"/><g class="cls-33"><path class="cls-9" d="M2908.8213,1051.4162c70.9256,18.1963-73.6535,41.3659-70.9256,63.8992s-18.3858-69.8865-18.3858-69.8865l16.061-10.7946"/></g><path class="cls-10" d="M2890.5986,1220.8288c26.5356.5931,23.1388-43.4553,23.1388-43.4553s45.3284,74.1713,46.156,75.3205c5.0338,6.9906,15.6991-3.8308,15.115-9.522,0,0,11.1134,22.5718,12.8032,21.886s16.9291-7.4195,7.3186-29.2761c0,0,16.984-8.8691,5.8215-27.699,0,0,14.6587,1.0376,9.6112-19.3566s-36.5507-75.43-65.3031-90.5792l-75.5385-109.5082-53.7408,72.3808s67.9419,86.252,70.1467,95.5657C2893.5626,1187.9952,2890.5986,1220.8288,2890.5986,1220.8288Z"/><path class="cls-5" d="M2415.33,877.2338,2665.1117,891.08l156.78,197.59s97.8862,3.5462,111.4138-44.5738l-198.93-273.5209a94.7619,94.7619,0,0,0-79.5014-38.99l-377.34,11.4175Z"/><path class="cls-10" d="M2415.33,877.2338,2665.1117,891.08l156.78,197.59s97.8862,3.5462,111.4138-44.5738l-198.93-273.5209a94.7619,94.7619,0,0,0-79.5014-38.99l-377.34,11.4175Z"/><path class="cls-5" d="M2810.9523,955.92c44.67-5.9145,8.5055,348.2978-337.4887,320.39,0,0-86.301-84.0106-166.4824-184.518-70.8807-88.8491-136.9792-190.59-134.45-258.5826l104.9349-108.8882C2473.9111,678.3226,2530.7507,993.02,2810.9523,955.92Z"/><g class="cls-34"><path class="cls-12" d="M2412.2755,961.5964c-142.7009,96.8542,79.702,455.1739,385.1821,161.0147s5.0612,185.0173,5.0612,185.0173l-293.0122,146.244-380.1773-166.2379,41.5251-329.9689,170.8142-2.7894Z"/></g><path class="cls-10" d="M2810.9523,955.92c44.67-5.9145,8.5055,348.2978-337.4887,320.39,0,0-86.301-84.0106-166.4824-184.518-70.8807-88.8491-136.9792-190.59-134.45-258.5826l104.9349-108.8882C2473.9111,678.3226,2530.7507,993.02,2810.9523,955.92Z"/><path class="cls-5" d="M2247.4127,809.5741,1937.5074,982.2719l-294.225-82.15s-26.1452,95.1814-20.6218,107.0163l291.8943,122.7073a108.16,108.16,0,0,0,74.9928,3.2714l422.7278-171.52"/><path class="cls-10" d="M2247.4127,809.5741,1937.5074,982.2719l-294.225-82.15s-26.1452,95.1814-20.6218,107.0163l291.8943,122.7073a108.16,108.16,0,0,0,74.9928,3.2714l422.7278-171.52"/><path class="cls-1" d="M2225.8323,695.1985l-9.0112-118.2833-139.3127-65.4523c-28.0819,28.3275-56.1524,180.0294-20.7047,213.47,13.4436,12.6826,71.3349-.4646,115.6584-13.0676"/><path class="cls-3" d="M2223.604,718.1732c-75.4589-55.0309-97.71-101.3445-66.123-127.8208s93.0164,26.535,93.0164,26.535l15.47,28.229Z"/><path class="cls-1" d="M2070.6345,511.9377c34.9017-55.2892,108.8954-72.3617,165.2707-38.1313s73.7811,106.8,38.88,162.0891a118.6777,118.6777,0,0,1-11.5713,15.3756l-201.4526-122.32A117.5828,117.5828,0,0,1,2070.6345,511.9377Z"/><path class="cls-7" d="M2049.4265,539.79,2258.819,666.9313a12.9535,12.9535,0,0,0,17.63-4.0677l8.8215-13.9746a12.4207,12.4207,0,0,0-4.1474-17.2908l-48.03-29.163-161.3629-97.9779a12.9535,12.9535,0,0,0-17.63,4.0677L2045.279,522.5A12.4208,12.4208,0,0,0,2049.4265,539.79Z"/><g class="cls-35"><path class="cls-1" d="M2258.136,585.6943c-5.4558,55.238-53.3695,93.7976-53.3695,93.7976L2161.75,651.2711s41.9446-62.8582,55.0711-74.3559"/><polyline class="cls-1" points="2177.27 548.131 2131.2 606.871 2091.223 582.945 2143.566 522.656 2101.745 506.612 2051.724 568.147"/></g><path class="cls-10" d="M2049.4265,539.79,2258.819,666.9313a12.9535,12.9535,0,0,0,17.63-4.0677l8.8215-13.9746a12.4207,12.4207,0,0,0-4.1474-17.2908l-48.03-29.163-161.3629-97.9779a12.9535,12.9535,0,0,0-17.63,4.0677L2045.279,522.5A12.4208,12.4208,0,0,0,2049.4265,539.79Z"/><path class="cls-1" d="M2131.2,618.9516c88.3391-42.7585,98.1342,40.1451,24.9776,60.54"/><path class="cls-1" d="M2555.9173,1065.21c-116.83,29.1141-143.6418-30.5757-143.6418-30.5757l-5.9231,19.2892s26.8924,63.038,149.2354,26.2444Z"/><path class="cls-1" d="M2167.2058,2287.2057l-20.914,104.6288s-116.96,4.3249-220.7905-134.7649l15.1559-73.2336C2137.1988,2323.5983,2167.2058,2287.2057,2167.2058,2287.2057Z"/><path class="cls-1" d="M3239.467,1446.5521l93.9284-50.5229s-11.5011-113.2024-174.7459-172.0236l-65.5931,35.8582C3283.9757,1407.2218,3239.467,1446.5521,3239.467,1446.5521Z"/><rect class="cls-20" x="2062.4791" y="586.5522" width="30.8553" height="16.7233" rx="8.3616" transform="translate(595.8575 2168.1745) rotate(-62.6866)"/><path class="cls-20" d="M2091.196,552.9208c22.9554,10.5931,23.1489,22.4809,8.3675,16.7954S2076.1983,546,2091.196,552.9208Z"/><path class="cls-1" d="M2057.5,585.2846c-36.3883,3.4132-32.1456,42.919-7.5945,42.919"/><g class="cls-21"><ellipse class="cls-9" cx="2106.1811" cy="630.6457" rx="22.8279" ry="21.1086"/></g><path class="cls-1" d="M2083.3527,648.277c-16.7942,9.89-31.0792.6269-31.0792.6269"/><path class="cls-22" d="M2165.8145,632.533a8.0421,8.0421,0,1,1,8.0009,13.58"/><path class="cls-22" d="M3446.34,1364.93a238.8181,238.8181,0,0,0-30.0153-64.2408,250.7892,250.7892,0,0,0-20.882,35.6627"/><path class="cls-10" d="M1804.45,2426.9039l-.2422,27.5683-61.5205-24.25"/><path class="cls-10" d="M2663.3255,886.8682a29.7681,29.7681,0,0,0-4.7566-26.6985"/><path class="cls-10" d="M1938.2929,983.9442a47.2335,47.2335,0,0,0,24.2827,34.0259"/><path class="cls-10" d="M2630.4887,959.6758a112.8649,112.8649,0,0,1-38.5746,87.4122"/><path class="cls-10" d="M2259.7063,1728.43a99.9221,99.9221,0,0,1-53.1428-26.6152"/><path class="cls-10" d="M2704.076,1486.2616a112.9018,112.9018,0,0,1-30.1339,60.2039"/><path class="cls-10" d="M3007.6742,1484.44a39.5361,39.5361,0,0,1,2.4343,33.7245"/><path class="cls-22" d="M2121.3846,1949.31a231.7949,231.7949,0,0,1-58.1469,5.2136"/></svg>
```

## File: miniprogram/images/icon-index-build.svg
```
<svg id="图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 4000 3000"><defs><style>.cls-1,.cls-10{fill:none;}.cls-2,.cls-6,.cls-7{fill:#22c678;}.cls-10,.cls-16,.cls-17,.cls-2,.cls-3,.cls-4{stroke:#3d3d3c;stroke-linecap:round;stroke-linejoin:round;stroke-width:4px;}.cls-3,.cls-9{fill:#14a35b;}.cls-13,.cls-4{fill:#4f4f5b;}.cls-5{clip-path:url(#clip-path);}.cls-6{opacity:0.2;}.cls-8{clip-path:url(#clip-path-2);}.cls-11{clip-path:url(#clip-path-3);}.cls-12{clip-path:url(#clip-path-4);}.cls-14{clip-path:url(#clip-path-5);}.cls-15{fill:#3d3d3c;}.cls-16,.cls-18{fill:#fff;}.cls-17{fill:#cfcfd0;}.cls-19{clip-path:url(#clip-path-6);}.cls-20{clip-path:url(#clip-path-7);}.cls-21{clip-path:url(#clip-path-8);}.cls-22{clip-path:url(#clip-path-9);}.cls-23{fill:#190000;}.cls-24{clip-path:url(#clip-path-10);}</style><clipPath id="clip-path"><rect class="cls-1" width="4000" height="3000"/></clipPath><clipPath id="clip-path-2"><path class="cls-2" d="M450.127,2243.22c0-184.6748,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4285,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H459.2773A336.9017,336.9017,0,0,1,450.127,2243.22Z"/></clipPath><clipPath id="clip-path-3"><path class="cls-3" d="M1529.6581,1089.0266c0-184.6748,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4286,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2853,220.6511,220.6511,0,0,1,111.5774,30.1125,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3114-1.1827,25.8151H1538.8084A336.9,336.9,0,0,1,1529.6581,1089.0266Z"/></clipPath><clipPath id="clip-path-4"><path class="cls-3" d="M2127.17,2208.3681c0-184.6747,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4285,65.9152,286.3517,164.207a223.8828,223.8828,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H2136.32A336.9017,336.9017,0,0,1,2127.17,2208.3681Z"/></clipPath><clipPath id="clip-path-5"><rect class="cls-4" x="1688.2857" y="1066.1595" width="1436.2233" height="1248.8181"/></clipPath><clipPath id="clip-path-6"><polygon class="cls-2" points="775.864 2341.886 2052.95 2341.886 2052.95 1126.354 1414.407 689.16 775.864 1126.354 775.864 2341.886"/></clipPath><clipPath id="clip-path-7"><rect class="cls-4" x="3346.4781" y="1970.8518" width="50.5678" height="395.3951"/></clipPath><clipPath id="clip-path-8"><path class="cls-4" d="M3496.1241,1764.1166H3234.7806a53.8923,53.8923,0,0,1,53.7488,54.0342v195.208H3549.873v-195.208A53.8923,53.8923,0,0,0,3496.1241,1764.1166Z"/></clipPath><clipPath id="clip-path-9"><path class="cls-4" d="M3234.7806,1764.1166h-37.9487a53.8923,53.8923,0,0,0-53.7489,54.0342v195.208h145.4464v-195.208A53.8923,53.8923,0,0,0,3234.7806,1764.1166Z"/></clipPath><clipPath id="clip-path-10"><path class="cls-2" d="M3281.138,2274.6415c67.1542,0,124.9577,40.0908,151.1322,97.7438H3130.0047C3156.178,2314.7323,3213.9827,2274.6415,3281.138,2274.6415Z"/></clipPath></defs><title>建筑描边矢量插画人物场景插画2屋子07线条绿</title><g class="cls-5"><path class="cls-6" d="M1130.7886,1761.1034c88.7939,49.88,202.9509,81.013,329.3748,87.2431.0563.4414.0887.8826.1462,1.3241,40.1072,307.8052,466.1614,506.0521,951.6181,442.7968,462.36-60.2457,811.8355-335.9743,809.3947-628.003,195.4048-94.5627,317.078-248.4616,296.6991-404.861-12.9458-99.3539-81.0651-183.0333-183.6475-241.4694,52.7934-84.9163,76.9532-176.6947,64.9109-269.114-40.1071-307.8051-466.1608-506.0523-951.6178-442.797-307.8172,40.1087-565.5539,175.7415-702.7749,348.0912-118.8367-19.6139-249.2879-22.2824-384.3548-4.6831C875.0806,712.8864,514.0536,1013.69,554.1607,1321.4954,583.4012,1545.9034,817.8067,1712.042,1130.7886,1761.1034Z"/><path class="cls-7" d="M450.127,2243.22c0-184.6748,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4285,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H459.2773A336.9017,336.9017,0,0,1,450.127,2243.22Z"/><g class="cls-8"><path class="cls-9" d="M804.3508,1854.2348c-276.5626,293.1279,18.4375,422.8763-198.2032,547.9908s253.5157,13.9016,253.5157,13.9016Z"/></g><path class="cls-10" d="M450.127,2243.22c0-184.6748,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4285,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H459.2773A336.9017,336.9017,0,0,1,450.127,2243.22Z"/><path class="cls-9" d="M1529.6581,1089.0266c0-184.6748,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4286,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2853,220.6511,220.6511,0,0,1,111.5774,30.1125,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3114-1.1827,25.8151H1538.8084A336.9,336.9,0,0,1,1529.6581,1089.0266Z"/><g class="cls-11"><path class="cls-7" d="M1522.0572,1163.6542c0-184.6747,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4286,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H1531.2075A336.9017,336.9017,0,0,1,1522.0572,1163.6542Z"/></g><path class="cls-10" d="M1529.6581,1089.0266c0-184.6748,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4286,65.9152,286.3517,164.207a223.8566,223.8566,0,0,1,23.8891-1.2853,220.6511,220.6511,0,0,1,111.5774,30.1125,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3114-1.1827,25.8151H1538.8084A336.9,336.9,0,0,1,1529.6581,1089.0266Z"/><path class="cls-9" d="M2127.17,2208.3681c0-184.6747,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4285,65.9152,286.3517,164.207a223.8828,223.8828,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H2136.32A336.9017,336.9017,0,0,1,2127.17,2208.3681Z"/><g class="cls-12"><path class="cls-7" d="M2026,2275.6759c0-184.6748,148.9171-334.3832,332.6157-334.3832,121.88,0,228.4286,65.9152,286.3517,164.2071a223.8584,223.8584,0,0,1,23.8892-1.2853,220.6517,220.6517,0,0,1,111.5774,30.1125,278.734,278.734,0,0,1,203.5043-88.0584c154.7647,0,280.2264,126.1285,280.2264,281.7158,0,8.7051-.4141,17.3115-1.1827,25.8152H2035.15A336.9008,336.9008,0,0,1,2026,2275.6759Z"/></g><path class="cls-10" d="M2127.17,2208.3681c0-184.6747,148.9172-334.3831,332.6158-334.3831,121.88,0,228.4285,65.9152,286.3517,164.207a223.8828,223.8828,0,0,1,23.8891-1.2852,220.651,220.651,0,0,1,111.5774,30.1124,278.7345,278.7345,0,0,1,203.5043-88.0583c154.7648,0,280.2265,126.1284,280.2265,281.7158,0,8.705-.4141,17.3115-1.1827,25.8152H2136.32A336.9017,336.9017,0,0,1,2127.17,2208.3681Z"/><rect class="cls-13" x="1688.2857" y="1066.1595" width="1436.2233" height="1248.8181"/><g class="cls-14"><polyline class="cls-15" points="3213.386 1698.605 2082.046 1698.605 2082.046 2286.492 1782.437 2286.492 1846.07 1495.773 3154.918 1536.938 3280.178 1709.987"/></g><rect class="cls-10" x="1688.2857" y="1066.1595" width="1436.2233" height="1248.8181"/><rect class="cls-16" x="2141.1396" y="1821.1384" width="371.9305" height="293.9519" transform="translate(4654.2097 3936.2287) rotate(180)"/><rect class="cls-16" x="2337.3822" y="1842.7674" width="149.0363" height="250.694" transform="translate(4823.8007 3936.2287) rotate(180)"/><rect class="cls-16" x="2167.7913" y="1842.7674" width="149.0363" height="250.694" transform="translate(4484.6188 3936.2287) rotate(180)"/><rect class="cls-16" x="2645.0826" y="1165.8215" width="371.9305" height="293.9519" transform="translate(5662.0956 2625.5949) rotate(180)"/><rect class="cls-16" x="2841.3251" y="1187.4505" width="149.0363" height="250.694" transform="translate(5831.6865 2625.5949) rotate(180)"/><rect class="cls-17" x="2671.7342" y="1187.4505" width="149.0363" height="250.694" transform="translate(5492.5046 2625.5949) rotate(180)"/><rect class="cls-16" x="2114.488" y="1165.8215" width="371.9305" height="293.9519" transform="translate(4600.9065 2625.5949) rotate(180)"/><rect class="cls-2" x="1628.2283" y="1020.0213" width="1556.338" height="81.5227" transform="translate(4812.7946 2121.5653) rotate(180)"/><rect class="cls-2" x="2719.0258" y="1858.5162" width="235.9014" height="456.4614" transform="translate(5673.9529 4173.4938) rotate(180)"/><rect class="cls-16" x="2738.1626" y="1979.4805" width="7.7973" height="92.4969" transform="translate(5484.1226 4051.4579) rotate(180)"/><rect class="cls-16" x="2683.5816" y="1821.1384" width="300.9752" height="37.3778" transform="translate(5668.1385 3679.6546) rotate(180)"/><rect class="cls-16" x="2672.3546" y="1821.1384" width="323.4292" height="11.1261" transform="translate(5668.1385 3653.4029) rotate(180)"/><rect class="cls-2" x="2688.1579" y="2291.2646" width="291.8227" height="47.4261" transform="translate(5668.1385 4629.9553) rotate(180)"/><rect class="cls-2" x="2688.1579" y="2314.9776" width="291.8227" height="23.713" transform="translate(5668.1385 4653.6683) rotate(180)"/><rect class="cls-18" x="1653.9654" y="1459.7734" width="1500.953" height="166.9647"/><rect class="cls-10" x="1653.9654" y="1459.7734" width="1500.953" height="166.9647"/><rect class="cls-16" x="1653.9654" y="1609.493" width="1500.953" height="34.4904" transform="translate(4808.8837 3253.4763) rotate(180)"/><polygon class="cls-16" points="728.307 1184.868 728.307 1097.371 1414.407 627.615 2100.508 1097.371 2100.508 1184.868 728.307 1184.868"/><polygon class="cls-7" points="775.864 2341.886 2052.95 2341.886 2052.95 1126.354 1414.407 689.16 775.864 1126.354 775.864 2341.886"/><g class="cls-19"><polyline class="cls-9" points="694.624 1246.216 1414.407 742.41 2052.95 1152.508 1773.218 705.339 1200.586 617.296 917.616 719.241"/></g><polygon class="cls-10" points="775.864 2341.886 2052.95 2341.886 2052.95 1126.354 1414.407 689.16 775.864 1126.354 775.864 2341.886"/><rect class="cls-16" x="1180.8709" y="1008.2191" width="467.0725" height="252.662" transform="translate(2828.8143 2269.1001) rotate(180)"/><rect class="cls-4" x="1144.1759" y="1260.881" width="540.4624" height="22.6431" transform="translate(2828.8143 2544.4052) rotate(180)"/><rect class="cls-16" x="1200.586" y="1035.5245" width="427.6423" height="210.6919" transform="translate(2828.8143 2281.741) rotate(180)"/><rect class="cls-16" x="1492.3885" y="1035.5245" width="135.8398" height="210.6919" transform="translate(3120.6168 2281.741) rotate(180)"/><rect class="cls-16" x="893.5522" y="1495.7734" width="1041.7099" height="854.9816" transform="translate(2828.8143 3846.5284) rotate(180)"/><rect class="cls-17" x="917.6156" y="1515.5234" width="993.5831" height="835.2316" transform="translate(2828.8143 3866.2784) rotate(180)"/><rect class="cls-16" x="922.8523" y="1565.7395" width="983.1096" height="9.6322" transform="translate(2828.8143 3141.1113) rotate(180)"/><rect class="cls-16" x="922.8523" y="1637.8634" width="983.1096" height="9.6322" transform="translate(2828.8143 3285.3589) rotate(180)"/><rect class="cls-16" x="922.8523" y="1782.111" width="983.1096" height="9.6322" transform="translate(2828.8143 3573.8542) rotate(180)"/><rect class="cls-16" x="922.8523" y="1926.3586" width="983.1096" height="9.6322" transform="translate(2828.8143 3862.3494) rotate(180)"/><rect class="cls-16" x="922.8523" y="1709.9872" width="983.1096" height="9.6322" transform="translate(2828.8143 3429.6065) rotate(180)"/><rect class="cls-16" x="922.8523" y="1854.2348" width="983.1096" height="9.6322" transform="translate(2828.8143 3718.1018) rotate(180)"/><rect class="cls-16" x="922.8523" y="1998.4824" width="983.1096" height="9.6322" transform="translate(2828.8143 4006.5971) rotate(180)"/><rect class="cls-16" x="922.8523" y="2142.7301" width="983.1096" height="9.6322" transform="translate(2828.8143 4295.0923) rotate(180)"/><rect class="cls-16" x="922.8523" y="2070.6063" width="983.1096" height="9.6322" transform="translate(2828.8143 4150.8447) rotate(180)"/><rect class="cls-16" x="922.8523" y="2214.8539" width="983.1096" height="9.6322" transform="translate(2828.8143 4439.34) rotate(180)"/><rect class="cls-16" x="922.8523" y="2286.9777" width="983.1096" height="9.6322" transform="translate(2828.8143 4583.5876) rotate(180)"/><rect class="cls-4" x="1807.2606" y="1315.9125" width="171.6328" height="56.8209" transform="translate(3786.1539 2688.6459) rotate(180)"/><rect class="cls-4" x="1266.4547" y="849.4203" width="171.6328" height="56.8209" transform="translate(2704.5423 1755.6616) rotate(180)"/><rect class="cls-2" x="1028.9532" y="1312.7975" width="171.6328" height="56.8209" transform="translate(2229.5392 2682.4159) rotate(180)"/><rect class="cls-4" x="1028.9532" y="1312.7975" width="171.6328" height="56.8209" transform="translate(2229.5392 2682.4159) rotate(180)"/><rect class="cls-13" x="3346.4781" y="1970.8518" width="50.5678" height="395.3951"/><g class="cls-20"><path class="cls-15" d="M3331.4435,2096.7669c78.9356,1.7377,67.04,226.7465,67.04,226.7465l25.6423-354.1962-55.8886-4.6338"/></g><rect class="cls-10" x="3346.4781" y="1970.8518" width="50.5678" height="395.3951"/><path class="cls-13" d="M3496.1241,1764.1166H3234.7806a53.8923,53.8923,0,0,1,53.7488,54.0342v195.208H3549.873v-195.208A53.8923,53.8923,0,0,0,3496.1241,1764.1166Z"/><g class="cls-21"><path class="cls-15" d="M3079.3907,1793.37c484.3606-6.7043,441.2393-41.4187,470.4823,219.989s140.5451-317.1519,140.5451-317.1519l-197.3439-107.2532-393.3334,82.1338"/></g><path class="cls-10" d="M3496.1241,1764.1166H3234.7806a53.8923,53.8923,0,0,1,53.7488,54.0342v195.208H3549.873v-195.208A53.8923,53.8923,0,0,0,3496.1241,1764.1166Z"/><path class="cls-13" d="M3234.7806,1764.1166h-37.9487a53.8923,53.8923,0,0,0-53.7489,54.0342v195.208h145.4464v-195.208A53.8923,53.8923,0,0,0,3234.7806,1764.1166Z"/><g class="cls-22"><path class="cls-15" d="M3273.96,1815.0891h-37.9487a53.8924,53.8924,0,0,0-53.7489,54.0343v195.208h145.4464v-195.208A53.8923,53.8923,0,0,0,3273.96,1815.0891Z"/></g><path class="cls-10" d="M3234.7806,1764.1166h-37.9487a53.8923,53.8923,0,0,0-53.7489,54.0342v195.208h145.4464v-195.208A53.8923,53.8923,0,0,0,3234.7806,1764.1166Z"/><polygon class="cls-2" points="3461.362 1735.461 3435.492 1725.995 3424.668 1722.034 3418.068 1719.619 3362.275 1873.724 3379.698 1880.099 3427.132 1749.084 3453.003 1758.551 3461.362 1735.461"/><ellipse class="cls-16" cx="3371.762" cy="1880.0992" rx="25.2839" ry="25.4183"/><ellipse class="cls-23" cx="3371.762" cy="1880.0992" rx="9.4873" ry="9.5377"/><path class="cls-7" d="M3281.138,2274.6415c67.1542,0,124.9577,40.0908,151.1322,97.7438H3130.0047C3156.178,2314.7323,3213.9827,2274.6415,3281.138,2274.6415Z"/><g class="cls-24"><path class="cls-9" d="M3130.0047,2274.6415c216.4734,13.5369,200.2049,143.568,200.2049,143.568H3549.873L3418.91,2212.3859"/></g><path class="cls-10" d="M3281.138,2274.6415c67.1542,0,124.9577,40.0908,151.1322,97.7438H3130.0047C3156.178,2314.7323,3213.9827,2274.6415,3281.138,2274.6415Z"/><rect class="cls-16" x="2310.7306" y="1187.4505" width="149.0363" height="250.694" transform="translate(4770.4974 2625.5949) rotate(180)"/><rect class="cls-16" x="2141.1396" y="1187.4505" width="149.0363" height="250.694" transform="translate(4431.3155 2625.5949) rotate(180)"/><path class="cls-10" d="M1060.9713,1148.0122a82.8468,82.8468,0,0,1-24.6289,24.6717"/><path class="cls-10" d="M2004.5261,2074.9281l-24.6767,30.8169"/><path class="cls-10" d="M2886.9344,2034.9782l-24.9642,46.13"/><path class="cls-10" d="M2517.2616,950.0362l-21.57,21.5649"/><path class="cls-10" d="M614.8765,2136.04l-18.7653,18.1937"/></g></svg>
```

## File: miniprogram/images/icon-index-not.svg
```
<svg id="图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 4000 3000"><defs><style>.cls-1,.cls-8{fill:#fff;}.cls-1,.cls-11,.cls-17,.cls-2,.cls-3,.cls-4{stroke:#3d3d3c;stroke-linecap:round;stroke-linejoin:round;stroke-width:4px;}.cls-12,.cls-2{fill:#ecedee;}.cls-3,.cls-5,.cls-6,.cls-7{fill:#22c678;}.cls-14,.cls-4{fill:#4f4f5b;}.cls-5{opacity:0.2;}.cls-7{opacity:0.14;}.cls-9{clip-path:url(#clip-path);}.cls-10{fill:#cfcfd0;}.cls-11{fill:none;}.cls-13{clip-path:url(#clip-path-2);}.cls-15{clip-path:url(#clip-path-3);}.cls-16{fill:#3d3d3c;}.cls-17{fill:#f5f6f7;}.cls-18{fill:#190000;}.cls-19{clip-path:url(#clip-path-4);}.cls-20{clip-path:url(#clip-path-5);}.cls-21{clip-path:url(#clip-path-6);}.cls-22{clip-path:url(#clip-path-7);}.cls-23{clip-path:url(#clip-path-8);}.cls-24{clip-path:url(#clip-path-9);}.cls-25{clip-path:url(#clip-path-10);}.cls-26{clip-path:url(#clip-path-11);}.cls-27{clip-path:url(#clip-path-12);}.cls-28{fill:#14a35b;}.cls-29{clip-path:url(#clip-path-13);}.cls-30{clip-path:url(#clip-path-14);}.cls-31{opacity:0.27;}.cls-32{clip-path:url(#clip-path-15);}</style><clipPath id="clip-path"><path class="cls-1" d="M2815.4792,1664.1206c-88.1181,260.4767,18.5512,423.2746,143.7717,595.3753s79.8732,182.9538,143.7717,309.0576c0,0-42.9069-294.0549-187.693-670.0792C2886.3,1823.0818,2853.1751,1744.3941,2815.4792,1664.1206Z"/></clipPath><clipPath id="clip-path-2"><path class="cls-2" d="M3532.9313,1761.3309c88.1182,260.4767-189.9667,354.4579-315.1872,526.5586s-79.8731,182.9538-143.7716,309.0576c0,0,27.34-138.7982,150.4707-522.5038C3267.7292,1939.5536,3495.2355,1841.6044,3532.9313,1761.3309Z"/></clipPath><clipPath id="clip-path-3"><path class="cls-4" d="M3312.8447,2687.9653c-.5289,17.79-30.1767,121.5124-30.1767,121.5124a50.3282,50.3282,0,0,1-49.1209,39.6073s-120.3043-2.3221-149.4,0c-24.0306,1.9177-115.4629,0-115.4629,0a50.3281,50.3281,0,0,1-49.1208-39.6073l-57.5128-262.273,483.7429,8.0879-32.9483,132.6727"/></clipPath><clipPath id="clip-path-4"><path class="cls-3" d="M2210.3082,945.5979c-17.3891,55.3048,108.4026,220.9728,108.4026,220.9728s403.8763-296.8623,203.8863-450.0767C2412.4253,632.09,2210.3082,945.5979,2210.3082,945.5979Z"/></clipPath><clipPath id="clip-path-5"><path class="cls-1" d="M2309.7531,2871.6682s-51.8268-71.9429,12.4906-74.8121c125.8887-5.6161,166.2852-137.4279,166.2852-137.4279l119.5657-53.4506,60.951,117.1329c8.6169,16.56,4.0464,36.0324-10.2087,43.4936l-195.651,102.4052Z"/></clipPath><clipPath id="clip-path-6"><path class="cls-1" d="M1452.8326,2870.6918s-6.2864-85.5914,63.9369-74.7844c132.5018,13.3616,216.1644-116.7312,216.1644-116.7312l118.3375,66.7889-32.582,87.5093c-7.653,20.5546-28.1439,37.2174-45.7677,37.2174Z"/></clipPath><clipPath id="clip-path-7"><path class="cls-2" d="M1764.3377,2538.0932c8.3783,88.23,158.3259,168.0572,158.3259,168.0572L2880.15,1592.4153l-271.92-190.8268Z"/></clipPath><clipPath id="clip-path-8"><path class="cls-2" d="M2338.8134,2546.6961c290.0883,15.229,250.9226-48.0675,250.9226-48.0675l-298.0507-695.5857,510.5718-118.657c150.4344-34.961,227.2515-203.1567,155.3909-340.2377l-88.4555-168.7374L2518.4861,1283.96l52.8545,71.4243L2006.146,1656.6575c-57.376,29.7507-79.6693,100.6366-49.696,158.0191Z"/></clipPath><clipPath id="clip-path-9"><path class="cls-1" d="M2232.807,598.6958c-107.2446-49.8983-81.1328,101.3433-81.1328,101.3433l-155.9048,522.9852h172.9817l61.39-509.97S2289.13,572.0476,2232.807,598.6958Z"/></clipPath><clipPath id="clip-path-10"><path class="cls-1" d="M2241.7358,678.911c85.58,93.4743,105.0026,131.7761,105.0026,131.7761l142.2528-82.9214-152.747-167.5242L2223.3924,672.33"/></clipPath><clipPath id="clip-path-11"><path class="cls-1" d="M2288.6067,908.387l-186.23,302.19-303.1946.9988s-65.407,9.4012-69.9178,15.2769-82.3226,89.3114-91.3442,88.1363,9.0216,43.48,59.7684-9.4012l19.171-21.1527s21.4265,24.6781,33.8312,25.8533,77.7322-24.5024,77.7322-24.5024l306.38,65.7246c46.3426,2.7063,90.3778-19.31,113.967-56.98L2538.06,887.8946Z"/></clipPath><clipPath id="clip-path-12"><path class="cls-3" d="M2465.7366,1342.0052c144.9089,45.0288,490.5122-154.5413,490.5122-154.5413-78.0824-60.7331-184.0389-502-515.889-513.6919L2319.0834,788.5241C2272.4553,1066.8863,2557.6156,1082.2344,2465.7366,1342.0052Z"/></clipPath><clipPath id="clip-path-13"><path class="cls-3" d="M2468.4482,675.8767c-129.7367-49.0172-245.7682,319.22-245.7682,319.22-.7763,57.9842,167.2476,180.3143,167.2476,180.3143s126.1731-167.746,151.5515-310.3082"/></clipPath><clipPath id="clip-path-14"><polygon class="cls-1" points="1479.935 1361.873 1455.717 1361.873 1355.721 869.749 1379.939 869.749 1479.935 1361.873"/></clipPath><clipPath id="clip-path-15"><polygon class="cls-3" points="1375.365 2085.094 1666.14 2306.636 1280.037 2535.773 989.262 2314.232 1375.365 2085.094"/></clipPath></defs><title>Title-run</title><path class="cls-5" d="M1043.8648,1922.9641c98.39,52.951,225.0595,85.69,365.49,91.6914.06.4725.0938.9448.1554,1.4174,42.9279,329.4529,515.2449,539.5187,1054.9489,469.195,514.0257-66.9778,903.7753-364.1434,902.613-676.8993,217.607-102.3155,353.6091-267.79,331.797-435.1889-13.8564-106.3415-89.0965-195.6024-202.761-257.6443,59.107-91.2273,86.4368-189.6521,73.5476-288.5711-42.9278-329.4529-515.2442-539.519-1054.9486-469.1953C2172.4931,402.36,1885.4142,548.9929,1732.04,734.3112c-131.93-20.3763-276.8539-22.5421-427.0138-2.9762-539.704,70.3237-942.4205,394.4066-899.4927,723.86C436.83,1695.3851,696.3856,1872.0791,1043.8648,1922.9641Z"/><circle class="cls-5" cx="1646" cy="573" r="64"/><circle class="cls-6" cx="828.5" cy="2110.5" r="79.5"/><circle class="cls-7" cx="3515" cy="2111" r="162"/><path class="cls-8" d="M2815.4792,1664.1206c-88.1181,260.4767,18.5512,423.2746,143.7717,595.3753s79.8732,182.9538,143.7717,309.0576c0,0-42.9069-294.0549-187.693-670.0792C2886.3,1823.0818,2853.1751,1744.3941,2815.4792,1664.1206Z"/><g class="cls-9"><path class="cls-10" d="M2814.7833,1569.1862s62.5923-2.2759.6959,94.9344,103.0787,579.9224,103.0787,579.9224l-279.3933-235.8907Z"/></g><path class="cls-11" d="M2815.4792,1664.1206c-88.1181,260.4767,18.5512,423.2746,143.7717,595.3753s79.8732,182.9538,143.7717,309.0576c0,0-42.9069-294.0549-187.693-670.0792C2886.3,1823.0818,2853.1751,1744.3941,2815.4792,1664.1206Z"/><path class="cls-2" d="M2830.5462,2203.1312c-88.1181,123.7179,18.5512,201.0416,143.7717,282.7837s79.8731,86.8971,143.7716,146.7923c0,0-42.9068-139.6665-187.6929-318.2657C2901.3671,2278.6325,2868.2421,2241.2584,2830.5462,2203.1312Z"/><path class="cls-12" d="M3532.9313,1761.3309c88.1182,260.4767-189.9667,354.4579-315.1872,526.5586s-79.8731,182.9538-143.7716,309.0576c0,0,27.34-138.7982,150.4707-522.5038C3267.7292,1939.5536,3495.2355,1841.6044,3532.9313,1761.3309Z"/><g class="cls-13"><path class="cls-10" d="M3335.4308,1869.6469c-17.74,15.7775-93.5749,548.2723-93.5749,548.2723l-241.258,353.9144,57.3753-732.7634Z"/></g><path class="cls-11" d="M3532.9313,1761.3309c88.1182,260.4767-189.9667,354.4579-315.1872,526.5586s-79.8731,182.9538-143.7716,309.0576c0,0,27.34-138.7982,150.4707-522.5038C3267.7292,1939.5536,3495.2355,1841.6044,3532.9313,1761.3309Z"/><path class="cls-3" d="M3087.2818,1320.2516c108.0932,264.005,111.4366,542.37,44.2662,821.9412s-14.24,281.2153-33.5512,477.703c0,0-19.3485-40.5146-37.1386-616.975C3054.6042,1800.2691,3078.1882,1444.4344,3087.2818,1320.2516Z"/><path class="cls-14" d="M3312.8447,2687.9653c-.5289,17.79-30.1767,121.5124-30.1767,121.5124a50.3282,50.3282,0,0,1-49.1209,39.6073s-120.3043-2.3221-149.4,0c-24.0306,1.9177-115.4629,0-115.4629,0a50.3281,50.3281,0,0,1-49.1208-39.6073l-57.5128-262.273,483.7429,8.0879-32.9483,132.6727"/><g class="cls-15"><path class="cls-16" d="M3334.2454,2484.9634c-54.5716,287.8465-329.451,377.2346-450.7214,364.9739s303.1759,113.4188,313.2817,115.4459,274.8794-36.4876,274.8794-36.4876v-309l-64.6775-72.6911"/></g><path class="cls-11" d="M3312.8447,2687.9653c-.5289,17.79-30.1767,121.5124-30.1767,121.5124a50.3282,50.3282,0,0,1-49.1209,39.6073s-120.3043-2.3221-149.4,0c-24.0306,1.9177-115.4629,0-115.4629,0a50.3281,50.3281,0,0,1-49.1208-39.6073l-57.5128-262.273,483.7429,8.0879-32.9483,132.6727"/><path class="cls-17" d="M2899.9,2035.9125a115.1011,115.1011,0,0,0,22.0047,50.8533"/><path class="cls-17" d="M3100.1613,1686.6209l11.182,58.1312"/><path class="cls-17" d="M3375.1507,2050.4248v14.5474"/><path class="cls-17" d="M2907.4473,2384.9555l21.3752,29.3748"/><path class="cls-17" d="M3059.5318,2738.0449a54.1113,54.1113,0,0,0,43.5568,3.1276"/><path class="cls-4" d="M2086.8752,198.151a70.8449,70.8449,0,0,0-70.7408-70.9479H520.6359a70.8449,70.8449,0,0,0-70.7408,70.9479v87.0281h1636.98Z"/><path class="cls-3" d="M449.8951,285.1791v868.6137a70.8449,70.8449,0,0,0,70.7408,70.9479H2016.1344a70.8449,70.8449,0,0,0,70.7408-70.9479V285.1791Z"/><ellipse class="cls-1" cx="538.8286" cy="204.3029" rx="19.5955" ry="19.6529"/><ellipse class="cls-1" cx="621.1675" cy="204.3029" rx="19.5955" ry="19.6529"/><ellipse class="cls-1" cx="703.5064" cy="204.3029" rx="19.5955" ry="19.6529"/><path class="cls-18" d="M710.3729,392.3473H684.3545v83.4214H665.62V392.3473H639.4853V376.5484h70.8876Z"/><path class="cls-18" d="M768.6154,450.3951H733.6427l-7.5171,25.3736H707.0319l33.06-99.22h23.1533l33.05,99.22h-20.162Zm-4.7783-15.7989-12.6449-42.97-12.7712,42.97Z"/><path class="cls-18" d="M807.0359,403.7143a26.6252,26.6252,0,0,1,15.9858-25.0717A42.5331,42.5331,0,0,1,841.29,374.99q13.3588,0,21.9587,5.328a27.2579,27.2579,0,0,1,11.81,15.4969L860.1408,402.76a18.4438,18.4438,0,0,0-6.6235-8.8589q-4.3559-2.9878-11.7515-2.99-7.9979,0-11.9943,3.0488-4.006,3.0535-4.0013,8.9173a9.9455,9.9455,0,0,0,4.1762,8.62q4.1809,2.9879,14.6844,4.9043,16.826,2.9952,24.4644,10.4124,7.6336,7.4295,7.6336,20.5912a27.3128,27.3128,0,0,1-4.5937,15.4969,30.3992,30.3992,0,0,1-12.8878,10.7144,44.39,44.39,0,0,1-18.7926,3.828q-14.568,0-24.2313-5.9855a28.8262,28.8262,0,0,1-12.8878-16.992l15.0341-7.0618a18.5237,18.5237,0,0,0,7.76,10.5293q5.6087,3.5943,14.1988,3.5942,8.5951,0,13.0723-3.5309,4.4724-3.5356,4.4772-10.1153a11.659,11.659,0,0,0-4.72-9.8135q-4.72-3.5941-16.2869-5.7468-15.2818-2.754-22.5609-9.8134Q807.0408,415.4442,807.0359,403.7143Z"/><path class="cls-18" d="M947.2667,475.7687,909.4289,426.22v49.5492H890.6946v-99.22h18.7343v44.5233L943.8,376.5484h22.4346L928.173,423.2243l42.247,52.5444Z"/><ellipse class="cls-2" cx="703.5064" cy="654.6444" rx="59.4172" ry="59.5911"/><rect class="cls-2" x="815.1336" y="623.2128" width="1104.8296" height="62.8633" rx="31.3857"/><ellipse class="cls-1" cx="703.5064" cy="840.6204" rx="59.4172" ry="59.5911"/><rect class="cls-1" x="815.1336" y="809.1888" width="1104.8296" height="62.8633" rx="31.3857"/><ellipse class="cls-1" cx="703.5064" cy="1026.5965" rx="59.4172" ry="59.5911"/><rect class="cls-1" x="815.1336" y="995.1649" width="1104.8296" height="62.8633" rx="31.3857"/><path class="cls-17" d="M1227.1209,407.9484l-42.5954,47.086"/><path class="cls-17" d="M1749.1926,1130.96l-19.3689,11.8841"/><path class="cls-17" d="M1813.7584,211.1585l-52.5832,35.1327"/><polyline class="cls-1" points="2229.841 960.045 2099.989 1139.935 2116.911 1244.023 2246.64 1146.723 2309.572 1000.775"/><path class="cls-6" d="M2210.3082,945.5979c-17.3891,55.3048,108.4026,220.9728,108.4026,220.9728s403.8763-296.8623,203.8863-450.0767C2412.4253,632.09,2210.3082,945.5979,2210.3082,945.5979Z"/><g class="cls-19"><path class="cls-11" d="M2238.4,872.6307c-6.9246,52.4722,20.0044,126.5506,42.3169,148.1568s-16.1573,30.866-16.1573,30.866l-87.293-18.7744-45.4447-131.6974"/></g><path class="cls-11" d="M2210.3082,945.5979c-17.3891,55.3048,108.4026,220.9728,108.4026,220.9728s403.8763-296.8623,203.8863-450.0767C2412.4253,632.09,2210.3082,945.5979,2210.3082,945.5979Z"/><polygon class="cls-1" points="2614.986 2646.884 2608.095 2605.978 2513.337 2423.876 2397.003 2484.766 2527.922 2706.15 2614.986 2646.884"/><path class="cls-8" d="M2309.7531,2871.6682s-51.8268-71.9429,12.4906-74.8121c125.8887-5.6161,166.2852-137.4279,166.2852-137.4279l119.5657-53.4506,60.951,117.1329c8.6169,16.56,4.0464,36.0324-10.2087,43.4936l-195.651,102.4052Z"/><g class="cls-20"><polyline class="cls-1" points="2273.023 2854.228 2453.062 2846.126 2689.334 2713.016 2702.268 2820.662 2463.995 2914.417"/></g><path class="cls-11" d="M2309.7531,2871.6682s-51.8268-71.9429,12.4906-74.8121c125.8887-5.6161,166.2852-137.4279,166.2852-137.4279l119.5657-53.4506,60.951,117.1329c8.6169,16.56,4.0464,36.0324-10.2087,43.4936l-195.651,102.4052Z"/><polygon class="cls-1" points="1805.559 2804.959 1914.976 2627.034 2028.893 2462.051 1885.067 2462.051 1678.397 2774.934 1805.559 2804.959"/><path class="cls-8" d="M1452.8326,2870.6918s-6.2864-85.5914,63.9369-74.7844c132.5018,13.3616,216.1644-116.7312,216.1644-116.7312l118.3375,66.7889-32.582,87.5093c-7.653,20.5546-28.1439,37.2174-45.7677,37.2174Z"/><g class="cls-21"><polyline class="cls-1" points="1432.84 2850.756 1853.645 2846.126 1785.994 2921.362 1506.702 2899.37"/></g><path class="cls-11" d="M1452.8326,2870.6918s-6.2864-85.5914,63.9369-74.7844c132.5018,13.3616,216.1644-116.7312,216.1644-116.7312l118.3375,66.7889-32.582,87.5093c-7.653,20.5546-28.1439,37.2174-45.7677,37.2174Z"/><path class="cls-12" d="M1764.3377,2538.0932c8.3783,88.23,158.3259,168.0572,158.3259,168.0572L2880.15,1592.4153l-271.92-190.8268Z"/><g class="cls-22"><path class="cls-10" d="M1851.2714,2225.6888c271.1053-169.7817,342.3489,190.0729,438.6121,144.8174s-89.5661-419.5651-89.5661-419.5651l-113.4422-14.8872"/><path class="cls-1" d="M1808.2949,2478.8942s-36.7609,6.9682-43.9572,59.199"/></g><path class="cls-11" d="M1764.3377,2538.0932c8.3783,88.23,158.3259,168.0572,158.3259,168.0572L2880.15,1592.4153l-271.92-190.8268Z"/><path class="cls-12" d="M2338.8134,2546.6961c290.0883,15.229,250.9226-48.0675,250.9226-48.0675l-298.0507-695.5857,510.5718-118.657c150.4344-34.961,227.2515-203.1567,155.3909-340.2377l-88.4555-168.7374L2518.4861,1283.96l52.8545,71.4243L2006.146,1656.6575c-57.376,29.7507-79.6693,100.6366-49.696,158.0191Z"/><g class="cls-23"><path class="cls-10" d="M2532.0457,1342.8571c-100.9636,128.115,431.6709-203.1279,452.7332,112.0662s66.1857-288.3526,66.1857-288.3526L2855.43,1047.2916l-533.1862,196.7311"/><path class="cls-10" d="M2571.1526,1812.3557c-64.5114-76.13-335.4831-196.107,102.2467-196.107s410.6558-111.63,410.6558-111.63L3062.9975,1738.09l-213.584,124.1458"/></g><path class="cls-11" d="M2338.8134,2546.6961c290.0883,15.229,250.9226-48.0675,250.9226-48.0675l-298.0507-695.5857,510.5718-118.657c150.4344-34.961,227.2515-203.1567,155.3909-340.2377l-88.4555-168.7374L2518.4861,1283.96l52.8545,71.4243L2006.146,1656.6575c-57.376,29.7507-79.6693,100.6366-49.696,158.0191Z"/><path class="cls-4" d="M2290.0173,2465.3167c9.1652,66.3619,48.7961,81.3794,48.7961,81.3794s281.4232,23.1142,250.9226-48.0675l-25.0547-58.4722S2530.9066,2505.8284,2290.0173,2465.3167Z"/><polygon class="cls-1" points="3105.552 2872.407 3069.114 2872.407 2877.064 1727.383 2913.502 1727.383 3105.552 2872.407"/><polygon class="cls-1" points="2309.572 2872.407 2346.01 2872.407 2538.06 1727.383 2501.622 1727.383 2309.572 2872.407"/><rect class="cls-3" x="2468.76" y="1716.1504" width="477.6039" height="43.8795"/><rect class="cls-14" x="2428.7428" y="1635.6041" width="557.6383" height="88.145" rx="44.0081"/><rect class="cls-11" x="2428.7428" y="1635.6041" width="557.6383" height="88.145" rx="44.0081"/><path class="cls-8" d="M2232.807,598.6958c-107.2446-49.8983-81.1328,101.3433-81.1328,101.3433l-155.9048,522.9852h172.9817l61.39-509.97S2289.13,572.0476,2232.807,598.6958Z"/><g class="cls-24"><path class="cls-10" d="M1898.8141,1151.6261c150.4113,150.8515,238.2734-66.0732,269.548-48.1218s14.827,183.8882,14.827,183.8882l-172.7786,37.4333-114.7637-24.6191"/><path class="cls-10" d="M2153.0514,642.6939c-29.4326,21.7106,58.6263,59.37,69.626,92.6108s30.2951-59.3328,30.2951-59.3328l-17.2973-21.3727"/></g><path class="cls-11" d="M2232.807,598.6958c-107.2446-49.8983-81.1328,101.3433-81.1328,101.3433l-155.9048,522.9852h172.9817l61.39-509.97S2289.13,572.0476,2232.807,598.6958Z"/><ellipse class="cls-4" cx="2489.6108" cy="604.4846" rx="128.8499" ry="71.4873"/><path class="cls-8" d="M2241.7358,678.911c85.58,93.4743,105.0026,131.7761,105.0026,131.7761l142.2528-82.9214-152.747-167.5242L2223.3924,672.33"/><g class="cls-25"><path class="cls-10" d="M2297.5756,642.7848c84.1551-3.01-34.3087,69.2928-29.2029,135.1263s-80.335-77.4087-80.335-77.4087"/></g><path class="cls-11" d="M2241.7358,678.911c85.58,93.4743,105.0026,131.7761,105.0026,131.7761l142.2528-82.9214-152.747-167.5242L2223.3924,672.33"/><ellipse class="cls-4" cx="2183.1891" cy="490.0544" rx="89.2662" ry="57.607"/><path class="cls-4" d="M2307.1657,404.6874c-34.3887,0-66.0394,8.0674-91.6779,21.5423-.1481.0362-.2962.0422-.4443.08-11.8805,3.0551-22.0078,10.7029-29.8614,20.9642-20.07,18.4149-32.1311,41.4226-32.1311,66.4776,0,60.2329,69.0016,109.0624,154.1147,109.0624,85.1172,0,154.1187-48.83,154.1187-109.0624C2461.2844,453.5169,2392.2829,404.6874,2307.1657,404.6874Z"/><path class="cls-1" d="M2168.4741,503.2938c-108.31,128.7279,47.1258,266.6813,159.39,136.0184,0,0-1.0688-16.03-4.4112-38.5321C2212.65,605.4652,2170.7717,552.4043,2168.4741,503.2938Z"/><path class="cls-1" d="M2299.5672,613.3562l-27.1119-36.6506c139.48-120.9784,154.5582,45.3669,46.2695,65.9883"/><path class="cls-8" d="M2288.6067,908.387l-186.23,302.19-303.1946.9988s-65.407,9.4012-69.9178,15.2769-82.3226,89.3114-91.3442,88.1363,9.0216,43.48,59.7684-9.4012l19.171-21.1527s21.4265,24.6781,33.8312,25.8533,77.7322-24.5024,77.7322-24.5024l306.38,65.7246c46.3426,2.7063,90.3778-19.31,113.967-56.98L2538.06,887.8946Z"/><g class="cls-26"><path class="cls-10" d="M2230.8471,1005.3c-45.1234,13.5766,91.6324,192.3356,83.043,268.1385s85.0416-145.9488,85.0416-145.9488l-68.8132-72.4087"/></g><path class="cls-11" d="M2288.6067,908.387l-186.23,302.19-303.1946.9988s-65.407,9.4012-69.9178,15.2769-82.3226,89.3114-91.3442,88.1363,9.0216,43.48,59.7684-9.4012l19.171-21.1527s21.4265,24.6781,33.8312,25.8533,77.7322-24.5024,77.7322-24.5024l306.38,65.7246c46.3426,2.7063,90.3778-19.31,113.967-56.98L2538.06,887.8946Z"/><polygon class="cls-1" points="2455.976 685.519 2440.36 673.772 2414.4 626.667 2314.452 748.167 2309.572 850.95 2455.976 685.519"/><path class="cls-6" d="M2465.7366,1342.0052c144.9089,45.0288,490.5122-154.5413,490.5122-154.5413-78.0824-60.7331-184.0389-502-515.889-513.6919L2319.0834,788.5241C2272.4553,1066.8863,2557.6156,1082.2344,2465.7366,1342.0052Z"/><g class="cls-27"><path class="cls-28" d="M2480.7605,870.3157c178.6034-49.781-24.2654,246.0989,23.3385,370.8177s339.298-11.8187,262.5883,49.2761-363.2433,95.0365-363.2433,95.0365l-58.66-389.1969"/><path class="cls-1" d="M2667.1237,941.4325c-63.2487-80.9961-37.0375-199.1584,45.2354-203.925l142.3526,218.1368c-7.348,15.3185-10.0767,18.6565-24.3319,29.7655-58.4463,33.5351-126.3685,3.2608-163.2562-43.9773m-24.67,20.8175c66.1968,84.9584,186.5576,99.6677,234.1569,28.6162l.4533-.97-188.31-286.5825a167.23,167.23,0,0,0-22.6091,6.7287c-79.7648,45.5376-91.0567,165.7487-23.6911,252.208"/><path class="cls-1" d="M2745.1825,803.6944c5.2355,6.9913,12.1419,9.832,16.2587,9.0249l-20.3066-31.1171c-2.8548,4.39-2.1213,13.8537,4.048,22.0922"/><path class="cls-1" d="M2671.9145,827.0676c14.3588-8.2772,30.7,10.426,26.5086,24.8283-4.0027,13.753-22.2572,12.7309-30.05-1.6825-4.6419-8.5859-3.0862-18.7526,3.5415-23.1458"/><path class="cls-1" d="M2701.6766,901.396c42.0669,58.9686,105.3505,46.0871,112.4986,3.9986a75.7638,75.7638,0,0,0,.7126-10.7752l-38.9061-59.6186c-.3,3.7833-.611,7.6953,4.5119,15.546,26.4778,48.6841-4.0144,67.3393-29.9594,58.5265-6.221-2.1131-18.0532-7.7975-27.4037-22.1255-11.1285-14.4108-23.3648-6.9667-24.4242,2.66a18.5484,18.5484,0,0,0,2.97,11.7881"/></g><path class="cls-11" d="M2465.7366,1342.0052c144.9089,45.0288,490.5122-154.5413,490.5122-154.5413-78.0824-60.7331-184.0389-502-515.889-513.6919L2319.0834,788.5241C2272.4553,1066.8863,2557.6156,1082.2344,2465.7366,1342.0052Z"/><path class="cls-6" d="M2468.4482,675.8767c-129.7367-49.0172-245.7682,319.22-245.7682,319.22-.7763,57.9842,167.2476,180.3143,167.2476,180.3143s126.1731-167.746,151.5515-310.3082"/><g class="cls-29"><path class="cls-11" d="M2239.5541,923.3973c-3.592,70.4145,208.7581,204.222,208.7581,204.222l-33.3352,88.7815-236.25-214.1329"/></g><path class="cls-11" d="M2468.4482,675.8767c-129.7367-49.0172-245.7682,319.22-245.7682,319.22-.7763,57.9842,167.2476,180.3143,167.2476,180.3143s126.1731-167.746,151.5515-310.3082"/><path class="cls-1" d="M1453.943,1324.8258h449.1162a20.8719,20.8719,0,0,1,20.8719,20.8719v15.1906a0,0,0,0,1,0,0H1453.943a0,0,0,0,1,0,0v-36.0625A0,0,0,0,1,1453.943,1324.8258Z"/><rect class="cls-4" x="1500.3823" y="1341.5112" width="49.7964" height="5.1993"/><rect class="cls-4" x="1561.9283" y="1341.5112" width="30.9924" height="5.1993"/><polygon class="cls-8" points="1479.935 1361.873 1455.717 1361.873 1355.721 869.749 1379.939 869.749 1479.935 1361.873"/><g class="cls-30"><polygon class="cls-4" points="1486.277 1306.815 1462.059 1306.815 1377.173 887.282 1401.391 887.282 1486.277 1306.815"/></g><polygon class="cls-11" points="1479.935 1361.873 1455.717 1361.873 1355.721 869.749 1379.939 869.749 1479.935 1361.873"/><rect class="cls-1" x="1362.823" y="1467.9765" width="42.9453" height="1398.8046"/><rect class="cls-1" x="1992.5344" y="1467.9765" width="42.9453" height="1398.8046"/><rect class="cls-2" x="1337.4462" y="1427.5144" width="732.0223" height="54.8176"/><rect class="cls-14" x="1219.9854" y="1351.7341" width="958.7418" height="82.3364" rx="12.3946"/><rect class="cls-11" x="1219.9854" y="1351.7341" width="958.7418" height="82.3364" rx="12.3946"/><path class="cls-4" d="M1808.2949,2478.8942c-38.4585-.4594-52.1014,70.1671-52.1014,70.1671s-2.5145,43.46,166.47,157.0891l44.906-52.234C1815.2309,2571.0328,1808.2949,2478.8942,1808.2949,2478.8942Z"/><path class="cls-14" d="M2157.6545,572.1306h18.0349a10.6117,10.6117,0,0,1,10.6117,10.6117v0a10.6117,10.6117,0,0,1-10.6117,10.6117h-18.0349a10.6116,10.6116,0,0,1-10.6116-10.6116v0A10.6116,10.6116,0,0,1,2157.6545,572.1306Z" transform="translate(1408.1453 2694.304) rotate(-85.2943)"/><path class="cls-14" d="M2161.7655,526.9205c32.062,1.2082,38.0857,15.0751,17.9941,15.6308S2140.8181,526.1312,2161.7655,526.9205Z"/><path class="cls-1" d="M2138.5609,575.28c-37.2921,27.6679-20.1324,67.8544,6.2452,56.2585"/><g class="cls-31"><ellipse class="cls-10" cx="2238.9005" cy="619.9509" rx="35.6377" ry="24.5356"/></g><path class="cls-1" d="M2191.863,620.7009c-7.1727,20.5617-25.191,19.0856-25.191,19.0856"/><polygon class="cls-1" points="1375.365 2085.094 989.262 2314.232 927.269 1942.941 1313.372 1713.803 1375.365 2085.094"/><polygon class="cls-6" points="1375.365 2085.094 1666.14 2306.636 1280.037 2535.773 989.262 2314.232 1375.365 2085.094"/><g class="cls-32"><line class="cls-17" x1="832.2933" y1="2728.546" x2="1823.1084" y2="2728.546"/><line class="cls-17" x1="832.2933" y1="2690.5358" x2="1823.1084" y2="2690.5358"/><line class="cls-17" x1="832.2933" y1="2652.5256" x2="1823.1084" y2="2652.5256"/><line class="cls-17" x1="832.2933" y1="2614.5154" x2="1823.1084" y2="2614.5154"/><line class="cls-17" x1="832.2933" y1="2576.5052" x2="1823.1084" y2="2576.5052"/><line class="cls-17" x1="832.2933" y1="2538.495" x2="1823.1084" y2="2538.495"/><line class="cls-17" x1="832.2933" y1="2500.4848" x2="1823.1084" y2="2500.4848"/><line class="cls-17" x1="832.2933" y1="2462.4746" x2="1823.1084" y2="2462.4746"/><line class="cls-17" x1="832.2933" y1="2424.4644" x2="1823.1084" y2="2424.4644"/><line class="cls-17" x1="832.2933" y1="2386.4542" x2="1823.1084" y2="2386.4542"/><line class="cls-17" x1="832.2933" y1="2348.444" x2="1823.1084" y2="2348.444"/><line class="cls-17" x1="832.2933" y1="2310.4338" x2="1823.1084" y2="2310.4338"/><line class="cls-17" x1="832.2933" y1="2272.4236" x2="1823.1084" y2="2272.4236"/><line class="cls-17" x1="832.2933" y1="2234.4134" x2="1823.1084" y2="2234.4134"/><line class="cls-17" x1="832.2933" y1="2196.4033" x2="1823.1084" y2="2196.4033"/><line class="cls-17" x1="832.2933" y1="2158.3931" x2="1823.1084" y2="2158.3931"/><line class="cls-17" x1="832.2933" y1="2120.3829" x2="1823.1084" y2="2120.3829"/><line class="cls-17" x1="832.2933" y1="2082.3727" x2="1823.1084" y2="2082.3727"/><line class="cls-17" x1="832.2933" y1="2044.3625" x2="1823.1084" y2="2044.3625"/><line class="cls-17" x1="832.2933" y1="2006.3523" x2="1823.1084" y2="2006.3523"/><line class="cls-17" x1="832.2933" y1="1968.3421" x2="1823.1084" y2="1968.3421"/><line class="cls-17" x1="832.2933" y1="1930.3319" x2="1823.1084" y2="1930.3319"/><line class="cls-17" x1="832.2933" y1="1892.3217" x2="1823.1084" y2="1892.3217"/></g><polygon class="cls-11" points="1375.365 2085.094 1666.14 2306.636 1280.037 2535.773 989.262 2314.232 1375.365 2085.094"/><polygon class="cls-2" points="1313.372 1713.803 1604.147 1935.345 1666.14 2306.636 1375.365 2085.094 1313.372 1713.803"/><path class="cls-17" d="M1171.975,1951.7771a701.796,701.796,0,0,1-76.4062,95.7873"/></svg>
```

## File: miniprogram/images/icon-meetings-not.svg
```
<svg id="图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 4000 3000"><defs><style>.cls-1,.cls-10{fill:#fff;}.cls-1,.cls-12,.cls-2,.cls-3,.cls-4,.cls-8,.cls-9{stroke:#3d3d3c;stroke-linecap:round;stroke-linejoin:round;stroke-width:4px;}.cls-2,.cls-5,.cls-6,.cls-7{fill:#22c678;}.cls-3,.cls-33{fill:#4f4f5b;}.cls-14,.cls-4{fill:#cfcfd0;}.cls-5{opacity:0.2;}.cls-7{opacity:0.14;}.cls-8{fill:#ecedee;}.cls-20,.cls-9{fill:#f5f6f7;}.cls-11{clip-path:url(#clip-path);}.cls-12{fill:none;}.cls-13{clip-path:url(#clip-path-2);}.cls-15{clip-path:url(#clip-path-3);}.cls-16{clip-path:url(#clip-path-4);}.cls-17{clip-path:url(#clip-path-5);}.cls-18{clip-path:url(#clip-path-6);}.cls-19{clip-path:url(#clip-path-7);}.cls-21{clip-path:url(#clip-path-8);}.cls-22{clip-path:url(#clip-path-9);}.cls-23{clip-path:url(#clip-path-10);}.cls-24{clip-path:url(#clip-path-11);}.cls-25{fill:#14a35b;}.cls-26{clip-path:url(#clip-path-12);}.cls-27{clip-path:url(#clip-path-13);}.cls-28{clip-path:url(#clip-path-14);}.cls-29{clip-path:url(#clip-path-15);}.cls-30{clip-path:url(#clip-path-16);}.cls-31{clip-path:url(#clip-path-17);}.cls-32{clip-path:url(#clip-path-18);}.cls-34{clip-path:url(#clip-path-19);}.cls-35{fill:#3d3d3c;}.cls-36{clip-path:url(#clip-path-20);}.cls-37{clip-path:url(#clip-path-21);}.cls-38{clip-path:url(#clip-path-22);}.cls-39{clip-path:url(#clip-path-23);}.cls-40{opacity:0.27;}.cls-41{clip-path:url(#clip-path-24);}</style><clipPath id="clip-path"><path class="cls-1" d="M1500.4122,2231.9079c.12-.933.1274-1.8247.2175-2.7474.1312-1.3465.2483-2.6879.3165-4.0112.0348-.6591.139-1.3414.1583-1.9953.03-1.0726-.0617-2.1013-.0727-3.1584-.0129-1.2406-.0154-2.4837-.0843-3.7011-.0283-.4859.0026-.995-.0341-1.4783-.0933-1.2381-.321-2.4192-.4729-3.6314-.1415-1.1294-.2483-2.2718-.44-3.3806-.0553-.32-.0682-.6591-.1274-.9795-.2612-1.3957-.6665-2.7319-1.0069-4.0966-.2554-1.0261-.4562-2.0728-.756-3.0782-.056-.1887-.0837-.3877-.1416-.5764a68.32,68.32,0,0,0-2.5659-6.9757l-48.3151-112.1111a68.5782,68.5782,0,0,1,2.7069,7.5521c.2992,1.0028.5,2.047.7547,3.0705.4189,1.6774.8377,3.3574,1.1363,5.0864.19,1.1062.2985,2.246.4388,3.3729.2117,1.6877.4092,3.3806.507,5.1149.07,1.2173.072,2.46.0849,3.7011.018,1.7006.0045,3.4116-.0849,5.1536-.0682,1.3233-.1866,2.6621-.3172,4.0087q-.25,2.57-.6518,5.195-.3378,2.1943-.7862,4.4222c-.3391,1.6929-.738,3.4013-1.1723,5.1226q-.6467,2.5665-1.43,5.1718c-.7624,2.52-1.5378,5.0476-2.5016,7.627-.045.1215-.0811.2378-.1261.3593q-1.9707,5.23-4.4962,10.6123c-1.3383,2.8456-2.7885,5.686-4.3275,8.5239-.731,1.3465-1.5423,2.6853-2.317,4.0293-.8049,1.3957-1.5744,2.794-2.4263,4.187-.8229,1.344-1.7243,2.6828-2.59,4.0242-.8957,1.3879-1.7578,2.781-2.7,4.1638q-2.6589,3.9117-5.5436,7.7925c-.1622.2171-.3417.4316-.5051.6513q-2.6744,3.5628-5.5314,7.0972c-.7052.871-1.4567,1.7317-2.1786,2.6-1.41,1.6955-2.8227,3.3909-4.2967,5.0735-.8866,1.0132-1.8157,2.0186-2.7249,3.0265-1.3962,1.5482-2.7924,3.0989-4.24,4.6368-.7921.8425-1.6079,1.6774-2.4161,2.5173q-2.3008,2.3881-4.6769,4.7582-1.3231,1.3143-2.67,2.626-2.4524,2.38-4.989,4.74c-.8532.796-1.6967,1.5947-2.5627,2.3855q-3.1636,2.8922-6.4457,5.74c-.4652.4058-.913.8167-1.3814,1.22q-4.1838,3.6054-8.5406,7.1463c-.565.4575-1.1556.9046-1.7244,1.3621-2.3529,1.8919-4.7246,3.7735-7.1476,5.6318-1.081.8271-2.1954,1.6386-3.2892,2.4605-1.9534,1.468-3.9055,2.9361-5.9,4.3809-1.2611.9149-2.5512,1.8117-3.8277,2.7163-1.8472,1.3078-3.6938,2.6156-5.5739,3.9053-1.8453,1.2665-3.7266,2.51-5.6028,3.7554-1.5693,1.0416-3.127,2.0935-4.7169,3.1222-1.9308,1.2483-3.8971,2.4734-5.8569,3.7011-1.512.946-3.0112,1.9022-4.54,2.8353q-7.5511,4.6134-15.3789,8.9969-1.7854,1.0041-3.5851,1.9953-6.1815,3.4116-12.4963,6.6785c-.6395.3308-1.2675.6746-1.91,1.0028q-6.73,3.4388-13.6069,6.6863c-1.3151.6255-2.6438,1.2251-3.9654,1.84q-4.7685,2.21-9.59,4.3369-2.3385,1.0313-4.6879,2.0367-4.5427,1.9423-9.1313,3.7967c-1.4676.5945-2.9288,1.2018-4.4022,1.7834q-5.5031,2.1749-11.06,4.2128c-.7573.2792-1.5082.579-2.2668.8555q-6.638,2.4075-13.334,4.6238c-1.2508.4161-2.5093.7987-3.7633,1.2044-3.255,1.0494-6.5146,2.0806-9.7844,3.0628-1.6124.4833-3.2273.9407-4.8423,1.4086q-4.5284,1.3065-9.07,2.5251-2.5017.6707-5.0058,1.3129-4.9539,1.2639-9.9156,2.4063c-1.3981.3256-2.795.6668-4.1931.9769q-7.3484,1.6323-14.6962,3.0111c-.8113.15-1.6207.2739-2.4314.4187q-6.84,1.2328-13.6616,2.2175c-2.5659.3748-5.1338.77-7.6945,1.0907-5.2586.6617-10.41,1.1864-15.4773,1.61-1.0353.0879-2.0377.1344-3.0646.212q-5.63.4187-11.0975.6513c-1.49.062-2.9623.1059-4.4319.1447-3.0909.0853-6.1272.1111-9.1325.1008-1.7-.0026-3.4088,0-5.0817-.0336-3.1077-.0646-6.1446-.2016-9.1545-.3644-1.3318-.0724-2.6965-.1137-4.0084-.2042-4.2645-.3-8.4429-.6849-12.4976-1.1837-.4453-.0543-.8609-.137-1.3036-.1939q-5.3969-.6978-10.54-1.6283c-1.2894-.2352-2.5241-.5143-3.7884-.77-2.5234-.5117-5.02-1.0441-7.4417-1.6412-1.3717-.3385-2.7087-.703-4.0483-1.0674q-3.3558-.915-6.5757-1.9358c-1.301-.4136-2.5942-.8349-3.86-1.2742-2.1432-.747-4.2183-1.5456-6.26-2.37-1.1144-.45-2.252-.8813-3.3368-1.3517-2.3626-1.0287-4.6274-2.1271-6.8433-3.2592-.65-.3334-1.3434-.6332-1.98-.9744q-4.205-2.2485-8.03-4.753c-.664-.4342-1.2637-.9072-1.91-1.3517-1.8434-1.269-3.6449-2.5639-5.3448-3.9208-.8326-.6642-1.61-1.3621-2.4076-2.05-1.4168-1.2173-2.7957-2.4579-4.1012-3.74-.78-.765-1.5326-1.5481-2.2725-2.3364-1.22-1.3-2.3767-2.6363-3.4866-3.9983-.657-.8064-1.321-1.61-1.9386-2.44-1.12-1.499-2.1426-3.0446-3.1309-4.6109-.4684-.7443-.978-1.4654-1.4168-2.2227a68.4218,68.4218,0,0,1-3.6783-7.2291l48.3157,112.1111a68.7112,68.7112,0,0,0,3.6783,7.2316c.4388.7547.9478,1.4784,1.4168,2.22.989,1.5662,2.012,3.1144,3.13,4.6134.619.8271,1.2817,1.6309,1.9393,2.4373,1.11,1.3621,2.2661,2.6983,3.4873,3.9983.74.7909,1.4914,1.5715,2.2712,2.3365,1.3061,1.2845,2.6843,2.5225,4.1011,3.74.7985.6875,1.5751,1.3853,2.4089,2.05,1.6967,1.3543,3.4944,2.6466,5.3339,3.9131.6512.4471,1.2547.9253,1.9238,1.362q3.8238,2.5006,8.024,4.75c.6441.3463,1.3467.6487,2.0042.9847,2.2082,1.1269,4.464,2.2227,6.8189,3.2462,1.0861.473,2.2249.9046,3.3425,1.3569,2.0429.8219,4.1179,1.6206,6.2624,2.37,1.2649.4394,2.555.8581,3.854,1.2716q3.2139,1.0235,6.5692,1.9333c1.3448.367,2.6869.734,4.0638,1.0726,2.418.5944,4.9073,1.1268,7.425,1.636,1.2682.2585,2.5054.5376,3.7987.7728q5.1615.9383,10.588,1.636c.4246.0543.8229.1318,1.2495.1861,4.0573.5014,8.2369.8839,12.504,1.1837,1.3081.0931,2.6676.1319,3.9956.2042,3.0176.1629,6.061.3024,9.177.3644,1.6645.0362,3.3618.0311,5.054.0362,1.678.0052,3.31.0569,5.0147.0336,1.3583-.0181,2.77-.0982,4.1449-.1369,1.4657-.0388,2.9327-.0828,4.4183-.1448q5.4636-.2249,11.1008-.6513c1.0275-.0775,2.0293-.1241,3.0658-.2119.3018-.0233.5913-.0336.8937-.0595q7.1631-.6164,14.5836-1.5481,2.6926-.3412,5.39-.7185c.7663-.106,1.5365-.2611,2.3034-.3722q6.8215-.9965,13.6609-2.22c.6113-.1085,1.22-.1835,1.8312-.2972.2007-.0362.4015-.0853.6016-.1215q7.3416-1.3684,14.6846-3.0058c.3667-.0827.7347-.1448,1.1015-.2275,1.0436-.2326,2.0846-.5169,3.1282-.76,3.2982-.7651,6.5937-1.5585,9.8867-2.4011.7663-.1939,1.5345-.3593,2.3-.5583.9059-.2352,1.8061-.5117,2.712-.7547q4.5321-1.2133,9.0515-2.52c1.0333-.3,2.0731-.5609,3.1057-.8659.5888-.1731,1.1717-.3747,1.76-.5505,3.2653-.9795,6.519-2.0082,9.7689-3.0575,1.0121-.3283,2.0312-.6177,3.0408-.9537.2438-.08.4851-.1706.7289-.2507q6.696-2.2215,13.338-4.629c.0965-.0336.1936-.0646.29-.0982.6524-.2378,1.2945-.5014,1.9457-.7418q5.5706-2.0547,11.0924-4.2258c.4452-.1757.8982-.336,1.3428-.5117,1.0224-.4084,2.0255-.8529,3.0446-1.2665q4.591-1.8609,9.1422-3.8019c.7754-.3334,1.5648-.6384,2.3382-.9718.79-.3437,1.5609-.7159,2.3491-1.0622q4.8246-2.1246,9.59-4.34c.9355-.4342,1.89-.8374,2.822-1.2768.3854-.1835.7585-.38,1.1427-.5609q6.8735-3.26,13.6171-6.694c.14-.07.2844-.1344.4234-.2068.49-.2507.9619-.5221,1.4509-.7753q6.334-3.2682,12.522-6.6941c.6608-.3644,1.3454-.703,2.0043-1.07.5366-.3,1.0455-.6229,1.58-.9253q7.8137-4.3963,15.3872-8.9995c.213-.1318.4388-.2507.6518-.3825,1.3093-.8012,2.5788-1.636,3.8752-2.4476,1.9586-1.2276,3.9229-2.4476,5.8525-3.6959,1.6014-1.0364,3.17-2.0935,4.7516-3.1454,1.8672-1.2406,3.7389-2.4786,5.5758-3.7373.4028-.2766.821-.5428,1.2212-.8193,1.4734-1.0184,2.9018-2.06,4.3546-3.0886,1.2765-.9046,2.5666-1.8015,3.827-2.7138,1.9959-1.4474,3.9473-2.9154,5.9-4.3834,1.0951-.8219,2.2095-1.6335,3.29-2.4606,2.3922-1.835,4.7323-3.6933,7.0563-5.5594.6016-.4833,1.2251-.9563,1.8221-1.44q4.23-3.4349,8.302-6.937c.0785-.0672.1531-.1344.2316-.2042.4684-.4032.9162-.8141,1.3814-1.22q3.2766-2.8494,6.4451-5.74c.8673-.7909,1.71-1.59,2.5633-2.3856q2.5324-2.361,4.9916-4.7427,1.3434-1.3065,2.6612-2.6181,2.3877-2.3727,4.69-4.7712c.8056-.8348,1.6188-1.67,2.409-2.51.3455-.367.71-.7288,1.0539-1.0958,1.088-1.1683,2.1142-2.352,3.1733-3.5254.9149-1.0157,1.8491-2.0263,2.7422-3.0446,1.4631-1.6723,2.8645-3.3548,4.2658-5.04.7322-.8787,1.494-1.7523,2.2088-2.6336q2.823-3.4854,5.4542-7.0016c.1879-.2482.3951-.4989.5817-.75q2.7834-3.7334,5.3532-7.4952c.0656-.0982.1222-.1939.1872-.2895.9413-1.3853,1.8035-2.7758,2.7-4.1663.8654-1.3414,1.7668-2.6777,2.5891-4.0242.8519-1.3931,1.6214-2.7914,2.4269-4.187.7258-1.2587,1.5-2.5122,2.1876-3.7735.0464-.0853.0837-.1706.1294-.2559q2.3094-4.253,4.3269-8.5239,1.0974-2.33,2.0885-4.6367,1.2962-3.0123,2.4083-5.9756c.0457-.1214.0811-.2377.1255-.3566.65-1.7394,1.2675-3.4711,1.8253-5.1821.27-.83.4279-1.6231.6763-2.4476.5263-1.7394.9992-3.4607,1.4309-5.1717.2207-.8762.5366-1.7808.7328-2.6518.1892-.8348.2722-1.6386.4389-2.4683q.4469-2.233.7868-4.4248C1500.1027,2233.544,1500.31,2232.7092,1500.4122,2231.9079Z"/></clipPath><clipPath id="clip-path-2"><polygon class="cls-1" points="1734.788 2628.707 1659.468 2553.066 1918.683 2244.734 2041.817 2368.391 1734.788 2628.707"/></clipPath><clipPath id="clip-path-3"><ellipse class="cls-1" cx="1717.881" cy="2570.0454" rx="102.665" ry="103.1009"/></clipPath><clipPath id="clip-path-4"><polygon class="cls-1" points="2869.774 2628.707 2945.095 2553.066 2685.879 2244.734 2562.745 2368.391 2869.774 2628.707"/></clipPath><clipPath id="clip-path-5"><ellipse class="cls-1" cx="2886.6813" cy="2570.0454" rx="102.665" ry="103.1009"/></clipPath><clipPath id="clip-path-6"><rect class="cls-1" x="2252.2709" y="770.3603" width="92.0498" height="171.4106"/></clipPath><clipPath id="clip-path-7"><path class="cls-4" d="M2646.4771,699.9814c-75.9113,93.0847,38.8064,201.1171,188.5567,324.2789s277.826,214.8141,353.7373,121.7294,16.0529-268.3871-133.6974-391.5489S2722.3884,606.8967,2646.4771,699.9814Z"/></clipPath><clipPath id="clip-path-8"><path class="cls-1" d="M2997.4193,707.0232c-26.0773,31.9768,1.856,56.7378,33.6976,82.9258s55.5338,43.8863,81.6111,11.91a75.0554,75.0554,0,0,0-10.4372-105.3168A74.3064,74.3064,0,0,0,2997.4193,707.0232Z"/></clipPath><clipPath id="clip-path-9"><path class="cls-4" d="M1950.1145,699.9814c75.9113,93.0847-38.8063,201.1171-188.5567,324.2789s-277.826,214.8141-353.7373,121.7294-16.0529-268.3871,133.6975-391.5489S1874.2032,606.8967,1950.1145,699.9814Z"/></clipPath><clipPath id="clip-path-10"><path class="cls-1" d="M1599.1723,707.0232c26.0773,31.9768-1.8559,56.7378-33.6975,82.9258s-55.5338,43.8863-81.6112,11.91a75.0555,75.0555,0,0,1,10.4372-105.3168A74.3064,74.3064,0,0,1,1599.1723,707.0232Z"/></clipPath><clipPath id="clip-path-11"><ellipse class="cls-2" cx="2305.2327" cy="1694.3907" rx="812.0997" ry="815.5478"/></clipPath><clipPath id="clip-path-12"><ellipse class="cls-1" cx="2305.2327" cy="1694.3907" rx="584.0619" ry="586.5417"/></clipPath><clipPath id="clip-path-13"><path class="cls-1" d="M2540.1074,1356.7074l-209.2719,271.5178a2.9,2.9,0,0,0-.3371.4619l-69.6817,135.931a2.3951,2.3951,0,0,0,.5313,3.2362l6.715,4.4757,6.7145,4.4753a2.3724,2.3724,0,0,0,3.1759-.7653l97.6641-117.28a2.9017,2.9017,0,0,0,.2928-.4914l167.86-299.12A2.2022,2.2022,0,0,0,2540.1074,1356.7074Z"/></clipPath><clipPath id="clip-path-14"><path class="cls-1" d="M2132.0515,1619.7315a38.196,38.196,0,0,0-63.9776-14.6972l-78.6394-18.0652c-2.8486-.4841-4.6515,4.1865-2.2224,5.7571l70.2165,39.8847a38.4221,38.4221,0,0,0,24.422,34.8587,38.0352,38.0352,0,0,0,32.7034-2.4094l82.1147,46.643a2.0961,2.0961,0,0,0,.5175.2421l149.57,32.9325a2.9175,2.9175,0,0,0,3.3544-2.0459l4.074-10.5539,4.0737-10.5531a2.9377,2.9377,0,0,0-1.1048-3.7819L2224.5607,1641.03a2.06,2.06,0,0,0-.5446-.1723Z"/></clipPath><clipPath id="clip-path-15"><polygon class="cls-1" points="2286.866 2599.657 2182.074 2618.621 2125.835 2366.975 2252.093 2344.126 2286.866 2599.657"/></clipPath><clipPath id="clip-path-16"><polygon class="cls-1" points="702.677 1548.365 735.849 1446.755 983.062 1516.621 943.094 1639.046 702.677 1548.365"/></clipPath><clipPath id="clip-path-17"><path class="cls-2" d="M1852.9158,1259.4973l-266.8643-130.5956L1439.0234,1620.313l-585.506-149.6c-62.6269,72.1878-37.7206,249.8719-37.7206,249.8719l696.771,150.3868c64.2024,18.8785,131.9222-15.8168,154.3865-79.0978L1812.87,1398.2955"/></clipPath><clipPath id="clip-path-18"><path class="cls-1" d="M1842.8757,619.5723,2030.1335,476.473,1771.4516,375.7644s-30.4237,6.1524-43.5077-9.0328c0,0-83.3253,35.9364-90.5593,31.7288s77.7728-88.5256,140.051-82.2414c22.2272,2.2428,254.8853,67.782,376.6228,116.3071,38.8514,15.4863,48.5952,62.5935,18.7031,90.4008L1930.362,748.42"/></clipPath><clipPath id="clip-path-19"><path class="cls-3" d="M1715.1574,672.9,1908.67,531.1508s84.2693,81.5454,110.9813,157.58L1823.6224,846.31Z"/></clipPath><clipPath id="clip-path-20"><path class="cls-3" d="M1742.6284,656.2194a36.55,36.55,0,0,0-10.552-.6387c-53.4671,4.5225-179.4023,15.1861-179.3231,15.2858h0a25.5448,25.5448,0,0,0-23.3167,26.0861l13.0558,565.3153c19.1841-2.1431,461.6483,43.2861,420.2483-24.5416-6.9838-258.5524-91.9067-421.7814-154.8464-508.0339"/></clipPath><clipPath id="clip-path-21"><path class="cls-1" d="M1574.5661,1214.3329s104.0757,66.8752,104.0757,100.1123c0,22.879-51.1115-8.381-51.1115-8.381-22.7933-6.4368-35.178-12.7092-48.6222-11.1253-42.5955,5.0183-236.5062-128.4887-347.665-196.2287-31.0737-18.9362-36.735-62.5941-11.5768-89.1868L1466.6846,748.42l93.4946,109.0221-182.7957,182.3531Z"/></clipPath><clipPath id="clip-path-22"><path class="cls-1" d="M1756.9768,570.1457c-2.5265,15.5183-27.4617,153.89-27.4617,153.89l-131.71-49.788,44.2144-142.1858Z"/></clipPath><clipPath id="clip-path-23"><path class="cls-2" d="M1557.0049,591.2632S1368.0868,381.5639,1642.6057,370.71Z"/></clipPath><clipPath id="clip-path-24"><path class="cls-1" d="M2258.1528,762.3708V870.27c-424.8262,212.7749-537.7234-143.5249-537.7234-143.5249l18.3967-5.1889c161.56,275.3719,422.644,72.5081,422.644,72.5081Z"/></clipPath></defs><title>跑步20323450918线条绿</title><path class="cls-5" d="M1043.8648,1922.9641c98.39,52.951,225.0595,85.69,365.49,91.6914.06.4725.0938.9448.1554,1.4174,42.9279,329.4529,515.2449,539.5187,1054.9489,469.195,514.0257-66.9778,903.7753-364.1434,902.613-676.8993,217.607-102.3155,353.6091-267.79,331.797-435.1889-13.8564-106.3415-89.0965-195.6024-202.761-257.6443,59.107-91.2273,86.4368-189.6521,73.5476-288.5711-42.9278-329.4529-515.2442-539.519-1054.9486-469.1953C2172.4931,402.36,1885.4142,548.9929,1732.04,734.3112c-131.93-20.3763-276.8539-22.5421-427.0138-2.9762-539.704,70.3237-942.4205,394.4066-899.4927,723.86C436.83,1695.3851,696.3856,1872.0791,1043.8648,1922.9641Z"/><circle class="cls-5" cx="1646" cy="573" r="64"/><circle class="cls-6" cx="828.5" cy="2110.5" r="79.5"/><circle class="cls-7" cx="3515" cy="2111" r="162"/><path class="cls-8" d="M1023.7037,1215.0342c-119.2517-119.7976-282.801-149.7755-365.2969-66.9567s-52.6987,247.0724,66.5531,366.87c48.1837,48.4042,103.5892,82.112,157.961,99.6511,50.3772,16.25,96.2041,46.4982,133.6159,85.9645q2.943,3.1046,5.9888,6.1648c84.691,85.0787,196.338,110.8882,249.3711,57.6475s27.37-165.37-57.3215-250.4489q-3.0456-3.06-6.1366-6.016c-39.2873-37.5838-69.3921-83.6154-85.5573-134.2119C1105.4343,1319.09,1071.8874,1263.4384,1023.7037,1215.0342Z"/><path class="cls-9" d="M844.0078,1260.6559a113.0261,113.0261,0,0,1-10.7586,74.9817"/><path class="cls-3" d="M3097.4623,1796.4908c-105.7294-106.1784-151.8715-231.9907-103.06-281.0091s174.0915-2.68,279.8209,103.4979c42.72,42.9015,75.6864,88.9959,96.6,131.9388,19.3772,39.788,48.3867,78.2627,82.9424,111.8508q2.7183,2.6421,5.4191,5.3541c75.0877,75.4066,110.5206,162.0806,79.142,193.5924s-117.6861-4.0715-192.7739-79.478q-2.7-2.7118-5.3313-5.4419c-33.4462-34.7026-71.7583-63.8354-111.3781-83.2949C3186.0818,1872.4984,3140.1823,1839.3922,3097.4623,1796.4908Z"/><path class="cls-9" d="M3176.788,1688.8966A65.7414,65.7414,0,0,1,3173.6127,1722"/><path class="cls-2" d="M1279.4857,2025.8c130.73-16.4411,203.0954,41.6629,161.6641,129.7578-41.4306,88.0933-180.9845,172.8507-311.7141,189.2918C998.6646,2361.2956,926.299,2303.19,967.73,2215.0968,1009.1609,2127.0019,1148.7146,2042.246,1279.4857,2025.8Z"/><path class="cls-10" d="M1500.4122,2231.9079c.12-.933.1274-1.8247.2175-2.7474.1312-1.3465.2483-2.6879.3165-4.0112.0348-.6591.139-1.3414.1583-1.9953.03-1.0726-.0617-2.1013-.0727-3.1584-.0129-1.2406-.0154-2.4837-.0843-3.7011-.0283-.4859.0026-.995-.0341-1.4783-.0933-1.2381-.321-2.4192-.4729-3.6314-.1415-1.1294-.2483-2.2718-.44-3.3806-.0553-.32-.0682-.6591-.1274-.9795-.2612-1.3957-.6665-2.7319-1.0069-4.0966-.2554-1.0261-.4562-2.0728-.756-3.0782-.056-.1887-.0837-.3877-.1416-.5764a68.32,68.32,0,0,0-2.5659-6.9757l-48.3151-112.1111a68.5782,68.5782,0,0,1,2.7069,7.5521c.2992,1.0028.5,2.047.7547,3.0705.4189,1.6774.8377,3.3574,1.1363,5.0864.19,1.1062.2985,2.246.4388,3.3729.2117,1.6877.4092,3.3806.507,5.1149.07,1.2173.072,2.46.0849,3.7011.018,1.7006.0045,3.4116-.0849,5.1536-.0682,1.3233-.1866,2.6621-.3172,4.0087q-.25,2.57-.6518,5.195-.3378,2.1943-.7862,4.4222c-.3391,1.6929-.738,3.4013-1.1723,5.1226q-.6467,2.5665-1.43,5.1718c-.7624,2.52-1.5378,5.0476-2.5016,7.627-.045.1215-.0811.2378-.1261.3593q-1.9707,5.23-4.4962,10.6123c-1.3383,2.8456-2.7885,5.686-4.3275,8.5239-.731,1.3465-1.5423,2.6853-2.317,4.0293-.8049,1.3957-1.5744,2.794-2.4263,4.187-.8229,1.344-1.7243,2.6828-2.59,4.0242-.8957,1.3879-1.7578,2.781-2.7,4.1638q-2.6589,3.9117-5.5436,7.7925c-.1622.2171-.3417.4316-.5051.6513q-2.6744,3.5628-5.5314,7.0972c-.7052.871-1.4567,1.7317-2.1786,2.6-1.41,1.6955-2.8227,3.3909-4.2967,5.0735-.8866,1.0132-1.8157,2.0186-2.7249,3.0265-1.3962,1.5482-2.7924,3.0989-4.24,4.6368-.7921.8425-1.6079,1.6774-2.4161,2.5173q-2.3008,2.3881-4.6769,4.7582-1.3231,1.3143-2.67,2.626-2.4524,2.38-4.989,4.74c-.8532.796-1.6967,1.5947-2.5627,2.3855q-3.1636,2.8922-6.4457,5.74c-.4652.4058-.913.8167-1.3814,1.22q-4.1838,3.6054-8.5406,7.1463c-.565.4575-1.1556.9046-1.7244,1.3621-2.3529,1.8919-4.7246,3.7735-7.1476,5.6318-1.081.8271-2.1954,1.6386-3.2892,2.4605-1.9534,1.468-3.9055,2.9361-5.9,4.3809-1.2611.9149-2.5512,1.8117-3.8277,2.7163-1.8472,1.3078-3.6938,2.6156-5.5739,3.9053-1.8453,1.2665-3.7266,2.51-5.6028,3.7554-1.5693,1.0416-3.127,2.0935-4.7169,3.1222-1.9308,1.2483-3.8971,2.4734-5.8569,3.7011-1.512.946-3.0112,1.9022-4.54,2.8353q-7.5511,4.6134-15.3789,8.9969-1.7854,1.0041-3.5851,1.9953-6.1815,3.4116-12.4963,6.6785c-.6395.3308-1.2675.6746-1.91,1.0028q-6.73,3.4388-13.6069,6.6863c-1.3151.6255-2.6438,1.2251-3.9654,1.84q-4.7685,2.21-9.59,4.3369-2.3385,1.0313-4.6879,2.0367-4.5427,1.9423-9.1313,3.7967c-1.4676.5945-2.9288,1.2018-4.4022,1.7834q-5.5031,2.1749-11.06,4.2128c-.7573.2792-1.5082.579-2.2668.8555q-6.638,2.4075-13.334,4.6238c-1.2508.4161-2.5093.7987-3.7633,1.2044-3.255,1.0494-6.5146,2.0806-9.7844,3.0628-1.6124.4833-3.2273.9407-4.8423,1.4086q-4.5284,1.3065-9.07,2.5251-2.5017.6707-5.0058,1.3129-4.9539,1.2639-9.9156,2.4063c-1.3981.3256-2.795.6668-4.1931.9769q-7.3484,1.6323-14.6962,3.0111c-.8113.15-1.6207.2739-2.4314.4187q-6.84,1.2328-13.6616,2.2175c-2.5659.3748-5.1338.77-7.6945,1.0907-5.2586.6617-10.41,1.1864-15.4773,1.61-1.0353.0879-2.0377.1344-3.0646.212q-5.63.4187-11.0975.6513c-1.49.062-2.9623.1059-4.4319.1447-3.0909.0853-6.1272.1111-9.1325.1008-1.7-.0026-3.4088,0-5.0817-.0336-3.1077-.0646-6.1446-.2016-9.1545-.3644-1.3318-.0724-2.6965-.1137-4.0084-.2042-4.2645-.3-8.4429-.6849-12.4976-1.1837-.4453-.0543-.8609-.137-1.3036-.1939q-5.3969-.6978-10.54-1.6283c-1.2894-.2352-2.5241-.5143-3.7884-.77-2.5234-.5117-5.02-1.0441-7.4417-1.6412-1.3717-.3385-2.7087-.703-4.0483-1.0674q-3.3558-.915-6.5757-1.9358c-1.301-.4136-2.5942-.8349-3.86-1.2742-2.1432-.747-4.2183-1.5456-6.26-2.37-1.1144-.45-2.252-.8813-3.3368-1.3517-2.3626-1.0287-4.6274-2.1271-6.8433-3.2592-.65-.3334-1.3434-.6332-1.98-.9744q-4.205-2.2485-8.03-4.753c-.664-.4342-1.2637-.9072-1.91-1.3517-1.8434-1.269-3.6449-2.5639-5.3448-3.9208-.8326-.6642-1.61-1.3621-2.4076-2.05-1.4168-1.2173-2.7957-2.4579-4.1012-3.74-.78-.765-1.5326-1.5481-2.2725-2.3364-1.22-1.3-2.3767-2.6363-3.4866-3.9983-.657-.8064-1.321-1.61-1.9386-2.44-1.12-1.499-2.1426-3.0446-3.1309-4.6109-.4684-.7443-.978-1.4654-1.4168-2.2227a68.4218,68.4218,0,0,1-3.6783-7.2291l48.3157,112.1111a68.7112,68.7112,0,0,0,3.6783,7.2316c.4388.7547.9478,1.4784,1.4168,2.22.989,1.5662,2.012,3.1144,3.13,4.6134.619.8271,1.2817,1.6309,1.9393,2.4373,1.11,1.3621,2.2661,2.6983,3.4873,3.9983.74.7909,1.4914,1.5715,2.2712,2.3365,1.3061,1.2845,2.6843,2.5225,4.1011,3.74.7985.6875,1.5751,1.3853,2.4089,2.05,1.6967,1.3543,3.4944,2.6466,5.3339,3.9131.6512.4471,1.2547.9253,1.9238,1.362q3.8238,2.5006,8.024,4.75c.6441.3463,1.3467.6487,2.0042.9847,2.2082,1.1269,4.464,2.2227,6.8189,3.2462,1.0861.473,2.2249.9046,3.3425,1.3569,2.0429.8219,4.1179,1.6206,6.2624,2.37,1.2649.4394,2.555.8581,3.854,1.2716q3.2139,1.0235,6.5692,1.9333c1.3448.367,2.6869.734,4.0638,1.0726,2.418.5944,4.9073,1.1268,7.425,1.636,1.2682.2585,2.5054.5376,3.7987.7728q5.1615.9383,10.588,1.636c.4246.0543.8229.1318,1.2495.1861,4.0573.5014,8.2369.8839,12.504,1.1837,1.3081.0931,2.6676.1319,3.9956.2042,3.0176.1629,6.061.3024,9.177.3644,1.6645.0362,3.3618.0311,5.054.0362,1.678.0052,3.31.0569,5.0147.0336,1.3583-.0181,2.77-.0982,4.1449-.1369,1.4657-.0388,2.9327-.0828,4.4183-.1448q5.4636-.2249,11.1008-.6513c1.0275-.0775,2.0293-.1241,3.0658-.2119.3018-.0233.5913-.0336.8937-.0595q7.1631-.6164,14.5836-1.5481,2.6926-.3412,5.39-.7185c.7663-.106,1.5365-.2611,2.3034-.3722q6.8215-.9965,13.6609-2.22c.6113-.1085,1.22-.1835,1.8312-.2972.2007-.0362.4015-.0853.6016-.1215q7.3416-1.3684,14.6846-3.0058c.3667-.0827.7347-.1448,1.1015-.2275,1.0436-.2326,2.0846-.5169,3.1282-.76,3.2982-.7651,6.5937-1.5585,9.8867-2.4011.7663-.1939,1.5345-.3593,2.3-.5583.9059-.2352,1.8061-.5117,2.712-.7547q4.5321-1.2133,9.0515-2.52c1.0333-.3,2.0731-.5609,3.1057-.8659.5888-.1731,1.1717-.3747,1.76-.5505,3.2653-.9795,6.519-2.0082,9.7689-3.0575,1.0121-.3283,2.0312-.6177,3.0408-.9537.2438-.08.4851-.1706.7289-.2507q6.696-2.2215,13.338-4.629c.0965-.0336.1936-.0646.29-.0982.6524-.2378,1.2945-.5014,1.9457-.7418q5.5706-2.0547,11.0924-4.2258c.4452-.1757.8982-.336,1.3428-.5117,1.0224-.4084,2.0255-.8529,3.0446-1.2665q4.591-1.8609,9.1422-3.8019c.7754-.3334,1.5648-.6384,2.3382-.9718.79-.3437,1.5609-.7159,2.3491-1.0622q4.8246-2.1246,9.59-4.34c.9355-.4342,1.89-.8374,2.822-1.2768.3854-.1835.7585-.38,1.1427-.5609q6.8735-3.26,13.6171-6.694c.14-.07.2844-.1344.4234-.2068.49-.2507.9619-.5221,1.4509-.7753q6.334-3.2682,12.522-6.6941c.6608-.3644,1.3454-.703,2.0043-1.07.5366-.3,1.0455-.6229,1.58-.9253q7.8137-4.3963,15.3872-8.9995c.213-.1318.4388-.2507.6518-.3825,1.3093-.8012,2.5788-1.636,3.8752-2.4476,1.9586-1.2276,3.9229-2.4476,5.8525-3.6959,1.6014-1.0364,3.17-2.0935,4.7516-3.1454,1.8672-1.2406,3.7389-2.4786,5.5758-3.7373.4028-.2766.821-.5428,1.2212-.8193,1.4734-1.0184,2.9018-2.06,4.3546-3.0886,1.2765-.9046,2.5666-1.8015,3.827-2.7138,1.9959-1.4474,3.9473-2.9154,5.9-4.3834,1.0951-.8219,2.2095-1.6335,3.29-2.4606,2.3922-1.835,4.7323-3.6933,7.0563-5.5594.6016-.4833,1.2251-.9563,1.8221-1.44q4.23-3.4349,8.302-6.937c.0785-.0672.1531-.1344.2316-.2042.4684-.4032.9162-.8141,1.3814-1.22q3.2766-2.8494,6.4451-5.74c.8673-.7909,1.71-1.59,2.5633-2.3856q2.5324-2.361,4.9916-4.7427,1.3434-1.3065,2.6612-2.6181,2.3877-2.3727,4.69-4.7712c.8056-.8348,1.6188-1.67,2.409-2.51.3455-.367.71-.7288,1.0539-1.0958,1.088-1.1683,2.1142-2.352,3.1733-3.5254.9149-1.0157,1.8491-2.0263,2.7422-3.0446,1.4631-1.6723,2.8645-3.3548,4.2658-5.04.7322-.8787,1.494-1.7523,2.2088-2.6336q2.823-3.4854,5.4542-7.0016c.1879-.2482.3951-.4989.5817-.75q2.7834-3.7334,5.3532-7.4952c.0656-.0982.1222-.1939.1872-.2895.9413-1.3853,1.8035-2.7758,2.7-4.1663.8654-1.3414,1.7668-2.6777,2.5891-4.0242.8519-1.3931,1.6214-2.7914,2.4269-4.187.7258-1.2587,1.5-2.5122,2.1876-3.7735.0464-.0853.0837-.1706.1294-.2559q2.3094-4.253,4.3269-8.5239,1.0974-2.33,2.0885-4.6367,1.2962-3.0123,2.4083-5.9756c.0457-.1214.0811-.2377.1255-.3566.65-1.7394,1.2675-3.4711,1.8253-5.1821.27-.83.4279-1.6231.6763-2.4476.5263-1.7394.9992-3.4607,1.4309-5.1717.2207-.8762.5366-1.7808.7328-2.6518.1892-.8348.2722-1.6386.4389-2.4683q.4469-2.233.7868-4.4248C1500.1027,2233.544,1500.31,2232.7092,1500.4122,2231.9079Z"/><g class="cls-11"><ellipse class="cls-1" cx="653.3122" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1808.9575" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1882.7009" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1956.4443" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2030.1876" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2103.931" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2140.8028" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1735.2141" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1772.0857" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1845.8291" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1919.5726" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="1993.316" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2067.0594" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2177.6744" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2214.5462" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2251.4178" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2288.2896" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2325.1613" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2362.033" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2398.9047" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2435.7764" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2472.6481" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2509.5197" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2546.3915" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2583.2631" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2620.1349" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2657.0067" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2693.8783" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2730.75" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2767.6217" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="653.3122" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="690.6116" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="727.911" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="765.2098" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="802.5092" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="839.8086" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="877.108" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="914.4074" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="951.7068" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="989.0062" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1026.3051" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1063.6045" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1100.9039" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1138.2033" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1175.5027" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1250.1015" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1212.8015" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1287.4003" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1324.6997" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1361.9991" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1399.2985" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1436.5979" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1473.8968" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1511.1962" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1548.4956" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1585.795" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1623.0944" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1660.3938" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1697.6932" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1734.9921" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1772.2915" cy="2804.4934" rx="4.4644" ry="4.4834"/><ellipse class="cls-1" cx="1809.5909" cy="2804.4934" rx="4.4644" ry="4.4834"/></g><path class="cls-12" d="M1500.4122,2231.9079c.12-.933.1274-1.8247.2175-2.7474.1312-1.3465.2483-2.6879.3165-4.0112.0348-.6591.139-1.3414.1583-1.9953.03-1.0726-.0617-2.1013-.0727-3.1584-.0129-1.2406-.0154-2.4837-.0843-3.7011-.0283-.4859.0026-.995-.0341-1.4783-.0933-1.2381-.321-2.4192-.4729-3.6314-.1415-1.1294-.2483-2.2718-.44-3.3806-.0553-.32-.0682-.6591-.1274-.9795-.2612-1.3957-.6665-2.7319-1.0069-4.0966-.2554-1.0261-.4562-2.0728-.756-3.0782-.056-.1887-.0837-.3877-.1416-.5764a68.32,68.32,0,0,0-2.5659-6.9757l-48.3151-112.1111a68.5782,68.5782,0,0,1,2.7069,7.5521c.2992,1.0028.5,2.047.7547,3.0705.4189,1.6774.8377,3.3574,1.1363,5.0864.19,1.1062.2985,2.246.4388,3.3729.2117,1.6877.4092,3.3806.507,5.1149.07,1.2173.072,2.46.0849,3.7011.018,1.7006.0045,3.4116-.0849,5.1536-.0682,1.3233-.1866,2.6621-.3172,4.0087q-.25,2.57-.6518,5.195-.3378,2.1943-.7862,4.4222c-.3391,1.6929-.738,3.4013-1.1723,5.1226q-.6467,2.5665-1.43,5.1718c-.7624,2.52-1.5378,5.0476-2.5016,7.627-.045.1215-.0811.2378-.1261.3593q-1.9707,5.23-4.4962,10.6123c-1.3383,2.8456-2.7885,5.686-4.3275,8.5239-.731,1.3465-1.5423,2.6853-2.317,4.0293-.8049,1.3957-1.5744,2.794-2.4263,4.187-.8229,1.344-1.7243,2.6828-2.59,4.0242-.8957,1.3879-1.7578,2.781-2.7,4.1638q-2.6589,3.9117-5.5436,7.7925c-.1622.2171-.3417.4316-.5051.6513q-2.6744,3.5628-5.5314,7.0972c-.7052.871-1.4567,1.7317-2.1786,2.6-1.41,1.6955-2.8227,3.3909-4.2967,5.0735-.8866,1.0132-1.8157,2.0186-2.7249,3.0265-1.3962,1.5482-2.7924,3.0989-4.24,4.6368-.7921.8425-1.6079,1.6774-2.4161,2.5173q-2.3008,2.3881-4.6769,4.7582-1.3231,1.3143-2.67,2.626-2.4524,2.38-4.989,4.74c-.8532.796-1.6967,1.5947-2.5627,2.3855q-3.1636,2.8922-6.4457,5.74c-.4652.4058-.913.8167-1.3814,1.22q-4.1838,3.6054-8.5406,7.1463c-.565.4575-1.1556.9046-1.7244,1.3621-2.3529,1.8919-4.7246,3.7735-7.1476,5.6318-1.081.8271-2.1954,1.6386-3.2892,2.4605-1.9534,1.468-3.9055,2.9361-5.9,4.3809-1.2611.9149-2.5512,1.8117-3.8277,2.7163-1.8472,1.3078-3.6938,2.6156-5.5739,3.9053-1.8453,1.2665-3.7266,2.51-5.6028,3.7554-1.5693,1.0416-3.127,2.0935-4.7169,3.1222-1.9308,1.2483-3.8971,2.4734-5.8569,3.7011-1.512.946-3.0112,1.9022-4.54,2.8353q-7.5511,4.6134-15.3789,8.9969-1.7854,1.0041-3.5851,1.9953-6.1815,3.4116-12.4963,6.6785c-.6395.3308-1.2675.6746-1.91,1.0028q-6.73,3.4388-13.6069,6.6863c-1.3151.6255-2.6438,1.2251-3.9654,1.84q-4.7685,2.21-9.59,4.3369-2.3385,1.0313-4.6879,2.0367-4.5427,1.9423-9.1313,3.7967c-1.4676.5945-2.9288,1.2018-4.4022,1.7834q-5.5031,2.1749-11.06,4.2128c-.7573.2792-1.5082.579-2.2668.8555q-6.638,2.4075-13.334,4.6238c-1.2508.4161-2.5093.7987-3.7633,1.2044-3.255,1.0494-6.5146,2.0806-9.7844,3.0628-1.6124.4833-3.2273.9407-4.8423,1.4086q-4.5284,1.3065-9.07,2.5251-2.5017.6707-5.0058,1.3129-4.9539,1.2639-9.9156,2.4063c-1.3981.3256-2.795.6668-4.1931.9769q-7.3484,1.6323-14.6962,3.0111c-.8113.15-1.6207.2739-2.4314.4187q-6.84,1.2328-13.6616,2.2175c-2.5659.3748-5.1338.77-7.6945,1.0907-5.2586.6617-10.41,1.1864-15.4773,1.61-1.0353.0879-2.0377.1344-3.0646.212q-5.63.4187-11.0975.6513c-1.49.062-2.9623.1059-4.4319.1447-3.0909.0853-6.1272.1111-9.1325.1008-1.7-.0026-3.4088,0-5.0817-.0336-3.1077-.0646-6.1446-.2016-9.1545-.3644-1.3318-.0724-2.6965-.1137-4.0084-.2042-4.2645-.3-8.4429-.6849-12.4976-1.1837-.4453-.0543-.8609-.137-1.3036-.1939q-5.3969-.6978-10.54-1.6283c-1.2894-.2352-2.5241-.5143-3.7884-.77-2.5234-.5117-5.02-1.0441-7.4417-1.6412-1.3717-.3385-2.7087-.703-4.0483-1.0674q-3.3558-.915-6.5757-1.9358c-1.301-.4136-2.5942-.8349-3.86-1.2742-2.1432-.747-4.2183-1.5456-6.26-2.37-1.1144-.45-2.252-.8813-3.3368-1.3517-2.3626-1.0287-4.6274-2.1271-6.8433-3.2592-.65-.3334-1.3434-.6332-1.98-.9744q-4.205-2.2485-8.03-4.753c-.664-.4342-1.2637-.9072-1.91-1.3517-1.8434-1.269-3.6449-2.5639-5.3448-3.9208-.8326-.6642-1.61-1.3621-2.4076-2.05-1.4168-1.2173-2.7957-2.4579-4.1012-3.74-.78-.765-1.5326-1.5481-2.2725-2.3364-1.22-1.3-2.3767-2.6363-3.4866-3.9983-.657-.8064-1.321-1.61-1.9386-2.44-1.12-1.499-2.1426-3.0446-3.1309-4.6109-.4684-.7443-.978-1.4654-1.4168-2.2227a68.4218,68.4218,0,0,1-3.6783-7.2291l48.3157,112.1111a68.7112,68.7112,0,0,0,3.6783,7.2316c.4388.7547.9478,1.4784,1.4168,2.22.989,1.5662,2.012,3.1144,3.13,4.6134.619.8271,1.2817,1.6309,1.9393,2.4373,1.11,1.3621,2.2661,2.6983,3.4873,3.9983.74.7909,1.4914,1.5715,2.2712,2.3365,1.3061,1.2845,2.6843,2.5225,4.1011,3.74.7985.6875,1.5751,1.3853,2.4089,2.05,1.6967,1.3543,3.4944,2.6466,5.3339,3.9131.6512.4471,1.2547.9253,1.9238,1.362q3.8238,2.5006,8.024,4.75c.6441.3463,1.3467.6487,2.0042.9847,2.2082,1.1269,4.464,2.2227,6.8189,3.2462,1.0861.473,2.2249.9046,3.3425,1.3569,2.0429.8219,4.1179,1.6206,6.2624,2.37,1.2649.4394,2.555.8581,3.854,1.2716q3.2139,1.0235,6.5692,1.9333c1.3448.367,2.6869.734,4.0638,1.0726,2.418.5944,4.9073,1.1268,7.425,1.636,1.2682.2585,2.5054.5376,3.7987.7728q5.1615.9383,10.588,1.636c.4246.0543.8229.1318,1.2495.1861,4.0573.5014,8.2369.8839,12.504,1.1837,1.3081.0931,2.6676.1319,3.9956.2042,3.0176.1629,6.061.3024,9.177.3644,1.6645.0362,3.3618.0311,5.054.0362,1.678.0052,3.31.0569,5.0147.0336,1.3583-.0181,2.77-.0982,4.1449-.1369,1.4657-.0388,2.9327-.0828,4.4183-.1448q5.4636-.2249,11.1008-.6513c1.0275-.0775,2.0293-.1241,3.0658-.2119.3018-.0233.5913-.0336.8937-.0595q7.1631-.6164,14.5836-1.5481,2.6926-.3412,5.39-.7185c.7663-.106,1.5365-.2611,2.3034-.3722q6.8215-.9965,13.6609-2.22c.6113-.1085,1.22-.1835,1.8312-.2972.2007-.0362.4015-.0853.6016-.1215q7.3416-1.3684,14.6846-3.0058c.3667-.0827.7347-.1448,1.1015-.2275,1.0436-.2326,2.0846-.5169,3.1282-.76,3.2982-.7651,6.5937-1.5585,9.8867-2.4011.7663-.1939,1.5345-.3593,2.3-.5583.9059-.2352,1.8061-.5117,2.712-.7547q4.5321-1.2133,9.0515-2.52c1.0333-.3,2.0731-.5609,3.1057-.8659.5888-.1731,1.1717-.3747,1.76-.5505,3.2653-.9795,6.519-2.0082,9.7689-3.0575,1.0121-.3283,2.0312-.6177,3.0408-.9537.2438-.08.4851-.1706.7289-.2507q6.696-2.2215,13.338-4.629c.0965-.0336.1936-.0646.29-.0982.6524-.2378,1.2945-.5014,1.9457-.7418q5.5706-2.0547,11.0924-4.2258c.4452-.1757.8982-.336,1.3428-.5117,1.0224-.4084,2.0255-.8529,3.0446-1.2665q4.591-1.8609,9.1422-3.8019c.7754-.3334,1.5648-.6384,2.3382-.9718.79-.3437,1.5609-.7159,2.3491-1.0622q4.8246-2.1246,9.59-4.34c.9355-.4342,1.89-.8374,2.822-1.2768.3854-.1835.7585-.38,1.1427-.5609q6.8735-3.26,13.6171-6.694c.14-.07.2844-.1344.4234-.2068.49-.2507.9619-.5221,1.4509-.7753q6.334-3.2682,12.522-6.6941c.6608-.3644,1.3454-.703,2.0043-1.07.5366-.3,1.0455-.6229,1.58-.9253q7.8137-4.3963,15.3872-8.9995c.213-.1318.4388-.2507.6518-.3825,1.3093-.8012,2.5788-1.636,3.8752-2.4476,1.9586-1.2276,3.9229-2.4476,5.8525-3.6959,1.6014-1.0364,3.17-2.0935,4.7516-3.1454,1.8672-1.2406,3.7389-2.4786,5.5758-3.7373.4028-.2766.821-.5428,1.2212-.8193,1.4734-1.0184,2.9018-2.06,4.3546-3.0886,1.2765-.9046,2.5666-1.8015,3.827-2.7138,1.9959-1.4474,3.9473-2.9154,5.9-4.3834,1.0951-.8219,2.2095-1.6335,3.29-2.4606,2.3922-1.835,4.7323-3.6933,7.0563-5.5594.6016-.4833,1.2251-.9563,1.8221-1.44q4.23-3.4349,8.302-6.937c.0785-.0672.1531-.1344.2316-.2042.4684-.4032.9162-.8141,1.3814-1.22q3.2766-2.8494,6.4451-5.74c.8673-.7909,1.71-1.59,2.5633-2.3856q2.5324-2.361,4.9916-4.7427,1.3434-1.3065,2.6612-2.6181,2.3877-2.3727,4.69-4.7712c.8056-.8348,1.6188-1.67,2.409-2.51.3455-.367.71-.7288,1.0539-1.0958,1.088-1.1683,2.1142-2.352,3.1733-3.5254.9149-1.0157,1.8491-2.0263,2.7422-3.0446,1.4631-1.6723,2.8645-3.3548,4.2658-5.04.7322-.8787,1.494-1.7523,2.2088-2.6336q2.823-3.4854,5.4542-7.0016c.1879-.2482.3951-.4989.5817-.75q2.7834-3.7334,5.3532-7.4952c.0656-.0982.1222-.1939.1872-.2895.9413-1.3853,1.8035-2.7758,2.7-4.1663.8654-1.3414,1.7668-2.6777,2.5891-4.0242.8519-1.3931,1.6214-2.7914,2.4269-4.187.7258-1.2587,1.5-2.5122,2.1876-3.7735.0464-.0853.0837-.1706.1294-.2559q2.3094-4.253,4.3269-8.5239,1.0974-2.33,2.0885-4.6367,1.2962-3.0123,2.4083-5.9756c.0457-.1214.0811-.2377.1255-.3566.65-1.7394,1.2675-3.4711,1.8253-5.1821.27-.83.4279-1.6231.6763-2.4476.5263-1.7394.9992-3.4607,1.4309-5.1717.2207-.8762.5366-1.7808.7328-2.6518.1892-.8348.2722-1.6386.4389-2.4683q.4469-2.233.7868-4.4248C1500.1027,2233.544,1500.31,2232.7092,1500.4122,2231.9079Z"/><path class="cls-9" d="M1217.7145,2177.02a31.3724,31.3724,0,0,1-10.6939,21.2076"/><polygon class="cls-10" points="1734.788 2628.707 1659.468 2553.066 1918.683 2244.734 2041.817 2368.391 1734.788 2628.707"/><g class="cls-13"><path class="cls-14" d="M1777.6146,2356.9237c143.6465-18.6684,54.0787,113.708,170.6859,190.0791s104.7774-156.1364,104.7774-156.1364l-273.7733-95.0395"/></g><polygon class="cls-12" points="1734.788 2628.707 1659.468 2553.066 1918.683 2244.734 2041.817 2368.391 1734.788 2628.707"/><ellipse class="cls-10" cx="1717.881" cy="2570.0454" rx="102.665" ry="103.1009"/><g class="cls-15"><path class="cls-14" d="M1669.4572,2453.66c1.8526,254.0145,201.1052,219.4859,201.1052,219.4859l-314.3324,19.81-15.21-162.9249"/></g><ellipse class="cls-12" cx="1717.881" cy="2570.0454" rx="102.665" ry="103.1009"/><polygon class="cls-10" points="2869.774 2628.707 2945.095 2553.066 2685.879 2244.734 2562.745 2368.391 2869.774 2628.707"/><g class="cls-16"><path class="cls-14" d="M2826.9476,2356.9237c-143.6465-18.6684-54.0787,113.708-170.6858,190.0791s-104.7775-156.1364-104.7775-156.1364l273.7734-95.0395"/></g><polygon class="cls-12" points="2869.774 2628.707 2945.095 2553.066 2685.879 2244.734 2562.745 2368.391 2869.774 2628.707"/><ellipse class="cls-10" cx="2886.6813" cy="2570.0454" rx="102.665" ry="103.1009"/><g class="cls-17"><path class="cls-14" d="M2935.105,2453.66C2933.2525,2707.6749,2734,2673.1463,2734,2673.1463l314.3324,19.81,15.21-162.9249"/></g><ellipse class="cls-12" cx="2886.6813" cy="2570.0454" rx="102.665" ry="103.1009"/><rect class="cls-10" x="2252.2709" y="770.3603" width="92.0498" height="171.4106"/><g class="cls-18"><path class="cls-14" d="M2230.8405,903.3282c-7.6048-96.4356,139.1047-81.4625,112.4879-108.1923S2209.573,757.42,2209.573,757.42V770.36"/></g><rect class="cls-12" x="2252.2709" y="770.3603" width="92.0498" height="171.4106"/><path class="cls-8" d="M2387.0979,757.5661c-.0066,40.4211-39.73,46.38-88.7305,46.3724s-88.7218-5.9806-88.7152-46.4017,39.7347-73.1824,88.735-73.1743S2387.1046,717.1449,2387.0979,757.5661Z"/><path class="cls-1" d="M2738.2541,1142.9344h0a35.5767,35.5767,0,0,0,50.2108-5.0183l295.72-362.6206a35.9352,35.9352,0,0,0-4.9972-50.4239h0a35.5768,35.5768,0,0,0-50.2108,5.0184l-295.72,362.6206A35.9353,35.9353,0,0,0,2738.2541,1142.9344Z"/><path class="cls-14" d="M2646.4771,699.9814c-75.9113,93.0847,38.8064,201.1171,188.5567,324.2789s277.826,214.8141,353.7373,121.7294,16.0529-268.3871-133.6974-391.5489S2722.3884,606.8967,2646.4771,699.9814Z"/><g class="cls-19"><path class="cls-20" d="M2948.1485,601.029c-248.14,84.0841,22.6257,538.9027,273.5361,634.4528S3495.43,1059.2413,3495.43,1059.2413L3152.9039,608.6731"/></g><path class="cls-12" d="M2646.4771,699.9814c-75.9113,93.0847,38.8064,201.1171,188.5567,324.2789s277.826,214.8141,353.7373,121.7294,16.0529-268.3871-133.6974-391.5489S2722.3884,606.8967,2646.4771,699.9814Z"/><path class="cls-10" d="M2997.4193,707.0232c-26.0773,31.9768,1.856,56.7378,33.6976,82.9258s55.5338,43.8863,81.6111,11.91a75.0554,75.0554,0,0,0-10.4372-105.3168A74.3064,74.3064,0,0,0,2997.4193,707.0232Z"/><g class="cls-21"><ellipse class="cls-14" cx="3074.8841" cy="665.6499" rx="78.0198" ry="56.9768"/></g><path class="cls-12" d="M2997.4193,707.0232c-26.0773,31.9768,1.856,56.7378,33.6976,82.9258s55.5338,43.8863,81.6111,11.91a75.0554,75.0554,0,0,0-10.4372-105.3168A74.3064,74.3064,0,0,0,2997.4193,707.0232Z"/><path class="cls-1" d="M1858.3375,1142.9344h0a35.5767,35.5767,0,0,1-50.2107-5.0183l-295.72-362.6206a35.9352,35.9352,0,0,1,4.9972-50.4239h0a35.5768,35.5768,0,0,1,50.2108,5.0184l295.72,362.6206A35.9353,35.9353,0,0,1,1858.3375,1142.9344Z"/><path class="cls-14" d="M1950.1145,699.9814c75.9113,93.0847-38.8063,201.1171-188.5567,324.2789s-277.826,214.8141-353.7373,121.7294-16.0529-268.3871,133.6975-391.5489S1874.2032,606.8967,1950.1145,699.9814Z"/><g class="cls-22"><path class="cls-20" d="M1648.4432,601.029c248.14,84.0841-22.6258,538.9027-273.5362,634.4528s-273.7454-176.2405-273.7454-176.2405l342.5261-450.5682"/></g><path class="cls-12" d="M1950.1145,699.9814c75.9113,93.0847-38.8063,201.1171-188.5567,324.2789s-277.826,214.8141-353.7373,121.7294-16.0529-268.3871,133.6975-391.5489S1874.2032,606.8967,1950.1145,699.9814Z"/><path class="cls-10" d="M1599.1723,707.0232c26.0773,31.9768-1.8559,56.7378-33.6975,82.9258s-55.5338,43.8863-81.6112,11.91a75.0555,75.0555,0,0,1,10.4372-105.3168A74.3064,74.3064,0,0,1,1599.1723,707.0232Z"/><g class="cls-23"><ellipse class="cls-14" cx="1521.7075" cy="665.6499" rx="78.0198" ry="56.9768"/></g><path class="cls-12" d="M1599.1723,707.0232c26.0773,31.9768-1.8559,56.7378-33.6975,82.9258s-55.5338,43.8863-81.6112,11.91a75.0555,75.0555,0,0,1,10.4372-105.3168A74.3064,74.3064,0,0,1,1599.1723,707.0232Z"/><ellipse class="cls-6" cx="2305.2327" cy="1694.3907" rx="812.0997" ry="815.5478"/><g class="cls-24"><path class="cls-25" d="M2986.3366,1250.1553c509.1213,1404.6155-1817.9522,1422.991-1817.9522,1422.991l1444.9149,187.0842,874.5538-290.1851L3609.53,1739.059"/><path class="cls-25" d="M1323.0156,2112.5364c-212.9348-682.248,669.9379-1214.6882,982.2171-1233.6935s-584.0619-42.4085-584.0619-42.4085l-211.38,77.3389-267.8932,104.1114"/><path class="cls-1" d="M2586.1258,913.7733c194.9281,119.7131,353.2908,253.89,400.2108,336.382s116.2059-68.7235,116.2059-68.7235l-361.89-280.6493"/></g><ellipse class="cls-12" cx="2305.2327" cy="1694.3907" rx="812.0997" ry="815.5478"/><ellipse class="cls-1" cx="2305.2327" cy="1694.3907" rx="648.6751" ry="651.4293"/><ellipse class="cls-10" cx="2305.2327" cy="1694.3907" rx="584.0619" ry="586.5417"/><g class="cls-26"><path class="cls-14" d="M2957.3906,1409.6763l-1134.89,1171.9906,700.455,236.3064L3208.06,2390.6751l-35.56-815.0042s-194.6606-260.1142-196.1575-249.7051"/></g><ellipse class="cls-12" cx="2305.2327" cy="1694.3907" rx="584.0619" ry="586.5417"/><rect class="cls-1" x="2289.4235" y="1146.6406" width="17.7446" height="69.5825" rx="8.8723"/><rect class="cls-1" x="2289.4235" y="2172.5583" width="17.7446" height="69.5825" rx="8.8723"/><rect class="cls-1" x="2800.176" y="1659.7465" width="17.8199" height="69.2883" rx="8.8911" transform="translate(4503.4766 -1114.6952) rotate(90)"/><rect class="cls-1" x="1778.5957" y="1659.7465" width="17.8199" height="69.2883" rx="8.8911" transform="translate(3481.8964 -93.115) rotate(90)"/><rect class="cls-1" x="2289.4235" y="1146.6406" width="17.7446" height="69.5825" rx="8.8723"/><rect class="cls-1" x="2289.4235" y="2172.5583" width="17.7446" height="69.5825" rx="8.8723"/><rect class="cls-1" x="2800.176" y="1659.7465" width="17.8199" height="69.2883" rx="8.8911" transform="translate(4503.4766 -1114.6952) rotate(90)"/><rect class="cls-1" x="1778.5957" y="1659.7465" width="17.8199" height="69.2883" rx="8.8911" transform="translate(3481.8964 -93.115) rotate(90)"/><path class="cls-1" d="M2060.2228,1280.2854h0a8.8518,8.8518,0,0,1-12.12-3.2612l-25.7719-44.8278a8.9306,8.9306,0,0,1,3.2475-12.1712h0a8.8518,8.8518,0,0,1,12.12,3.2613l25.7719,44.8277A8.9305,8.9305,0,0,1,2060.2228,1280.2854Z"/><path class="cls-1" d="M2571.013,2168.7562h0a8.8518,8.8518,0,0,1-12.12-3.2613l-25.7719-44.8277a8.9305,8.9305,0,0,1,3.2475-12.1712h0a8.852,8.852,0,0,1,12.12,3.2612l25.7718,44.8278A8.9306,8.9306,0,0,1,2571.013,2168.7562Z"/><path class="cls-1" d="M2710.65,1455.3069h0a8.9305,8.9305,0,0,1,3.2475-12.1712l44.6382-25.8813a8.8518,8.8518,0,0,1,12.12,3.2612h0a8.9307,8.9307,0,0,1-3.2475,12.1712l-44.6382,25.8813A8.8519,8.8519,0,0,1,2710.65,1455.3069Z"/><path class="cls-1" d="M1825.9359,1968.2658h0a8.9306,8.9306,0,0,1,3.2475-12.1712l44.6382-25.8813a8.8518,8.8518,0,0,1,12.12,3.2613h0a8.9305,8.9305,0,0,1-3.2475,12.1712l-44.6382,25.8813A8.8518,8.8518,0,0,1,1825.9359,1968.2658Z"/><path class="cls-1" d="M1885.9413,1455.3069h0a8.8518,8.8518,0,0,1-12.12,3.2613l-44.6382-25.8813a8.9306,8.9306,0,0,1-3.2475-12.1712h0a8.8518,8.8518,0,0,1,12.12-3.2612l44.6382,25.8813A8.9305,8.9305,0,0,1,1885.9413,1455.3069Z"/><path class="cls-1" d="M2770.6558,1968.2657h0a8.8518,8.8518,0,0,1-12.12,3.2612l-44.6382-25.8813a8.9305,8.9305,0,0,1-3.2475-12.1712h0a8.8519,8.8519,0,0,1,12.12-3.2613l44.6382,25.8813A8.9307,8.9307,0,0,1,2770.6558,1968.2657Z"/><path class="cls-1" d="M2536.3688,1280.2854h0a8.9305,8.9305,0,0,1-3.2475-12.1712l25.7719-44.8277a8.8518,8.8518,0,0,1,12.12-3.2613h0a8.9306,8.9306,0,0,1,3.2474,12.1712l-25.7718,44.8278A8.852,8.852,0,0,1,2536.3688,1280.2854Z"/><path class="cls-1" d="M2025.5787,2168.7562h0a8.9306,8.9306,0,0,1-3.2475-12.1712l25.7719-44.8278a8.8518,8.8518,0,0,1,12.12-3.2612h0a8.9305,8.9305,0,0,1,3.2475,12.1712l-25.7719,44.8277A8.8518,8.8518,0,0,1,2025.5787,2168.7562Z"/><ellipse class="cls-1" cx="2298.2958" cy="1712.2699" rx="26.6759" ry="26.7891"/><polygon class="cls-3" points="2305.449 1821.947 2287.828 1821.947 2294.518 1266.755 2298.759 1266.755 2305.449 1821.947"/><path class="cls-10" d="M2540.1074,1356.7074l-209.2719,271.5178a2.9,2.9,0,0,0-.3371.4619l-69.6817,135.931a2.3951,2.3951,0,0,0,.5313,3.2362l6.715,4.4757,6.7145,4.4753a2.3724,2.3724,0,0,0,3.1759-.7653l97.6641-117.28a2.9017,2.9017,0,0,0,.2928-.4914l167.86-299.12A2.2022,2.2022,0,0,0,2540.1074,1356.7074Z"/><g class="cls-27"><path class="cls-1" d="M2298.2958,1640.108c38.0615-9.3456,90.6615,54.2827,90.6615,54.2827H2316.55"/></g><path class="cls-12" d="M2540.1074,1356.7074l-209.2719,271.5178a2.9,2.9,0,0,0-.3371.4619l-69.6817,135.931a2.3951,2.3951,0,0,0,.5313,3.2362l6.715,4.4757,6.7145,4.4753a2.3724,2.3724,0,0,0,3.1759-.7653l97.6641-117.28a2.9017,2.9017,0,0,0,.2928-.4914l167.86-299.12A2.2022,2.2022,0,0,0,2540.1074,1356.7074Z"/><path class="cls-10" d="M2132.0515,1619.7315a38.196,38.196,0,0,0-63.9776-14.6972l-78.6394-18.0652c-2.8486-.4841-4.6515,4.1865-2.2224,5.7571l70.2165,39.8847a38.4221,38.4221,0,0,0,24.422,34.8587,38.0352,38.0352,0,0,0,32.7034-2.4094l82.1147,46.643a2.0961,2.0961,0,0,0,.5175.2421l149.57,32.9325a2.9175,2.9175,0,0,0,3.3544-2.0459l4.074-10.5539,4.0737-10.5531a2.9377,2.9377,0,0,0-1.1048-3.7819L2224.5607,1641.03a2.06,2.06,0,0,0-.5446-.1723Z"/><g class="cls-28"><path class="cls-14" d="M2153.208,1739.059c51.9662,3.0778,71.2062-90.3927,102.779-85.0251s4.83,110.5842,4.83,110.5842l-93.6665-2.1157"/></g><path class="cls-12" d="M2132.0515,1619.7315a38.196,38.196,0,0,0-63.9776-14.6972l-78.6394-18.0652c-2.8486-.4841-4.6515,4.1865-2.2224,5.7571l70.2165,39.8847a38.4221,38.4221,0,0,0,24.422,34.8587,38.0352,38.0352,0,0,0,32.7034-2.4094l82.1147,46.643a2.0961,2.0961,0,0,0,.5175.2421l149.57,32.9325a2.9175,2.9175,0,0,0,3.3544-2.0459l4.074-10.5539,4.0737-10.5531a2.9377,2.9377,0,0,0-1.1048-3.7819L2224.5607,1641.03a2.06,2.06,0,0,0-.5446-.1723Z"/><ellipse class="cls-1" cx="2298.2958" cy="1712.2699" rx="71.8568" ry="72.1619"/><ellipse class="cls-3" cx="2298.2958" cy="1712.2699" rx="37.8876" ry="38.0484"/><path class="cls-9" d="M1716.7723,2165.8777a176.0183,176.0183,0,0,0,41.73,61.9676"/><path class="cls-9" d="M2872.553,839.2225a173.98,173.98,0,0,0,51.9458,76.951"/><polygon class="cls-10" points="2286.866 2599.657 2182.074 2618.621 2125.835 2366.975 2252.093 2344.126 2286.866 2599.657"/><g class="cls-29"><path class="cls-14" d="M2289.1313,2454.5936c-77.1816,116.7646-124.6987,35.9077-126.0186,172.4367s-95.0343,7.9531-95.0343,7.9531l3.1476-181.985,177.6815-31.4244h98.994"/></g><polygon class="cls-12" points="2286.866 2599.657 2182.074 2618.621 2125.835 2366.975 2252.093 2344.126 2286.866 2599.657"/><path class="cls-3" d="M2188.0771,2701.9118l-7.5645-93.6916,125.98-38.8238s-36.013,98.5417,111.5312,111.4261c159.4979,13.9283,127.847,66.1593,127.847,66.1593l-319.859-8.9438A39.2252,39.2252,0,0,1,2188.0771,2701.9118Z"/><path class="cls-6" d="M2070.13,2516.3435c146.4124-2.5548,219.0009-61.75,219.0009-61.75l-64.6389-609.6126a142.0939,142.0939,0,0,0-10.8359-36.3309l-305.9332-572.78-350.876,67.7087,418.1965,631.5881S2070.9143,2513.7887,2070.13,2516.3435Z"/><path class="cls-12" d="M2070.13,2516.3435c146.4124-2.5548,219.0009-61.75,219.0009-61.75l-64.6389-609.6126a142.0939,142.0939,0,0,0-10.8359-36.3309l-305.9332-572.78-350.876,67.7087,418.1965,631.5881S2070.9143,2513.7887,2070.13,2516.3435Z"/><polygon class="cls-10" points="702.677 1548.365 735.849 1446.755 983.062 1516.621 943.094 1639.046 702.677 1548.365"/><g class="cls-30"><path class="cls-14" d="M662.3637,1570.5875C804.915,1602.4,789.75,1529.4963,842.87,1489.7305s22.7619,88.81,22.7619,88.81l-68.6359,83.508-94.3181-42.4168"/></g><polygon class="cls-12" points="702.677 1548.365 735.849 1446.755 983.062 1516.621 943.094 1639.046 702.677 1548.365"/><path class="cls-3" d="M673.1219,1408.4989l80.2995,26.9087L719.8659,1580.086s-69.2317-78.6424-150.6224,45.6142c-87.9849,134.3237-118.7117,81.5391-118.7117,81.5391l156.0889-273.382A56.112,56.112,0,0,1,673.1219,1408.4989Z"/><path class="cls-6" d="M1852.9158,1259.4973l-266.8643-130.5956L1439.0234,1620.313l-585.506-149.6c-62.6269,72.1878-37.7206,249.8719-37.7206,249.8719l696.771,150.3868c64.2024,18.8785,131.9222-15.8168,154.3865-79.0978L1812.87,1398.2955"/><g class="cls-31"><path class="cls-25" d="M1243.2684,1535.9338c403.8544,195.0348,124.164-220.4773,553.6822-252.6375s-348.0038-154.0035-348.0038-154.0035l-89.6654,125.82-95.0342,191.6423"/></g><path class="cls-12" d="M1852.9158,1259.4973l-266.8643-130.5956L1439.0234,1620.313l-585.506-149.6c-62.6269,72.1878-37.7206,249.8719-37.7206,249.8719l696.771,150.3868c64.2024,18.8785,131.9222-15.8168,154.3865-79.0978L1812.87,1398.2955"/><path class="cls-10" d="M1842.8757,619.5723,2030.1335,476.473,1771.4516,375.7644s-30.4237,6.1524-43.5077-9.0328c0,0-83.3253,35.9364-90.5593,31.7288s77.7728-88.5256,140.051-82.2414c22.2272,2.2428,254.8853,67.782,376.6228,116.3071,38.8514,15.4863,48.5952,62.5935,18.7031,90.4008L1930.362,748.42"/><g class="cls-32"><path class="cls-14" d="M2137.5132,587.3883c-124.7576,117.8431-155.2736-25.4871-205.1219-31.2934s14.77,154.3029,14.77,154.3029l83.6005,21.9576,78.4557-67.1643Z"/></g><path class="cls-12" d="M1842.8757,619.5723,2030.1335,476.473,1771.4516,375.7644s-30.4237,6.1524-43.5077-9.0328c0,0-83.3253,35.9364-90.5593,31.7288s77.7728-88.5256,140.051-82.2414c22.2272,2.2428,254.8853,67.782,376.6228,116.3071,38.8514,15.4863,48.5952,62.5935,18.7031,90.4008L1930.362,748.42"/><path class="cls-33" d="M1715.1574,672.9,1908.67,531.1508s84.2693,81.5454,110.9813,157.58L1823.6224,846.31Z"/><g class="cls-34"><path class="cls-35" d="M1977.7036,898.6573c-25.8279-77.4308-169.8093-168.9649-169.8093-168.9649l34.6136,136.9138,168.4031,18.8312"/></g><path class="cls-12" d="M1715.1574,672.9,1908.67,531.1508s84.2693,81.5454,110.9813,157.58L1823.6224,846.31Z"/><path class="cls-33" d="M1742.6284,656.2194a36.55,36.55,0,0,0-10.552-.6387c-53.4671,4.5225-179.4023,15.1861-179.3231,15.2858h0a25.5448,25.5448,0,0,0-23.3167,26.0861l13.0558,565.3153c19.1841-2.1431,461.6483,43.2861,420.2483-24.5416-6.9838-258.5524-91.9067-421.7814-154.8464-508.0339"/><g class="cls-36"><path class="cls-35" d="M1610.2184,886.5781c-185.6862,442.7339,355.2547,376.6337,355.2547,376.6337l-411.8768,124.0211-210.93-31.0244,17.06-480.4831,164.1781-28.1144"/></g><path class="cls-12" d="M1742.6284,656.2194a36.55,36.55,0,0,0-10.552-.6387c-53.4671,4.5225-179.4023,15.1861-179.3231,15.2858h0a25.5448,25.5448,0,0,0-23.3167,26.0861l13.0558,565.3153c19.1841-2.1431,461.6483,43.2861,420.2483-24.5416-6.9838-258.5524-91.9067-421.7814-154.8464-508.0339"/><path class="cls-10" d="M1574.5661,1214.3329s104.0757,66.8752,104.0757,100.1123c0,22.879-51.1115-8.381-51.1115-8.381-22.7933-6.4368-35.178-12.7092-48.6222-11.1253-42.5955,5.0183-236.5062-128.4887-347.665-196.2287-31.0737-18.9362-36.735-62.5941-11.5768-89.1868L1466.6846,748.42l93.4946,109.0221-182.7957,182.3531Z"/><g class="cls-37"><path class="cls-14" d="M1230.77,980.3467c99.0344-96.8716,142.7745,41.5541,180.2338,33.2059s-76.8676-154.0837-76.8676-154.0837l-84.0738-25.0754"/></g><path class="cls-12" d="M1574.5661,1214.3329s104.0757,66.8752,104.0757,100.1123c0,22.879-51.1115-8.381-51.1115-8.381-22.7933-6.4368-35.178-12.7092-48.6222-11.1253-42.5955,5.0183-236.5062-128.4887-347.665-196.2287-31.0737-18.9362-36.735-62.5941-11.5768-89.1868L1466.6846,748.42l93.4946,109.0221-182.7957,182.3531Z"/><path class="cls-33" d="M1540.166,675.5009,1311.9508,879.2786s29.92,118.2936,116.7177,140.4424l181.55-133.1429"/><path class="cls-12" d="M1540.166,675.5009,1311.9508,879.2786s29.92,118.2936,116.7177,140.4424l181.55-133.1429"/><path class="cls-10" d="M1756.9768,570.1457c-2.5265,15.5183-27.4617,153.89-27.4617,153.89l-131.71-49.788,44.2144-142.1858Z"/><g class="cls-38"><path class="cls-14" d="M1790.0715,652.8483c-65.3821-34.92-129.1225-92.0886-129.1225-92.0886l96.0278-20.8208,17.7441,71.589,9.6652,27.8037"/></g><path class="cls-12" d="M1756.9768,570.1457c-2.5265,15.5183-27.4617,153.89-27.4617,153.89l-131.71-49.788,44.2144-142.1858Z"/><path class="cls-1" d="M1714.4865,602.7515c30.5136-16.313,92.1577-51.5162,97.0928-72.1328,17.7123-73.9935-152.5059-147.9863-161.98-147.9863S1531.4831,484.1482,1537.8,511.9519c3.3363,14.6859,47.7323,41.6387,88.8818,63.8024"/><path class="cls-3" d="M1646.4407,568.8525c.6316,33.6231-39.793,70.4791-39.793,70.4791s-102.9955-99.6947-79.1649-129.535,125.9753-176.5878,137.7658-170.5529,74.3911,43.3887-19.8606,117.1008c0,0,42.9511,40.52,2.5265,82.7645S1646.4407,568.8525,1646.4407,568.8525Z"/><path class="cls-1" d="M1685.1167,525.3149c-123.3189-32.623-76.2143,77.4366-23.17,62.0734"/><path class="cls-2" d="M1618.7771,399.4115s24.3769-145.6676,66.34-146.53-66.34,153.6149-66.34,153.6149Z"/><path class="cls-6" d="M1557.0049,591.2632S1368.0868,381.5639,1642.6057,370.71Z"/><g class="cls-39"><path class="cls-1" d="M1518.5383,370.71c-77.17,67.81,79.2665,173.3126,79.2665,173.3126l66.9191-103.6163s-45.9468-40.9946-171.0932-19.99"/></g><path class="cls-12" d="M1557.0049,591.2632S1368.0868,381.5639,1642.6057,370.71Z"/><path class="cls-33" d="M1739.8775,470.388h0a8.8455,8.8455,0,0,1-12.3562,2.1661l-12.2555-8.6494a8.931,8.931,0,0,1-2.1569-12.4086h0a8.8454,8.8454,0,0,1,12.3561-2.1661l12.2556,8.6494A8.9309,8.9309,0,0,1,1739.8775,470.388Z"/><path class="cls-33" d="M1693.3717,427.8785c-16.3767,21.326-10.719,32.6384.3841,19.9777S1704.0713,413.9454,1693.3717,427.8785Z"/><path class="cls-1" d="M1737.0322,434.8423c35.8854-14.74,50.8471,17.6752,31.1453,31.5649"/><g class="cls-40"><ellipse class="cls-14" cx="1715.1574" cy="515.2107" rx="29.7867" ry="29.9132"/></g><path class="cls-1" d="M1726.7283,500.0417a32.3742,32.3742,0,0,0,29.5255-14.749"/><path class="cls-9" d="M1647.9315,551.17c-4.8776-1.26-10.3857,2.8071-10.6421,7.8583s4.8112,9.66,9.7911,8.9043"/><path class="cls-10" d="M2258.1528,762.3708V870.27c-424.8262,212.7749-537.7234-143.5249-537.7234-143.5249l18.3967-5.1889c161.56,275.3719,422.644,72.5081,422.644,72.5081Z"/><g class="cls-41"><path class="cls-14" d="M2086.8728,788.8979c154.3393,1.2916,192.7418.1738,149.7465,91.792s89.4793-71.1261,89.4793-71.1261l-33.44-120.8333"/></g><path class="cls-12" d="M2258.1528,762.3708V870.27c-424.8262,212.7749-537.7234-143.5249-537.7234-143.5249l18.3967-5.1889c161.56,275.3719,422.644,72.5081,422.644,72.5081Z"/><path class="cls-1" d="M1572.557,675.5009s27.75-26.7793,32.6853-25.17,124.2728,73.7055,124.2728,73.7055-19.0173,3.651-18.0527,24.3845Z"/><path class="cls-1" d="M1761.3011,737.1862c-5.49-16.0564-24.2689-13.15-24.2689-13.15l7.9167-85.92h31.9725S1763.4712,743.5334,1761.3011,737.1862Z"/><ellipse class="cls-10" cx="1743.4658" cy="825.4906" rx="8.2471" ry="8.2821"/><ellipse class="cls-10" cx="1764.5009" cy="912.5238" rx="8.2471" ry="8.2821"/><path class="cls-9" d="M2031.23,480.0872a19.5117,19.5117,0,0,1,15.3311-5.3357"/><path class="cls-9" d="M1344.3217,1048.3339l35.9583-7.9957"/><path class="cls-9" d="M1440.7123,1617.9384a164.623,164.623,0,0,1,19.6313,34.7119"/><path class="cls-9" d="M1995.7219,1896.8805a160.03,160.03,0,0,1-13.391,52.3508"/><path class="cls-12" d="M628.9462,1624.2032q1.4911-31.8015,6.11-63.3511a526.6728,526.6728,0,0,1,20.2229,61.4037"/><path class="cls-12" d="M2335.8906,2607.2672l-16.0579,33.5405a393.2733,393.2733,0,0,1,84.0581-21.8024"/><path class="cls-9" d="M2008.5371,1677.8181l-20.3631,15.78"/><path class="cls-9" d="M999.6967,1641.0141l-5.1447,30.999"/><path class="cls-9" d="M2126.2224,861.0082a48.1051,48.1051,0,0,0,25.9032,15.2291"/><path class="cls-12" d="M1636.9519,1157.9478c76.5639,61.9978,183.3152,14.2078,183.3152,14.2078"/></svg>
```

## File: miniprogram/pages/meeting/detail.js
```javascript
"use strict";
// pages/meeting/detail.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    meeting_id: 0,
    owner: false,
    joined: false,
    attendees_show: false,
    no_icon_attendee_num: 0,
    info: {}
  },

  refresh: function () {
    if (this.data.meeting_id <= 0) {
        return;
    }
    app.api.api_meeting_info({ meeting_id: this.data.meeting_id }).then(res => {
        console.log("API响应数据:", res); // 这里打印API响应的数据

        let no_icon_attendee_num = 0;
        for (let i in res.attendees) {
            if (!res.attendees[i].avatarurl) {
                no_icon_attendee_num += 1;
            }
        }
        this.setData({
            info: res,
            no_icon_attendee_num: no_icon_attendee_num,
            owner: res.is_manager
            
        });

        app.userInfo().then(userRes => {
            let joined = false;
            for (let i in this.data.info.attendees) {
                if (this.data.info.attendees[i].id == userRes.id) {
                    joined = true;
                    break;
                }
            }
            this.setData({
                joined: joined
            });
        });
    });
},

  attendees_show_change: function(){
    this.setData({ attendees_show: !this.data.attendees_show})
  },
  home: function () {
    app.gotoHome()
  },
  join: function(){

    app.api.api_meeting_join({ meeting_id: this.data.meeting_id }).then(res => {
      this.refresh()
    })
  },
  
  leave: function(){
    wx.showModal({
      title: '提示',
      content: '确定要退出该预约吗？',
      success: sm => {
        if (sm.confirm) {
			app.api.api_meeting_leave({ meeting_id: this.data.meeting_id }).then(res => {
			  this.refresh()
			})
		}
      }
    })
  },
  
  edit: function(){
    wx.navigateTo({
      url: 'edit?meeting_id=' + this.data.meeting_id,
    })
  },
  del: function(){
    wx.showModal({
      title: '提示',
      content: '确定要取消该会议吗？',
      success: sm => {
        if (sm.confirm) {
          app.api.api_meeting_cancel({ meeting_id: this.data.meeting_id }).then(res => {
            wx.navigateBack()
          })
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({ show_home: getCurrentPages().length == 1})
    let meeting_id = options.meeting_id
    if(meeting_id){
      this.setData({ meeting_id: meeting_id})
    }else{
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      wx.navigateBack()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refresh()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    let title = '会议'
    if (this.data.info.name){
      title += " - " + this.data.info.name
    }
    return {
      title: title,
      path: '/pages/meeting/detail?meeting_id='+this.data.meeting_id
    }
  }
})
```

## File: miniprogram/pages/meeting/detail.json
```json
{
  "usingComponents": {},
  "navigationBarTitleText": "会议详情"
}
```

## File: miniprogram/pages/meeting/detail.wxml
```
<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<!--pages/meeting/detail.wxml-->
<view class="page">
  <view class="page__hd">
    <view class='weui-cell'>
      <view class='weui-cell__bd'>
        <view class="page__title">{{info.name}}</view>
        <view class="page__desc">{{info.description}}</view>
      </view>


      <button class="join-meeting-btn" bindtap='join' wx:if="{{!joined}}">参与</button>
      <button class="leave-meeting-btn" bindtap='leave' wx:else>退出</button>

    </view>
  </view>
  <view class="page_bd page__bd_spacing body-with-footer">
    <view class="weui-cells weui-cells_after-title">
        <view class="weui-cell">
            <view class="weui-cell__bd">会议室</view>
            <view class="weui-cell__ft">{{info.room.name}}</view>
        </view>
        <view class="weui-cell">
            <view class="weui-cell__bd">时间</view>
            <view class="weui-cell__ft">{{info.date}} {{timeUtils.formatTime(info.start_time)}} - {{timeUtils.formatTime(info.end_time)}}</view>
        </view>
        <view class="weui-cell">
            <view class="weui-cell__bd">发起人</view>
            <view class="weui-cell__ft"><image src="{{info.user.avatarurl}}" style="margin-right: 10rpx;vertical-align: middle;width:48rpx; height: 48rpx;"></image>{{info.user.nickname}}</view>
        </view>
        <view class="weui-cell" bindtap="attendees_show_change">
          <view class="weui-cell__bd">参与人</view>
          <view class="weui-cell__ft attendees {{attendees_show ? 'attendees_detail' : 'attendees_info'}}">
            <view wx:for="{{info.attendees}}" wx:key="id" wx:if="{{item.avatarurl}}">
              <image src="{{item.avatarurl}}" style="margin-right: 1rpx;vertical-align: middle;width:32rpx; height: 32rpx;"></image>
              <block wx:if="{{attendees_show && item.nickname}}">
                {{item.nickname}}
              </block>
            </view>
            <view wx:if="{{!attendees_show}}">
              ({{info.attendees.length}})
            </view>
            <view wx:elif="{{no_icon_attendee_num > 0}}">
              及其他{{no_icon_attendee_num}}人
            </view>
          </view>
        </view>
    </view>
  </view>
  
  <view class="weui-footer-detail">
    <block wx:if="{{owner}}">
        <button class="footer-edit" bindtap='edit'>修改预约</button>
        <button class="footer-del" bindtap='del'>取消预约</button>
    </block>
  </view>
  
</view>
```

## File: miniprogram/pages/meeting/detail.wxss
```
/* pages/meeting/detail.wxss */
.attendees_detail view{
  float: none;
}
.attendees_info view{
  float: left;
}

.join-meeting-btn,
.leave-meeting-btn {
  padding: 0px 10px; /* 调整内边距以缩小按钮 */
  width: 81px;
  height: 30px;
  border-radius: 16px; /* 圆角 */
  border: none; /* 去除边框 */
  font-size: 12px; /* 文字大小 */
  transition: background-color 0.8s, color 0.3s; /* 过渡动画 */
}

.leave-meeting-btn {
  background-color: #44c97b; /* 淡绿色 */
  color: #ffffff; /* 淡绿色文字 */
}

.join-meeting-btn {
  background-color: #181818; /* 关注按钮背景色 */
  color: #ffffff; /* 关注按钮文字颜色 */
}

.weui-footer-detail {
    position: fixed; /* 固定定位 */
    bottom: 0; /* 底部对齐 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度占满整个屏幕 */
    background-color: #ffffff; /* 底部导航栏背景色 */
    display: flex; /* 使用 Flexbox 布局 */
    justify-content: space-around; /* 水平居中并平均分配空间 */
    padding: 20px 0; /* 上下内边距 */
  }

.footer-edit {
    padding: 5px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    width: 120px;
    height: 45px;
    border: 1px solid #242424; /* 边框 */
    font-size: 14px; /* 文字大小 */
    background-color: #242424; /* 透明背景 */
    color: #ffffff; /* 文字颜色 */
}

.footer-del {
    padding: 5px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    width: 120px;
    height: 45px;
    border: 1px solid #181818; /* 边框 */
    font-size: 14px; /* 文字大小 */
    background-color: #24242400;
    color: #272727; /* 文字颜色 */
}
```

## File: miniprogram/pages/meeting/edit.js
```javascript
"use strict";
// pages/meeting/edit.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user_info: {},
    meeting_id: 0,
    room_id: 0,
    date: "",
    start_time: "",
    end_time: "",
    room: {},
    name: "",
    description: ""
  },
  bindKeyInput(e) {
    this.data[e.currentTarget.dataset.obj] = e.detail.value
  },
  onGetUserInfo: function (e) {
    app.onGetUserInfo(e).then(res => {
      this.setData({ user_info: res })
    })
  },
  refresh: function(){
    if(this.data.meeting_id > 0){
      app.api.api_meeting_info({meeting_id: this.data.meeting_id}).then(res => {
        this.setData({
          date: res.date,
          start_time: res.start_time,
          end_time: res.end_time,
          room: res.room,
          name: res.name,
          description: res.description
        })
      })
    }else{
      app.api.api_meeting_room_info({ room_id: this.data.room_id }).then(res => {
        this.setData({ room: res })
      })
    }
  },
  save: function () {
    if(!this.data.name.trim()){
      wx.showToast({
        icon: 'none',
        title: '请输入名称',
      })
      return
    }
    wx.showLoading({
      mask: true,
      title: '加载中...',
    })
    if (this.data.meeting_id <= 0) {
      app.api.api_meeting_reserve({
        room_id: this.data.room_id, 
        name: this.data.name, 
        description: this.data.description, 
        date: this.data.date, 
        start_time: this.data.start_time, 
        end_time: this.data.end_time
      }).then(res => {
        wx.hideLoading()
        wx.redirectTo({
          url: "detail?meeting_id="+res.id
        })
      }).catch(res => {
        wx.hideLoading()
      })
    }else{
      app.api.api_meeting_edit({
        meeting_id: this.data.meeting_id,
        name: this.data.name,
        description: this.data.description
      }).then(res => {
        wx.hideLoading()
        wx.navigateBack()
      }).catch(res => {
        wx.hideLoading()
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.userInfo().then(res => {
      this.setData({ user_info: res })
    })
    let meeting_id = options.meeting_id
    if(meeting_id){
      this.setData({meeting_id: parseInt(meeting_id)})
      return
    }
    let room_id = options.room_id
    if (room_id){
      room_id = parseInt(room_id)
    }else{
      room_id = 0
    }
    let start_time = options.start_time
    let end_time = options.end_time
    let date = options.date
    if (room_id <= 0 || !start_time || !end_time || !date){
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    this.setData({ room_id: room_id, start_time: start_time, end_time: end_time, date: date })
    return
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refresh()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})
```

## File: miniprogram/pages/meeting/edit.json
```json
{
  "usingComponents": {},
  "navigationBarTitleText": "会议编辑"
}
```

## File: miniprogram/pages/meeting/edit.wxml
```
<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<!--pages/meeting/edit.wxml-->
<view class="page">
  <view class="page_bd page__bd_spacing weui-cells weui-cells_after-title">
    <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
            <view class="weui-label">名称</view>
        </view>
        <view class="weui-cell__bd">
            <input class="weui-input" placeholder="请输入名称" value="{{name}}" data-obj="name" bindinput='bindKeyInput'/>
        </view>
    </view>
    <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
            <view class="weui-label">描述</view>
        </view>
        <view class="weui-cell__bd">
            <input class="weui-input" placeholder="请输入描述" value="{{description}}" data-obj="description" bindinput='bindKeyInput'/>
        </view>
    </view>
    <view class="weui-cells weui-cells_after-title">
        <view class="weui-cell">
            <view class="weui-cell__bd">会议室</view>
            <view class="weui-cell__ft">{{room.name}}</view>
        </view>
        <view class="weui-cell">
            <view class="weui-cell__bd">时间</view>
            <view class="weui-cell__ft">{{date}} {{timeUtils.formatTime(start_time)}} - {{timeUtils.formatTime(end_time)}}</view>
        </view>
    </view>
    
  </view>

  <block wx:if="{{!user_info.avatarurl}}">
      <view class="weui-cells__title">需要通过微信授权获得你的微信名和头像</view>
      <button class="save-btn" open-type="getUserInfo" lang="zh_CN" bindgetuserinfo="onGetUserInfo">授权登录</button>
    </block>
    <button class="save-btn" bindtap="save" wx:else>保存</button>
    
</view>
```

## File: miniprogram/pages/meeting/edit.wxss
```
/* pages/meeting/edit.wxss */

.save-btn {
    padding: 2px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    margin-top: 8%;
    width: 240px;
    height: 45px;
    font-size: 16px; /* 文字大小 */
    background-color: #242424; /* 透明背景 */
    color: #ffffff; /* 文字颜色 */
}
```

## File: miniprogram/pages/meeting/meeting.js
```javascript
// pages/meeting/meeting.js
Page({

    /**
     * 页面的初始数据
     */
    data: {

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})
```

## File: miniprogram/pages/meeting/meeting.json
```json
{
    "usingComponents": {}
}
```

## File: miniprogram/pages/meeting/meeting.wxml
```
<!--pages/meeting/meeting.wxml-->
<text>pages/meeting/meeting.wxml</text>
```

## File: miniprogram/pages/meeting/meeting.wxss
```
/* pages/meeting/meeting.wxss */
```

## File: miniprogram/pages/meeting/reserve.js
```javascript
"use strict";
// pages/meeting/reserve.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    room_ids: "",
    loading: true,
    select:{
      selected: false,
      click: false,
      start: "",
      end: "",
      room: {}
    }
  },
  date_select_change: function (e) {
    this.setData({
      select: {selected: false, click: false, start: "", end: "", room: {}}, td_data: {}
    })
    this.refresh()
  },
  title_click: function(e){
    wx.showActionSheet({
      itemList: ['查看详情', '从列表移出'],
      success: r => {
        if (r.tapIndex == 0){
          wx.navigateTo({
            url: '../room/detail?room_id=' + e.detail.title_id + '&date=' + this.selectComponent("#date_select").data.select_date,
          })
        } else if (r.tapIndex == 1){
          let select_rooms = this.data.room_ids.split(",")
          const index = select_rooms.indexOf(e.detail.title_id)
          if (index >= 0) {
            select_rooms.splice(index, 1)
            const room_ids_str = select_rooms.join(",")
            wx.setStorageSync('RESERVE_ROOM_IDS', room_ids_str)
            this.setData({ room_ids: room_ids_str })
            this.refresh()
          }
        }
      }
    })
  },
  title_label_click: function () {
    const select_rooms = this.data.room_ids.split(",")
    if(select_rooms.length >= 5){
      wx.showToast({
        title: '超过上限',
        icon: 'none'
      })
      return
    }
    app.api.api_meeting_follow_rooms().then(res => {
      let room_ids = []
      let room_names = []
      for(let i in res){
        let room = res[i]
        if (select_rooms.indexOf(room.id.toString()) < 0){
          room_ids.push(room.id.toString())
          room_names.push(room.name)
        }
      }
      if(room_ids.length == 0){
        wx.showToast({
          title: '已没有关注的会议室',
          icon: 'none'
        })
        return
      }
      wx.showActionSheet({
        itemList: room_names,
        success: r => {
          room_ids.push(room_ids[r.tapIndex])
          let room_ids_str = select_rooms
          if (room_ids_str != ''){
            room_ids_str += ","
          }
          room_ids_str += room_ids[r.tapIndex]
          wx.setStorageSync('RESERVE_ROOM_IDS', room_ids_str)
          this.setData({ room_ids: room_ids_str })
          this.refresh()
        }
      })
    })
  },
  data_click: function (e) {
    const meeting_id = e.detail.data_id
    if(meeting_id != null){
      wx.navigateTo({
        url: '../meeting/detail?meeting_id=' + meeting_id,
      })
      return
    }
    const room_id = e.detail.title_id
    const time = e.detail.label_id
    if (!this.data.td_data || !this.data.td_data[room_id]){
      return
    }
    const td_data = this.data.td_data[room_id][time]
    if (td_data.expire || td_data.meeting_status != 0){
      return 
    }
    if (td_data.selected_status != 0){
      // if (this.data.select.click) {
        this.data.select.click = false
        this.data.select.selected = false
        this.setData({ select: this.data.select })
        this.check_td_data()
      // }
      return
    }
    if (!this.data.select.selected || this.data.select.room.id != room_id) {
      this.data.select.click = false
      this.data.select.selected = false
    }
    this.data.select.room = this.data.rooms.find(r => {return r.id == room_id})
    if (!this.data.select.click) {
      this.data.select.selected = true
      this.data.select.click = true
      this.data.select.start = time
      this.data.select.end = time
    } else {
      this.data.select.click = false
      if (this.data.select.start == time){
        this.data.select.selected = false
      } else if (app.time.parseTime(this.data.select.start).value() > app.time.parseTime(time).value()){
        this.data.select.start = time
      }else{
        this.data.select.end = time
      }
    }
    this.data.select.end_real = app.time.valueToTime(app.time.parseTime(this.data.select.end).value() + 30 * 60).string(2)
    this.setData({ select: this.data.select })
    this.check_td_data()
  },
  check_td_data: function(){
    this.data.td_data = app.meetings.getTdData(
      this.data.rooms,
      this.data.meetings,
      this.data.time_range,
      this.data.select,
      this.selectComponent("#date_select").data.select_date
    )
    this.selectComponent("#time_table").set_data({
      titles: this.data.rooms, labels: this.data.time_range, td_data: this.data.td_data
    })
    this.setData({ select: this.data.select, loading: false })
  },
  reserve: function(){
    wx.navigateTo({
      url: 'edit?room_id=' + this.data.select.room.id + "&start_time=" + this.data.select.start + "&end_time=" + this.data.select.end_real + "&date=" + this.selectComponent("#date_select").data.select_date
    })
  },
  refresh: function () {
    if (!this.data.room_ids) {
      this.setData({ meetings: [], rooms: [], td_data: {}, loading: false })
      return
    }
    app.api.api_meeting_room_meetings({
      room_ids: this.data.room_ids,
      date: this.selectComponent("#date_select").data.select_date
    }).then(res => {

      const start_time = app.time.parseTime(res.start_time).value()
      const end_time = app.time.parseTime(res.end_time).value()
      let time_range = []
      for (let time = start_time; time <= end_time; time += 1800) {
        const t = app.time.valueToTime(time)
        const id = t.string(2)
        time_range.push({ id: id, text: t.minute == 0 ? id : "", data: t })
      }
      this.data.meetings = res.meetings
      this.data.rooms = res.rooms
      this.data.time_range = time_range
      this.selectComponent("#date_select").setDateRange(res.start_date, res.end_date)
      this.check_td_data()
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let room_ids = options.room_ids
    if(!room_ids){
      try {
        room_ids = wx.getStorageSync('RESERVE_ROOM_IDS')
      } catch (e) {
        room_ids = ""
      }
    }
    if(!room_ids){
      room_ids = ""
    }
    if (options.date) {
      this.selectComponent("#date_select").setData({select_date: options.date})
    }
    this.setData({ room_ids: room_ids })
    this.refresh()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refresh()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})
```

## File: miniprogram/pages/meeting/reserve.json
```json
{
  "usingComponents": {
    "date_select": "/components/date_select",
    "time_table": "/components/time_table"
  },
  "navigationBarTitleText": "会议预约"
}
```

## File: miniprogram/pages/meeting/reserve.wxml
```
<!--pages/meeting/reserve.wxml-->
<view class='page' hidden="{{loading}}">
  <date_select id="date_select" bindchange="date_select_change" start_date="{{start_date}}" end_date="{{end_date}}"></date_select>
  <view class='page_table'>
    <time_table
      id="time_table"
      bindtitle_label_click="title_label_click"
      bindtitle_click="title_click"
      binddata_click="data_click"
      no_title_desc="请先添加会议室"
      title_label="添加"></time_table>
  </view>
  <view class="footer">
    <button class="weui-btn" bindtap="reserve" wx:if="{{select.selected}}">预约时间({{select.start}}-{{select.end_real}})</button>
  </view>
</view>
```

## File: miniprogram/pages/meeting/reserve.wxss
```
/* pages/meeting/reserve.wxss */

page{
  height: 100%
}
.page{
  height: 100%
}
.footer{
  margin-bottom: 30rpx;
  background-color: #00000000; /* 透明背景 */
}
.weui-btn {
    padding: 2px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    margin-top: 8%;
    width: 240px;
    height: 45px;
    font-size: 16px; /* 文字大小 */
    background-color: #242424; /* 透明背景 */
    color: #ffffff; /* 文字颜色 */
}

.page_table{
  position: absolute;
  top: 162rpx;
  bottom: 130rpx;
  left: 20rpx;
  right:20rpx;
}
#time_table {
    
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}
```

## File: miniprogram/pages/my/my.json
```json
{
  "usingComponents": {}
}
```

## File: miniprogram/pages/room/detail.js
```javascript
"use strict";
// pages/room/detail.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    room_id: 0,
    show_home: false,
    owner: false,
    info: {},
    meetings: [],
    description: [],
  },
  refresh: function () {

    app.api.api_meeting_room_info({ room_id: this.data.room_id }).then(res => {
                console.log("API响应数据:", res); // 这里打印API响应的数据
      this.setData({ info: res })
      app.userInfo().then(res => {
        this.setData({
          owner: res.id == this.data.info.create_user
        })
      })
    })

    app.api.api_meeting_room_meetings({
      room_ids: this.data.room_id,
      date: this.selectComponent("#date_select").data.select_date
    }).then(res => {
      this.selectComponent("#date_select").setDateRange(res.start_date, res.end_date)
      this.setData({
        meetings: res.meetings,
        history_limit_start: res.history_start_date,
        history_limit_end: res.history_end_date
      })
    })
  },
  
  hide_qrcode: function () {
    this.setData({
      show_qr_code: false
    })
  },
  show_qrcode: function(){
    this.setData({
      show_qr_code:true
    })
    // wx.previewImage({
    //   current: this.data.info.qr_code,
    //   urls: [this.data.info.qr_code]
    // })
  },
  home: function(){
    app.gotoHome()
  },
  date_select_change: function (e) {
    this.refresh()
  },
  reserve: function(){
    wx.navigateTo({
      url: '../meeting/reserve?room_ids=' + this.data.room_id + "&date=" + this.selectComponent("#date_select").data.select_date
    })
  },
  history: function(){
    wx.navigateTo({
      url: '../room/history?room_id=' + this.data.room_id
    })
  },
  unfollow: function(){
    wx.showModal({
        title: '提示',
        content: '取消关注后需重新扫码，确定要取消吗？',
        success: sm => {
          if (sm.confirm) {
            app.api.api_meeting_room_un_follow({ room_id: this.data.room_id}).then(res => {
            wx.navigateBack()
            this.data.info.is_follow = false
            this.setData({info: this.data.info})
            })
          }   
        }
    })
},

  follow: function(){
    app.api.api_meeting_room_follow({ room_id: this.data.room_id }).then(res => {
      this.data.info.is_follow = true
      this.setData({ info: this.data.info })
    })
  },
  edit: function(){
    wx.navigateTo({
      url: 'edit?room_id=' + this.data.room_id
    })
  },
  del: function() {
    wx.showModal({
      title: '提示',
      content: '确定要删除吗？',
      success: sm => {
        if (sm.confirm) {
          app.api.api_meeting_room_delete({ room_id: this.data.room_id }).then(res => {
            wx.navigateBack()
          })
        }
      }
    })
  },
  detail: function (e) {
    wx.navigateTo({
      url: '../meeting/detail?meeting_id=' + e.currentTarget.id
    })
  },
  formatNumber: function(n) {
    n = n.toString()
    return n[1] ? n : '0' + n
  },
  dateId: function(date){
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    return [year, month, day].map(this.formatNumber).join('-')
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let now = app.nowDate()
    this.setData({
      show_home: getCurrentPages().length == 1,
      history_end: this.dateId(now),
      history_start: this.dateId(new Date(now.setDate(now.getDate() - 7)))

    })
    const scene = decodeURIComponent(options.scene)
    let room_id = ''
    scene.split("&").map(s => {
      if(s.substring(0, s.indexOf("=")) == "room_id"){
        room_id = s.substring(s.indexOf("=") + 1, s.length)
      }
    })
    let from_scene = false
    if(room_id){
      from_scene = true
    }else{
      room_id = options.room_id
    }
    if(options.date){
      this.selectComponent("#date_select").setData({ select_date: options.date })
    }
    if (room_id) {
      this.setData({ room_id: parseInt(room_id) })
      if(from_scene){
        this.follow()
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refresh()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    let title = '会议室'
    if (this.data.info.name) {
      title += " - " + this.data.info.name
    }
    return {
      title: title,
      path: '/pages/room/detail?room_id=' + this.data.room_id
    }
  }
})
```

## File: miniprogram/pages/room/detail.json
```json
{
  "usingComponents": {
    "date_select": "/components/date_select"
  },
  "navigationBarTitleText": "会议室详情"
}
```

## File: miniprogram/pages/room/detail.wxml
```
<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<!--pages/room/detail.wxml-->
<view class="page__hd">
  <view class='weui-cell'>
    <view class='weui-cell__bd'>
        <view class="weui-flex__item" bindtap='show_qrcode'>
        <image src="/images/icon-qrcode.png" class="qrcode-icon"></image>
      </view>
      <image class="background-image" src="/images/bg-meetings.svg"></image>
      <view class="page__title">{{info.name}}</view>
      <view class="page__desc">{{info.description}}</view>
  </view>
</view>


    <!-- 这里放置背景图片下的其他内容 -->
    <!-- 修改开始：确保所有视图标签正确闭合 -->
    <view class="weui-flex">

    <view class="weui-flex__item">
    <button class="follow-btn" bindtap='follow' wx:if="{{!info.is_follow}}">关注</button>
    <button class="unfollow-btn" bindtap='unfollow' wx:else>取消关注</button>
    </view>

      <view class="weui-flex__item">
        <button class="history-btn" bindtap='history' wx:if="{{owner}}">查看历史</button>
      </view>
      <block wx:if="{{owner}}">
        <view class="weui-ico-edit">
          <image src="/images/icon-edit.png" class="icon-edit" bindtap='edit'></image>
        </view>
      </block>
    </view>

</view>

<date_select id="date_select" bindchange="date_select_change"></date_select>

  
<view class="container">
  <block wx:if="{{meetings.length > 0}}">
    <block wx:for="{{meetings}}" wx:key="id">
      <view class='meeting-item' id="{{item.id}}" bindtap='detail'>
        <view class="weui-form-preview__item">
          <view class="weui-form-preview__label">
            <image src="/images/ic_clock.png" class="icon"></image>预定时间</view>
          <view class="weui-form-preview-time">{{timeUtils.formatTime(item.start_time)}} - {{timeUtils.formatTime(item.end_time)}}</view>
        </view>
        <view class="weui-form-preview__bd">
          <view class="weui-form-preview__item">
            <view class="weui-form-preview__label">
              <image src="/images/ic_daipingjia.png" class="icon"></image>用途</view>
            <view class="weui-form-preview__value">{{item.name}}</view>
          </view>
          <view class="weui-form-preview__item">
            <view class="weui-form-preview__label">
              <image src="/images/ic_edit.png" class="icon"></image>备注</view>
            <view class="weui-form-preview__value">{{item.description}}</view>
          </view>
        </view>
      </view>
    </block>
  </block>
  <block wx:else>
    <image class="not-room-image" src="/images/icon-meetings-not.svg"></image>
    <view class="placeholder">暂无人预约~</view>
  </block>
</view>


  <view class="weui-footer-detail">
        <button class="footer-reserve" bindtap='reserve'>预约</button>
        <button class="footer-share" open-type="share">分享</button>
  </view>
  
<modal class="modal" hidden="{{!show_qr_code}}" no-cancel bindconfirm="hide_qrcode" confirmText="确定" title="{{info.name}} 专属二维码">
  <view class="dew">  
    <image class="qr_code" src="{{info.qr_code}}" mode="aspectFit" show-menu-by-longpress></image>
    <view class="vedw">长按二维码可保存到相册</view>   
  </view>
</modal>
```

## File: miniprogram/pages/room/detail.wxss
```
/* pages/room/detail.wxss */

.weui-cell {
top: 0px;
margin: 0px;
}

.background-image {

    height: 160px;
    width: 210px;
    margin-left: 150px;
    margin-bottom: -150px;
}
.weui-ico-edit {
    border-radius: 18px; /* 圆角 */
    padding: 0; /* 移除内边距 */
    width: 42px;
    height: 30px;
    border: 1px solid #ffffff; /* 设置边框 */
    display: flex; /* 使用 Flexbox 布局 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
}

.icon-edit {
    width: 20px; /* 图标宽度 */
    height: 20px; /* 图标高度 */
}


  .weui-flex{
    background-color: #37C470; /* 淡绿色 */
    border-radius: 18px;
    display: flex;
    justify-content: space-between; /* 两端对齐，确保左边的查看历史和右边的按钮分开 */
    align-items: center; /* 垂直居中 */
    padding: 10px; /* 根据需要调整 */
    margin-top: 10px;
    margin-left: 0px;
  }

  .history-btn {
    padding: 0; /* 移除内边距 */
    border-radius: 18px; /* 圆角 */
    width: 81px;
    height: 30px;
    color: #ffffff; /* 文字颜色 */
    border: 1px solid #ffffff; /* 边框样式 */
    background-color: transparent; /* 移除背景颜色 */
    font-size: 12px; /* 字体大小 */
    margin-left: 126px;
    line-height: 30px; /* 文字垂直居中 */
    text-align: center; /* 文字水平居中 */
  }
  
  

  .follow-btn,
  .unfollow-btn {
    padding: 0px 10px; /* 调整内边距以缩小按钮 */
    width: 81px;
    height: 30px;
    border-radius: 18px; /* 圆角 */
    border: none; /* 去除边框 */
    font-size: 12px; /* 文字大小 */
    transition: background-color 0.8s, color 0.3s; /* 过渡动画 */
  }
  
  .unfollow-btn {
    background-color: #ffffff; /* 淡绿色 */
    color: #131313; /* 淡绿色文字 */
  }
  
  .follow-btn {
    background-color: #181818; /* 关注按钮背景色 */
    color: #ffffff; /* 关注按钮文字颜色 */
  }
  
  .follow-btn:active,
  .unfollow-btn:active {
    background-color: #666666; /* 点击时的背景色 */
    color: #ffffff; /* 点击时的文字颜色 */
  }
  
  
  .qrcode-icon {
    width: 24px; /* 图标宽度 */
    height: 24px; /* 图标高度 */
    display: block; /* 确保图像块级显示 */
    margin-right: 10px; /* 右边距，确保贴着右边显示 */
  }

  .container {
    border-top-left-radius: 24px; /* 左上角圆角 */
    border-top-right-radius: 24px; /* 右上角圆角 */
    padding: 6px 0 240px; /* 顶部内边距为 6px */
    background-color: rgb(247, 247, 247); /* 白色背景 */
    margin-top: 10px;
  }
  
  .placeholder {
    height: 30px; /* 占位符高度 */
    bottom: 46px; /* 距离底部的距离，应为容器底部内边距 + 提示信息高度 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度占满容器 */
    text-align: center; /* 文本水平居中 */
    color: #888; /* 提示信息颜色 */
    font-size: 14px; /* 提示信息字体大小 */
    line-height: 30px; /* 行高与容器高度一致 */
  }
  
  .icon {
    width: 18px; /* 图标宽度 */
    height: 18px; /* 图标高度 */
    margin-right: 6px; /* 图标与文字间距 */
    margin-bottom: -4px;
  }

  .meeting-item {
    background-color: #ffffff; /* 白色背景 */
    border-radius: 12px; /* 圆角边缘 */
    box-shadow: 0 3px 20px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
    margin: 15px; /* 添加外边距，确保卡片之间有间隔 */
    padding: 15px; /* 内边距，确保内容不会紧贴边缘 */
  }
  
  
  
  .weui-form-preview__item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px; /* 每项之间的间隔 */
  }
  
  .weui-form-preview__label {
    color: rgb(44, 44, 44); /* 标签颜色 */
    font-size: 14px; /* 标签字体大小 */
  }
  
  .weui-form-preview-time {
    color: #333; /* 值的颜色 */
    font-size: 21px; /* 值的字体大小 */
    text-align: right; /* 值的对齐方式 */
  }

  .weui-form-preview__value {
    color: #333; /* 值的颜色 */
    font-size: 16px; /* 值的字体大小 */
    margin-top: 0px;
    margin-bottom: 5px;
    text-align: right; /* 值的对齐方式 */
  }
  
.qr_code{
  width: 100%
}
.weui-footer-detail {
    position: fixed; /* 固定定位 */
    bottom: 0; /* 底部对齐 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度占满整个屏幕 */
    background-color: #ffffff; /* 底部导航栏背景色 */
    display: flex; /* 使用 Flexbox 布局 */
    justify-content: space-around; /* 水平居中并平均分配空间 */
    padding: 20px 0; /* 上下内边距 */
  }

.footer-reserve,
.footer-share {
    padding: 5px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    width: 120px;
    height: 45px;
    border: 1px solid #000000; /* 边框 */
    font-size: 14px; /* 文字大小 */
    background-color: transparent; /* 透明背景 */
    color: #000000; /* 文字颜色 */
}

.not-room-image {
    margin-top: 30px;
    display: block; /* 设置为块级元素 */
    margin-left: auto; /* 居中 */
    margin-right: auto; /* 居中 */
    transform: scale(0.8); /* 缩小图片尺寸 */
    filter: grayscale(100%) brightness(110%); /* 应用灰度滤镜并增加亮度 */
}




.not-room-desc {
    color: #666; /* 描述文字使用较深的灰色 */
    font-size: 14px; /* 调整字体大小 */
    margin-top: 10px;
    text-align: center; /* 文字水平居中 */
  }
```

## File: miniprogram/pages/room/edit.js
```javascript
"use strict";
// pages/room/edit.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user_info: {},
    room_id: 0,
    name: "",
    description: "",
    create_user_manager: false
  },
  refreshInfo: function() {
    if (this.data.room_id > 0){
      app.api.api_meeting_room_info({ room_id: this.data.room_id}).then(res => {
        this.setData({
          name: res.name,
          description: res.description,
          create_user_manager: res.create_user_manager
        })
      })
    }
  },
  create_user_manager_change: function(e){
    this.setData({ create_user_manager: e.detail.value })
  },
  bindKeyInput: function(e) {
    this.data[e.currentTarget.dataset.obj] = e.detail.value
  },
  onGetUserInfo: function (e) {
    app.onGetUserInfo(e).then(res => {
      this.setData({ user_info: res })
    })
  },
  save: function() {
    if(!this.data.name.trim()){
      wx.showToast({
        icon: 'none',
        title: '请输入名称',
      })
      return
    }
    wx.showLoading({
      mask: true,
      title: '加载中...',
    })
    if (this.data.room_id > 0) {
      app.api.api_meeting_room_edit({
        room_id: this.data.room_id,
        name: this.data.name,
        description: this.data.description,
        create_user_manager: this.data.create_user_manager
      }).then(res => {
        wx.hideLoading()
        wx.navigateBack()
      }).catch(res => {
        wx.hideLoading()
      })
    } else {
      app.api.api_meeting_room_create({
        name: this.data.name,
        description: this.data.description,
        create_user_manager: this.data.create_user_manager
      }).then(res => {
        wx.hideLoading()
        wx.redirectTo({
          url: 'detail?room_id='+res.id,
        })
      }).catch(res => {
        wx.hideLoading()
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.userInfo().then(res => {
      this.setData({ user_info: res })
    })
    let room_id = options.room_id
    if(room_id){
      this.setData({ room_id: parseInt(room_id)})
      this.refreshInfo()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refreshInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})
```

## File: miniprogram/pages/room/edit.json
```json
{
  "usingComponents": {},
  "navigationBarTitleText": "会议室编辑"
}
```

## File: miniprogram/pages/room/edit.wxml
```
<!--pages/room/edit.wxml-->
<view class="page">
  <view class="page_bd page__bd_spacing weui-cells weui-cells_after-title">
    <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
            <view class="weui-label">名称</view>
        </view>
        <view class="weui-cell__bd">
            <input class="weui-input" placeholder="请输入名称" value="{{name}}" data-obj="name" bindinput='bindKeyInput'/>
        </view>
    </view>
    <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
            <view class="weui-label">描述</view>
        </view>
        <view class="weui-cell__bd">
            <input class="weui-input" placeholder="请输入描述" value="{{description}}" data-obj="description" bindinput='bindKeyInput'/>
        </view>
    </view>
    <view class="weui-cells weui-cells_after-title">
      <view class="weui-cell weui-cell_switch">
        <view class="weui-cell__bd">创建者权限</view>
        <view class="weui-cell__ft">
          <switch checked='{{create_user_manager}}' bindchange='create_user_manager_change'/>
        </view>
      </view>
    </view>
    <view class="weui-cells__tips" wx:if="{{create_user_manager}}">勾选后会议室创建者可以修改该会议室的会议内容及取消会议（不可取消），为防止误操作，建议需要使用时勾选，使用完成后取消该权限</view>
  </view>
  <block wx:if="{{!user_info.avatarurl}}">
      <view class="weui-cells__title">需要通过微信授权获得你的微信名和头像</view>
      <button class="save-btn" open-type="getUserInfo" lang="zh_CN" bindgetuserinfo="onGetUserInfo">授权登录</button>
    </block>
    <button class="save-btn" bindtap="save" wx:else>保存</button>
</view>
```

## File: miniprogram/pages/room/edit.wxss
```
/* pages/room/edit.wxss */

.save-btn {
    padding: 2px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    margin-top: 8%;
    width: 240px;
    height: 45px;
    font-size: 16px; /* 文字大小 */
    background-color: #242424; /* 透明背景 */
    color: #ffffff; /* 文字颜色 */
}
```

## File: miniprogram/pages/room/history.js
```javascript
"use strict";
// pages/room/history.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    room_id: 0,
    info: {},
    meetings: [],
    history_start: '',
    history_end: '',
    history_limit_start: '',
    history_limit_end: '',
  },
  refresh: function () {

    app.api.api_meeting_room_info({ room_id: this.data.room_id }).then(res => {
      this.setData({ info: res })
      app.userInfo().then(res => {
        this.setData({ owner: res.id == this.data.info.create_user })
      })
    })
    app.api.api_meeting_history_meetings({
      room_id: this.data.room_id,
      start_date: this.data.history_start,
      end_date: this.data.history_end
    }).then(res => {
      this.setData({
        meetings: res.meetings,
        history_limit_start: res.history_start_date,
        history_limit_end: res.history_end_date
      })
    })
  },
  change_history: function(){
    this.setData({
      history_view: !this.data.history_view
    })
    this.refresh()
  },
  change_history_start: function(e){
    this.setData({
      history_start: e.detail.value
    })
    this.refresh()
  },
  change_history_end: function(e){
    this.setData({
      history_end: e.detail.value
    })
    this.refresh()
  },
  date_select_change: function (e) {
    this.refresh()
  },
  detail: function (e) {
    wx.navigateTo({
      url: '../meeting/detail?meeting_id=' + e.currentTarget.id
    })
  },
  formatNumber: function(n) {
    n = n.toString()
    return n[1] ? n : '0' + n
  },
  dateId: function(date){
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    return [year, month, day].map(this.formatNumber).join('-')
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let now = app.nowDate()
    this.setData({
      room_id: parseInt(options.room_id),
      show_home: getCurrentPages().length == 1,
      history_end: this.dateId(now),
      history_start: this.dateId(new Date(now.setDate(now.getDate() - 7)))

    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refresh()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
})
```

## File: miniprogram/pages/room/history.json
```json
{
  "usingComponents": {}
}
```

## File: miniprogram/pages/room/history.wxml
```
<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<view class="page history">
  <view class="page__hd">
    <view class='weui-cell'>
      <view class='weui-cell__bd-title'>
        <view class="page__title">{{info.name}}</view>
        <view class="page__desc">{{info.description}}</view>
      </view>
    </view>
  </view>
  <view class="weui-flex">
    <view class="weui-flex__item" style="text-align:right">
      <picker value="{{history_start}}" mode="date" bindchange="change_history_start" start="{{history_limit_start}}" end="{{history_end}}">
        <input class="weui-input" value="{{history_start}}" disabled="true"></input>
      </picker>
    </view>
    <view class="weui-flex__item" style="display: flex;justify-content: center;align-items: center;">至</view>
    <view class="weui-flex__item" style="text-align:left">
      <picker value="{{history_end}}" mode="date" bindchange="change_history_end" start="{{history_start}}" end="{{history_limit_end}}">
        <input class="weui-input" value="{{history_end}}" disabled="true"></input>
      </picker>
    </view>
  </view>

  <view class="page_bd page__bd_spacing">
    <view class='weui-cells'>
      <block wx:for="{{meetings}}" wx:key="id">
        <view class="flag" wx:if="{{index == 0 || meetings[index-1].date != item.date}}">{{item.date}}</view>
        <view class="history-cell weui-cell_access" bindtap='detail' hover-class="weui-cell_active" id="{{item.id}}">
          <view class="weui-cell__bd">{{item.name}}</view>
          <view class="weui-cell__ft">{{timeUtils.formatTime(item.start_time)}} - {{timeUtils.formatTime(item.end_time)}}</view>
        </view>
      </block>
    </view>
  </view>
  
  
</view>
```

## File: miniprogram/pages/room/history.wxss
```
/* pages/room/history.wxss */
.history .page_bd .history-cell {
    background-color: #eeeeee;
    border-radius: 18px; /* 圆角 */
    margin: 10px;
    display: flex; /* 启用 flexbox 布局 */
    align-items: center; /* 垂直居中 */
    justify-content: space-between; /* 子元素间距平均分布 */
  }
  
  .weui-cell__bd, .weui-cell__ft {
    padding: 10px; /* 为子元素添加内边距 */
    margin: 0px 10px;
  }

  .weui-cell__bd-title {
    padding: 10px; /* 为子元素添加内边距 */
  }

  
.history .flag{
  padding-top: 30rpx;
  padding-left: 30rpx;
  font-weight: bold;
}

.weui-flex {
    display: flex; /* 确保使用flex布局 */
    align-items: center; /* 垂直居中所有子元素 */
}

.weui-flex__item {
    flex: 1; /* 确保所有子元素平分空间 */
    display: flex; /* 同样使用flex布局 */
    justify-content: center; /* 水平居中对齐 */
    align-items: center; /* 垂直居中对齐 */
}

.weui-flex__item:first-child {
    justify-content: flex-end; /* 第一个元素内的内容靠右对齐 */
}

.weui-flex__item:last-child {
    justify-content: flex-start; /* 最后一个元素内的内容靠左对齐 */
}
```

## File: miniprogram/pages/room/list.js
```javascript
// pages/room/list.js
const app = getApp();

Page({
  data: {
    rooms: [], // 存放会议室信息
  },

  onLoad: function() {
    this.loadRooms(); // 初次加载时调用加载会议室信息
  },

  onShow: function() {
    this.loadRooms(); // 每次页面显示时重新加载会议室信息
  },

  loadRooms: function() {
    app.api.api_meeting_follow_rooms().then(res => {
      console.log("API响应数据:", res); // 打印API响应的数据
      this.setData({
        rooms: res,
      });
    }).catch(err => {
      console.log('加载会议室信息失败', err);
      // 可以添加用户反馈，例如一个错误提示
      wx.showToast({
        title: '加载会议室信息失败',
        icon: 'none'
      });
    });
  },

  detail: function(e) {
    var roomId = e.currentTarget.dataset.id; // 通过data-id传递的会议室ID
    wx.navigateTo({
        url: 'detail?room_id=' + roomId
    });
  },

  create: function(e){
    wx.navigateTo({
      url: 'edit',
    })
  },

  onPullDownRefresh: function () {
    this.loadRooms(); // 下拉刷新时也重新加载数据
    wx.stopPullDownRefresh(); // 停止下拉刷新动作
  },

  onShareAppMessage: function () {
    // 分享逻辑，如果您有选择会议室进行分享的功能，确保逻辑与数据结构一致
    if (this.data.shareSelect && this.data.shareSelect.length === 0) {
      return {};
    }
    let title = this.data.rooms.filter(room => this.data.shareSelect.includes(room.id)).map(room => room.name);
    return {
      title: title.join(", "),
      path: '/pages/room/list?room_ids=' + this.data.shareSelect.join(",")
    };
  },

  // 其他生命周期函数和事件处理函数留空，按需要实现
});
```

## File: miniprogram/pages/room/list.json
```json
{
  "usingComponents": {}
}
```

## File: miniprogram/pages/room/list.wxml
```
<!-- 欢迎信息或其他通知 -->
  <button class="create-room-btn" bindtap='create'>+</button>
  <view class="content-wrapper">
    <image class="background-image" src="/images/icon-index-bg.svg"></image>
    <view class="weui-panel">
      <view class="weui-panel__bd">
        <view class="weui-media-box_text">
          <view class="weui-media-list__title">您好</view>
          <view class="weui-media-list__desc">欢迎来到环境馆！</view>
        </view>
      </view>
    </view>
  </view>
  <view class="page">
  <!-- 会议室列表 -->
  <view class="grey-background">
      <view class="weui-panel-title">会议室列表</view>
      <view class="weui-panel__bd" style="height: auto; overflow-y: auto;">
        <block wx:if="{{rooms.length > 0}}">
          <block wx:for="{{rooms}}" wx:key="id">
            <view bindtap="detail" class="weui-media-box weui-media-box_text" hover-class="weui-cell_active" data-id="{{item.id}}">
              <view class="weui-media-box__title">{{item.name}}</view>
              <view class="weui-media-box__desc">{{item.description}}</view>
              <image class="icon" src="/images/icon-index-build.svg"></image>
            </view>
          </block>
        </block>
        <block wx:else>
          <view class="weui-panel__bd">
            <view class="weui-media-box weui-media-box_text">
              <!-- 在此处添加你的提示图片 -->
              <image class="not-room-image" src="/images/icon-index-not.svg"></image>
              <view class="not-room-desc">请先创建会议室。</view>
            </view>
          </view>
        </block>
      </view>
      <view class="placeholder">暂无更多信息~</view>
  </view>
</view>
```

## File: miniprogram/pages/room/list.wxss
```
/* pages/room/list.wxss */
.content-wrapper {
    padding: 60px 0 10px; /* 顶部内边距为 24px */
    position: relative; /* 相对定位 */
    overflow: hidden; /* 隐藏超出卡片边界的内容 */
  }

  .page {
    border-top-left-radius: 24px; /* 左上角圆角 */
    border-top-right-radius: 24px; /* 右上角圆角 */
    padding: 6px 0 70px; /* 顶部内边距为 6px */
    background-color: rgb(247, 247, 247); /* 白色背景 */

}


/* 在你的 CSS 文件中添加以下样式 */
.background-image {
    position: fixed; /* 设置为 fixed，相对于视口固定位置 */
    width: 440px;
    height: 440px;
    margin-left: 90px;
    margin-top: -165px;
    z-index: -1; /* 将背景图放在最底层 */
}

  .grey-background {
    margin-top: 10px;
  }
  

  .create-room-btn {
    position: relative; /* 相对定位 */
    margin-top: 1%;
    margin-left: 15px;
    padding: 0px 0px; /* 调整内边距以缩小按钮 */
    width: 33px;
    height: 33px;
    border-radius: 10px; /* 圆角 */
    border: none; /* 去除边框 */
    text-align: center; /* 文本水平居中 */
    line-height: 30px; /* 文字垂直居中 */
    font-size: 27px; /* 文字大小 */
    background-color: #131313d5;
    color: #e7e7e7;
  }

  

  /* 会议室小卡片样式 */
  .weui-media-box {
      position: relative; /* 相对定位 */
      overflow: hidden; /* 隐藏超出卡片边界的内容 */
      background-color: #ffffff; /* 白色背景 */
      border-radius: 12px; /* 圆角边缘 */
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.02); /* 添加阴影效果 */
      margin: 15px; /* 添加一些外边距，确保卡片之间有间隔 */
      padding: 27px; /* 内边距，确保内容不会紧贴边缘 */
      margin-bottom: 20px;
    }
/* 在你的 CSS 文件中添加以下样式 */
  
.icon {
    position: absolute; /* 绝对定位 */
    bottom: -80px; /* 距离卡片底部的距离 */
    right: -100px; /* 距离卡片右侧的距离 */
    max-width: calc(190% - 20px); /* 图标最大宽度，考虑了右边距和左边距 */
    max-height: calc(190% - 20px); /* 图标最大高度，考虑了上边距和下边距 */
    transform: rotate(0deg); /* 旋转45度 */
    opacity: 0.6; /* 设置透明度为50% */
  }
  
    .weui-media-list__title {
      font-weight: bold; /* 标题加粗 */
      font-size: 24px;
      margin-top: 70px;
      margin-left: 15px;
    }
    
    .weui-media-list__desc {
      color: rgb(80, 80, 80); /* 描述文字使用较深的灰色 */
      font-size: 18px; /* 调整字体大小 */
      margin-top: 10px;
      margin-left: 15px;
    }
  
    .weui-panel__hd {
      font-weight: bold;
      color: rgb(20, 20, 20); /* 描述文字使用较深的灰色 */
      font-size: 15px; /* 调整字体大小 */
      margin-top: 20px;
      margin-left: 15px;
      margin-bottom: -12px;
    }
    .weui-panel-title {
        font-size: 16px;
        margin-left: 15px;
        margin-top: 10px;
        font-weight: bold; /* 标题加粗 */
      }

    .weui-media-box__title {
      font-size: 16px;
      font-weight: bold; /* 标题加粗 */
    }
    
    .weui-media-box__desc {
      color: #666; /* 描述文字使用较深的灰色 */
      font-size: 14px; /* 调整字体大小 */
      margin-top: 10px;
    }
    .not-room-image {
        margin-top: 30px;
        display: block; /* 设置为块级元素 */
        margin-left: auto; /* 居中 */
        margin-right: auto; /* 居中 */
        transform: scale(0.8); /* 缩小图片尺寸 */
    }

    .not-room-desc {
        color: #666; /* 描述文字使用较深的灰色 */
        font-size: 14px; /* 调整字体大小 */
        margin-top: 10px;
        text-align: center; /* 文字水平居中 */
      }
  
      .placeholder {
        bottom: 10px; /* 距离底部的距离，应为容器底部内边距 + 提示信息高度 */
        width: 100%; /* 宽度占满容器 */
        text-align: center; /* 文本水平居中 */
        color: #888; /* 提示信息颜色 */
        font-size: 14px; /* 提示信息字体大小 */
      }
```

## File: miniprogram/pages/room/meeting.js
```javascript
// pages/meeting/meeting.js
const app = getApp();
Page({
    data: {
      rooms: [], // 存放会议室信息
      meetings: [], // 存放用户的会议信息
      meetingId: [], // 存放用户的会议信息
    },
  
    onLoad: function() {
      this.loadMeetings();
    },
  
    loadMeetings: function() {
      app.api.api_meeting_my_meetings().then(res => {
      console.log("API响应数据:", res); // 这里打印API响应的数据
        // 映射会议数组，为每个会议添加会议室名称
        const meetingsWithRoomName = res.meetings.map(meeting => {
          const room = res.rooms.find(r => r.id === meeting.room);
          const meetingId = res.meetings.id;
          return {
            ...meeting,
            roomName: room ? room.name : '未知会议室', // 如果找不到对应的会议室，使用"未知会议室"
          };
        });
        
        this.setData({
          meetings: meetingsWithRoomName,
          rooms: res.rooms,
        });
      }).catch(err => {
        console.log('加载会议信息失败', err);
      });
    },
  
    detail: function(e) {
        wx.navigateTo({
            url: '../meeting/detail?meeting_id=' + e.currentTarget.id
          })
    },
  });
```

## File: miniprogram/pages/room/meeting.json
```json
{
  "usingComponents": {}
}
```

## File: miniprogram/pages/room/meeting.wxml
```
<!-- pages/meeting/meeting.wxml -->
<view class="page">
  <view class="weui-panel">
    <view class="weui-panel__hd">我的会议</view>
    <view class="weui-panel__bd">
      <block wx:for="{{meetings}}" wx:key="id">
        <view class="weui-media-box weui-media-box_text" data-id="{{item.id}}">
          <!-- 展示会议室名称和会议名称 -->
          <view class="weui-media-box__title">{{item.roomName}}</view>
          <view class="weui-media-box__title">{{item.name}}</view>
          <view class="weui-form-preview__item">
            <view class="weui-form-preview__label">
              <image src="/images/ic_daipingjia.png" class="icon"></image>用途</view>
            <view class="weui-form-preview__value">{{item.name}}</view>
          </view>
          <view class="weui-media-box__desc">{{item.date}} {{item.start_time}} 到 {{item.end_time}}</view>
        </view>
      </block>
      <view wx:if="{{meetings.length == 0}}">没有即将参加的会议。</view>
    </view>
  </view>
</view>
```

## File: miniprogram/pages/room/meeting.wxss
```
/* pages/room/list.wxss */
.page {
    padding: 10px;
  }
  
  .weui-media-box {
    border-radius: 12px;
    box-shadow: 0 3px 20px rgba(0, 0, 0, 0.05);
    margin: 10px 0;
    padding: 20px;
  }
  
  .weui-panel__hd {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .weui-form-preview__label {
    color: rgb(44, 44, 44); /* 标签颜色 */
    font-size: 14px; /* 标签字体大小 */
  }
  
  .icon {
    width: 18px; /* 图标宽度 */
    height: 18px; /* 图标高度 */
    margin-right: 6px; /* 图标与文字间距 */
    margin-bottom: -4px;
  }
```

## File: miniprogram/utils/api.js
```javascript
"use strict";
const request = require('./request');
const server = 'https://coffee.yooushe.com';

const ERROR_CODE = {
  SUCCESS: 0, // 返回成功
  ERROR_UNKNOWN: -1, // 未知错误
  ERROR_SYSTEM: -2, // 系统错误
  ERROR_BAD_PARAMETER: -11, // 参数错误
  ERROR_BAD_FORMAT: -12, // 格式错误
  ERROR_PERMISSION: -13, // 权限错误
  ERR_WECHAT_LOGIN: 10001, // 需要登录
  ERR_MEETING_ROOM_TIMEOVER: 20001, // 时间已过
  ERR_MEETING_ROOM_INUSE: 20002, // 时间冲突
  ERR_MEETING_ROOM_NOT_FOUND: 20003, // 会议室未找到
  ERR_MEETING_NOT_FOUND: 20004 // 会议室未找到
}


// 小程序登录
const api_wechat_login = function({
  js_code // 小程序登录code
} = {}) {
  return request({
    server: server,
    path: '/api/wechat/login',
    method: 'GET',
    data: {
      js_code: js_code
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 小程序用户信息
const api_wechat_user_info = function({
  encrypted_data, // 完整用户信息的加密数据
  iv // 加密算法的初始向量
} = {}) {
  return request({
    server: server,
    path: '/api/wechat/user/info',
    method: 'POST',
    data: {
      encrypted_data: encrypted_data,
      iv: iv
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 配置信息
const api_meeting_config = function() {
  return request({
    server: server,
    path: '/api/meeting/config',
    method: 'GET',
    data: {},
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 创建会议室
const api_meeting_room_create = function({
  name, // 名称
  description, // 描述
  create_user_manager // 创建人管理权限
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/create',
    method: 'POST',
    data: {
      name: name,
      description: description,
      create_user_manager: create_user_manager
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 修改会议室
const api_meeting_room_edit = function({
  room_id, // 会议室ID
  name, // 名称
  description, // 描述
  create_user_manager // 创建人管理权限
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/edit',
    method: 'POST',
    data: {
      room_id: room_id,
      name: name,
      description: description,
      create_user_manager: create_user_manager
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 删除会议室
const api_meeting_room_delete = function({
  room_id // 会议室ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/delete',
    method: 'GET',
    data: {
      room_id: room_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 会议室信息
const api_meeting_room_info = function({
  room_id // 会议室ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/info',
    method: 'GET',
    data: {
      room_id: room_id,
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 关注会议室
const api_meeting_room_follow = function({
  room_id // 会议室ID列表
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/follow',
    method: 'GET',
    data: {
      room_id: room_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 取消关注会议室
const api_meeting_room_un_follow = function({
  room_id // 会议室ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/un/follow',
    method: 'GET',
    data: {
      room_id: room_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 已关注会议室列表
const api_meeting_follow_rooms = function() {
  return request({
    server: server,
    path: '/api/meeting/follow/rooms',
    method: 'GET',
    data: {},
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 创建会议室列表
const api_meeting_create_rooms = function() {
  return request({
    server: server,
    path: '/api/meeting/create/rooms',
    method: 'GET',
    data: {},
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 会议室预约列表
const api_meeting_room_meetings = function({
  room_ids, // 会议室ID列表
  date // 日期
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/room/meetings',
    method: 'GET',
    data: {
      room_ids: room_ids,
      date: date
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 会议室预约历史
const api_meeting_history_meetings = function({
  room_id, // 会议室ID
  start_date, // 开始日期
  end_date // 结束日期
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/history/meetings',
    method: 'GET',
    data: {
      room_id: room_id,
      start_date: start_date,
      end_date: end_date
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 我参与的会议列表
const api_meeting_my_meetings = function({
  date // 日期
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/my/meetings',
    method: 'GET',
    data: {
      date: date
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 预约会议
const api_meeting_reserve = function({
    room_id, // 会议室ID
    name, // 名称
    description, // 描述
    date, // 预定日期
    start_time, // 开始时间
    end_time // 结束时间
  } = {}) {
    return request({
      server: server,
      path: '/api/meeting/reserve',
      method: 'POST',
      data: {
        room_id: room_id,
        name: name,
        description: description,
        date: date,
        start_time: start_time,
        end_time: end_time
      },
      header: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  }


// 会议详情
const api_meeting_info = function({
  meeting_id // 会议ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/info',
    method: 'GET',
    data: {
      meeting_id: meeting_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 会议修改
const api_meeting_edit = function({
  meeting_id, // 会议ID
  name, // 名称
  description, // 描述
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/edit',
    method: 'POST',
    data: {
      meeting_id: meeting_id,
      name: name,
      description: description,
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 取消会议
const api_meeting_cancel = function({
  meeting_id // 会议ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/cancel',
    method: 'GET',
    data: {
      meeting_id: meeting_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 参加会议
const api_meeting_join = function({
  meeting_id // 会议ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/join',
    method: 'GET',
    data: {
      meeting_id: meeting_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


// 取消参加会议
const api_meeting_leave = function({
  meeting_id // 会议ID
} = {}) {
  return request({
    server: server,
    path: '/api/meeting/leave',
    method: 'GET',
    data: {
      meeting_id: meeting_id
    },
    header: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}


module.exports = {
  ERROR_CODE: ERROR_CODE,
  api_wechat_login: api_wechat_login,
  api_wechat_user_info: api_wechat_user_info,
  api_meeting_config: api_meeting_config,
  api_meeting_room_create: api_meeting_room_create,
  api_meeting_room_edit: api_meeting_room_edit,
  api_meeting_room_delete: api_meeting_room_delete,
  api_meeting_room_info: api_meeting_room_info,
  api_meeting_room_follow: api_meeting_room_follow,
  api_meeting_room_un_follow: api_meeting_room_un_follow,
  api_meeting_follow_rooms: api_meeting_follow_rooms,
  api_meeting_create_rooms: api_meeting_create_rooms,
  api_meeting_room_meetings: api_meeting_room_meetings,
  api_meeting_history_meetings: api_meeting_history_meetings,
  api_meeting_my_meetings: api_meeting_my_meetings,
  api_meeting_reserve: api_meeting_reserve,
  api_meeting_info: api_meeting_info,
  api_meeting_edit: api_meeting_edit,
  api_meeting_cancel: api_meeting_cancel,
  api_meeting_join: api_meeting_join,
  api_meeting_leave: api_meeting_leave
}
```

## File: miniprogram/utils/apiviewws.js
```javascript
"use strict";
const httpCookie = require('./http-cookie.js')
let app = getApp()
const TASK_STATUS = {
  INIT: -1, // 初始化
  ERROR: -2, // 连接错误
  CLOSE: -3, // 连接关闭
  CLOSEING: -4, // 请求连接关闭中
  OK: 0, // 连接成功
  CONNECTING: 1, // 连接中
  RECONNECTING: 3, // 请求重连中
}
const ApiViewWS = function (ws_path, common_listener) {
  this.listenerList = {}
  this.common_listener = common_listener 
  this.gen_reqid = function () {
    return "apiview_" + parseInt(Math.random() * 9000000000 + 1000000000)
  }
  this.ws_path = ws_path
  this.task_status = TASK_STATUS.INIT
  this.connects = []
  this.last_msg_time = null
  this._proc_data = (data) => {
    let reqid = data['reqid']
    if (reqid === undefined) {
      if (typeof this.common_listener === "function") {
        this.common_listener(data)
      }
      return
    }
    if (app === undefined) {
      app = getApp()
    }
    let serverTimeString = data["server_time"].replace(/-/g, '/');
    let server_time = new Date(serverTimeString);
    if (!isNaN(server_time)) {
      this.last_msg_time = server_time
      app.globalData.timeDifference = server_time.getTime() - new Date().getTime()
    }
    let listener = this.listenerList[reqid]
    delete this.listenerList[reqid]
    if (typeof listener === "function") {
      listener(data["status_code"] == 200, data)
    }
  }
  this._failall = () => {
    for (let reqid in this.listenerList) {
      if (typeof this.listenerList[reqid] === "function") {
        this.listenerList[reqid](false, {status_code: -1})
      }
    }
  }
  this._connects_callback = (index, data) => {
    while(this.connects.length > 0){
      let connect = this.connects.pop()
      connect[index](data)
    }
  }
  this._new_task = () => {
    this.showLoading = true
    wx.showLoading({
      title: '加载中',
    })
    let header = {
      'Cookie': httpCookie.getCookieForReq()
    }
    this.task_status = TASK_STATUS.CONNECTING
    this.task = wx.connectSocket({
      url: this.ws_path,
      header: header,
      tcpNoDelay: false,
      protocols: ["apiview"],
      method: "GET",
      success: res => {
      },
      fail: res => {
        this._connects_callback(1, "网络错误")
        if (this.showLoading){
          this.showLoading = false
          wx.hideLoading()
        }
      }
    })
    this.task.onClose(res => {
      this._failall()
      if (this.task_status === TASK_STATUS.RECONNECTING) {
        //this._new_task()
      } else if (this.task_status === TASK_STATUS.CLOSEING){
        this.task_status = TASK_STATUS.CLOSE
        this._connects_callback(1, "网络错误")
      }
    })
    this.task.onError(res => {
      this.task_status = TASK_STATUS.ERROR
      if (this.showLoading) {
        this.showLoading = false
        wx.hideLoading()
      }
      this._failall()
      this._connects_callback(1, "网络错误")
    })
    this.task.onMessage(res => {
      this._proc_data(JSON.parse(res.data))
    })
    this.task.onOpen(res => {
      this.task_status = TASK_STATUS.OK
      if (this.showLoading) {
        this.showLoading = false
        wx.hideLoading()
      }
      this._connects_callback(0, this)
    })
  }
  this.connect = () => {
    return new Promise((resolve, reject) => {
      if (this.task_status === TASK_STATUS.OK){
        if (this.task.readyState === 1){
          resolve(this)
          return
        }else{
          this.task_status = TASK_STATUS.ERROR
        }
      }
      this.connects.push([resolve, reject])
      if (this.task_status < 0){
        this._new_task()
      }
    })
    
  }
  this.close = () => {
    this.task_status = TASK_STATUS.CLOSEING
    if(this.task){
      this.task.close()
    }
  }
  this.reconnect = () => {
    this.task_status = TASK_STATUS.RECONNECTING
    if(this.task){
      this.task.close()
    }
    this._new_task()
  }
  this.check_and_reconnect = (check_time) => {
    if (this.task_status > 0 || !app){
      return
    }
    if (this.task_status === TASK_STATUS.OK && this.last_msg_time && (app.nowDate() - this.last_msg_time) < (check_time * 1000)){
      return
    }
    this.reconnect()
  }
  this.req = (path, data, listener) => {
    let reqid = this.gen_reqid()
    while (this.listenerList.hasOwnProperty(reqid)) {
      reqid = this.gen_reqid()
    }
    this.listenerList[reqid] = listener
    const req_data = { path: path, reqid: reqid, data: data }
    this.task.send({
      data: JSON.stringify(req_data),
      success: res => {
      },
      fail: res => {
        listener(false, {})
      }
    })
  }
  return this
}
module.exports = ApiViewWS
```

## File: miniprogram/utils/http-cookie.js
```javascript
"use strict";

const setCookieTool = require('./set-cookie.js');

/**
 * 设置cookie 参数为字符串，
 * 例如cookieA=a; expires=Tue, 14-Aug-2018 10:00:45 GMT; path=/; HttpOnly,cookieB=b; expires=Wed, 15-Aug-2018 08:00:45 GMT; path=/
 */
const setCookie = (str) => {
  // 处理参数
  var splitCookieHeaders = setCookieTool.splitCookiesString(str);
  var cookies = setCookieTool.parse(splitCookieHeaders);

  // 获取本地的cookie
  var localCookie = queryCookie();

  // 循环处理 数组
  cookies.forEach((c) => {
    localCookie[c.name] = c;
  });

  // 过滤
  localCookie = checkExpires(localCookie);

  // 持久化cookies
  saveCookie(localCookie);

}

/**
 * 设置cookie 参数为wx.request的返回头
 */
const setCookieByHead = (head) => {
  if (head && head['Set-Cookie']) {
    setCookie(head['Set-Cookie']);
  }
}

/**
 * 获取cookie
 * 如果 传递了参数key，返回key对应的值
 * 如果 没有传递参数，返回所有cookie的kv对象
 */
const getCookie = (key) => {
  // 获取本地的cookie
  var localCookie = queryCookie();
  // 过滤
  localCookie = checkExpires(localCookie);
  // 持久化cookies
  saveCookie(localCookie);

  // 返回
  if (key) {
    return localCookie[key];
  } else {
    return localCookie;
  }
}

/**
 * 获取请求用的cookie字符串
 */
const getCookieForReq = () => {
  // 获取本地的cookie
  var localCookie = queryCookie();
  // 过滤
  localCookie = checkExpires(localCookie);
  // 持久化cookies
  saveCookie(localCookie);
  // 返回
  var rs = '';
  for (var i in localCookie) {
    var c = localCookie[i];
    rs += (c.name + "=" + c.value + "; ");
  }

  // 处理末端
  if (rs.substr(rs.length - 2, 2) == '; ') {
    rs = rs.substr(0, rs.length - 2);
  }

  return rs;
}

/**
 * 检查cookie是否过期（内部）
 * 返回过滤后的cookies
 */
const checkExpires = (cookies) => {
  if (!cookies) {
    cookies = queryCookie();
  }
  var app = getApp();
  var now = app === undefined ? new Date() : app.nowDate();
  var newCookies = {};

  for (var i in cookies) {
    var exp = new Date(cookies[i].expires);
    if (exp > now) {
      newCookies[i] = (cookies[i]);
    }
  }

  return newCookies;
}

/**
 * cookie持久化,会用cookies覆盖所有数据
 */
const saveCookie = (cookies) => {
  wx.setStorageSync('cti_cookie', cookies);
}

/**
 * 从持久化数据中返回cookie
 */
const queryCookie = () => {
  return wx.getStorageSync('cti_cookie');
}

module.exports = {
  getCookieForReq: getCookieForReq,
  setCookieByHead: setCookieByHead
}
```

## File: miniprogram/utils/meetings.js
```javascript
"use strict";
const check_status = (start_time, end_time, now_time) => {
  /**
   * 0: 不在范围内
   * 0x1: 第一
   * 0x2: 最后 
   * 0x4: 中间
   */
  let ret = 0
  if (start_time > now_time || now_time > end_time) {
    return ret
  }
  ret |= 0x4
  if (start_time == now_time) {
    ret |= 0x1
  }
  if (end_time == now_time) {
    ret |= 0x2
  }
  return ret
}
const get_str_list = (str_arr, count) => {
  const num = Math.floor(str_arr.length / count) /* 每格放文字数 */
  const left = str_arr.length - num * count /* 剩余文字数 */
  const float_pro_count = left / count /* 剩余文字每格需要放几个（小数） */
  let c = 0
  let left_c = 0
  let ret = []
  for (let i = 1; i < count; i++) {
    let now_c = num
    if (float_pro_count * i - left_c >= 0.5) {
      now_c++
      left_c++
    }
    ret.push(str_arr.slice(c, c + now_c).join(""))
    c += now_c
  }
  ret.push(str_arr.slice(c, str_arr.length).join(""))
  return ret
}
const get_meeting_data = (room_id, time, meetings) => {
  const time_value = time.value()
  const filter_meetings = meetings.filter(m => { return m.room.toString() == room_id.toString() })
  for (let i in filter_meetings) {
    let meeting = filter_meetings[i]
    const start_time = getApp().time.parseTime(meeting.start_time).value()
    const end_time = getApp().time.parseTime(meeting.end_time).value()
    let status = check_status(start_time, end_time - 30 * 60, time_value)
    if (status != 0) {
      let count = Math.round((end_time - start_time) / 30 / 60)
      let str_list = get_str_list(meeting.name.split(""), count)
      //let str_list = meeting.name_list
      let pos = Math.round((time_value - start_time) / 30 / 60)
      return { status: status, text: str_list[pos], id: meeting.id }

    }
  }
  return { status: 0, text: '', id: null }
}
const getTdData = (rooms, meetings, time_range, select, select_date) => {
  let td_data = {}
  let now = getApp().nowDate()
  let now_time = getApp().time.Time(now.getHours(), now.getMinutes(), now.getSeconds()).value()
  for (let i in rooms) {
    let room = rooms[i]
    td_data[room.id] = {}
    for (let j in time_range) {
      let time = time_range[j]

      let selected_status = 0
      if (select.selected && select.room.id == room.id) {
        selected_status = check_status(
          getApp().time.parseTime(select.start).value(),
          getApp().time.parseTime(select.end).value(),
          time.data.value()
        )
      }
      let meeting_data = get_meeting_data(room.id, time.data, meetings)
      let local_select_date = new Date(select_date)
      let today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).valueOf()
      local_select_date = new Date(local_select_date.getFullYear(), local_select_date.getMonth(), local_select_date.getDate()).valueOf()
      let expire = false
      if (local_select_date < today) {
        expire = true
      } else if (local_select_date == today) {
        if (now_time > time.data.value()) {
          expire = true
        }
      }
      let clazz = []
      if (expire) {
        clazz.push("expire")
      }
      const border_style = function (_clazz, _status) {
        if (_status & 0x1) {
          _clazz.push("top")
        }
        if (_status & 0x2) {
          _clazz.push("bottom")
        }
      }
      if (meeting_data.status == 0 && selected_status == 0) {
        border_style(clazz, 0x1 | 0x2 | 0x4)
      }
      if (meeting_data.status != 0) {
        clazz.push("in_use")
        border_style(clazz, meeting_data.status)
        if (selected_status != 0) {
          select.selected = false
          select.click = false
          select.start = ""
          select.end = ""
          select.room = {}
          return getTdData(rooms, meetings, time_range, select, select_date)
        }
      }
      if (selected_status != 0) {
        clazz.push("selected")
        border_style(clazz, selected_status)
      }

      td_data[room.id][time.id] = {
        clazz: clazz.join(" "),
        expire: expire,
        meeting_id: meeting_data.id,
        meeting_status: meeting_data.status,
        text: meeting_data.text,
        selected_status: selected_status
      }
    }
  }
  return td_data
}
module.exports = {
  getTdData: getTdData
}
```

## File: miniprogram/utils/request.js
```javascript
"use strict";
const USE_WEBSOCKET = true
const httpCookie = require('./http-cookie.js')
const ApiViewWS = require('./apiviewws.js')
let app = getApp()
let apiViewWSs = {}
const reconnectApiViews = (check_time) => {
  if(!USE_WEBSOCKET){
    return
  }
  for(let i in apiViewWSs){
    apiViewWSs[i].check_and_reconnect(check_time)
  }
}
const getApiViewWS = (server, need_connect) => {
  const ws_path = "ws" + server.substring(4) + "/wsapi"
  return new Promise((resolve, reject) => {
    if (!apiViewWSs.hasOwnProperty(ws_path)) {
      apiViewWSs[ws_path] = new ApiViewWS(ws_path)
    }
    if (!need_connect){
      resolve(apiViewWSs[ws_path])
      return
    }
    apiViewWSs[ws_path].connect().then(res => {
      resolve(apiViewWSs[ws_path])
    }).catch(res => {
      reject(res)
    })
  })
}
const check_res = function (res, server, path, data, method, header, resolve, reject, check_login) {
  if (app === undefined) {
    app = getApp()
  }
  if (check_login && res.data.code == app.api.ERROR_CODE.ERR_WECHAT_LOGIN) {
    app.login().then(res => {
      req(server, path, data, method, header, resolve, reject, false)
    }).catch(res => {
      resolve(res.data)
    })
  } else if (res.data.code != app.api.ERROR_CODE.SUCCESS) {
    reject(res.data.message)
  } else {
    resolve(res.data.data)
  }
}
const ws_request = function (server, path, data, method, header, resolve, reject, check_login) {
  if (!USE_WEBSOCKET || path === '/api/wechat/login') {
    wx_request(server, path, data, method, header, resolve, reject, check_login)
    return
  }
  getApiViewWS(server, true).then(conn => {
    conn.req(path, data, (succ, res) => {
      if (!succ) {
        reject("网络错误")
      } else {
        check_res(res, server, path, data, method, header, resolve, reject, check_login)
      }
    })
  }).catch(res => {
    wx_request(server, path, data, method, header, resolve, reject, check_login)
  })
}
const wx_request = function (server, path, data, method, header, resolve, reject, check_login){
  const url = server + path
  header['Cookie'] = httpCookie.getCookieForReq()
  const reqid = "req_" + parseInt(Math.random() * 9000000000 + 1000000000)
  wx.request({
    url: url,
    data: data,
    method: method,
    header: header,
    success(res) {
      httpCookie.setCookieByHead(res.header)
      if(app === undefined){
        app = getApp()
      }
      let server_time = new Date(res.header["Date"])
      if (!isNaN(server_time)){
        app.globalData.timeDifference = server_time.getTime() - new Date().getTime()
      }
      if(USE_WEBSOCKET){
        getApiViewWS(server, false).then(conn => {
          conn.reconnect()
          check_res(res, server, path, data, method, header, resolve, reject, check_login)
        }).catch(res => {
          check_res(res, server, path, data, method, header, resolve, reject, check_login)
        })
      }else{
        check_res(res, server, path, data, method, header, resolve, reject, check_login)
      }
    },
    fail(res) {
      reject("网络错误")
    },
    complete() {
    }
  })
}
const req = function (server, path, data, method, header, resolve, reject, check_login){
  return ws_request(server, path, data, method, header, resolve, reject, check_login)
}
const request = function({server, path, data, method, header} = {}){
  for (let key in data) {
    if(data[key] === undefined){
      delete data[key]
    }
  }
  return new Promise((resolve, reject) => {
    wx.showNavigationBarLoading()
    let resolve_callback = res => {
      wx.hideNavigationBarLoading()
      resolve(res)
    }
    let reject_callback = res => {
      wx.hideNavigationBarLoading()
      wx.showToast({
        icon: 'none',
        title: res
      })
      reject(res)
    }
    req(server, path, data, method, header, resolve_callback, reject_callback, true)
  })
}
module.exports = request
module.exports.reconnectApiViews = reconnectApiViews
```

## File: miniprogram/utils/set-cookie.js
```javascript
"use strict";

var defaultParseOptions = {
  decodeValues: true
};

function extend(target, source) {
  return Object.keys(source).reduce(function (target, key) {
    target[key] = source[key];
    return target;
  }, target);
}

function isNonEmptyString(str) {
  return typeof str === "string" && !!str.trim();
}

function parseString(setCookieValue, options) {
  var parts = setCookieValue.split(";").filter(isNonEmptyString);
  var nameValue = parts.shift().split("=");
  var name = nameValue.shift();
  var value = nameValue.join("="); // everything after the first =, joined by a "=" if there was more than one part
  var cookie = {
    name: name, // grab everything before the first =
    value: options.decodeValues ? decodeURIComponent(value) : value // decode cookie value
  };

  parts.forEach(function (part) {
    var sides = part.split("=");
    var key = sides
      .shift()
      .trimLeft()
      .toLowerCase();
    var value = sides.join("=");
    if (key === "expires") {
      cookie.expires = new Date(value);
    } else if (key === "max-age") {
      cookie.maxAge = parseInt(value, 10);
    } else if (key === "secure") {
      cookie.secure = true;
    } else if (key === "httponly") {
      cookie.httpOnly = true;
    } else if (key === "samesite") {
      cookie.sameSite = value;
    } else {
      cookie[key] = value;
    }
  });

  return cookie;
}

function parse(input, options) {
  if (!input) {
    return [];
  }
  if (input.headers) {
    input = input.headers["set-cookie"];
  }
  if (!Array.isArray(input)) {
    input = [input];
  }

  var defaultOptions = extend({}, defaultParseOptions);
  if (options) {
    options = extend(defaultOptions, options);
  } else {
    options = defaultOptions;
  }

  return input.filter(isNonEmptyString).map(function (str) {
    return parseString(str, options);
  });
}

/*
  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas
  that are within a single set-cookie field-value, such as in the Expires portion.
  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2
  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128
  React Native's fetch does this for *every* header, including set-cookie.
  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25
  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation
*/
function splitCookiesString(cookiesString) {
  if (Array.isArray(cookiesString)) {
    return cookiesString;
  }
  if (typeof cookiesString !== "string") {
    return [];
  }

  var cookiesStrings = [];
  var pos = 0;
  var start;
  var ch;
  var lastComma;
  var nextStart;
  var cookiesSeparatorFound;

  function skipWhitespace() {
    while (pos < cookiesString.length && /\s/.test(cookiesString.charAt(pos))) {
      pos += 1;
    }
    return pos < cookiesString.length;
  }

  function notSpecialChar() {
    ch = cookiesString.charAt(pos);

    return ch !== "=" && ch !== ";" && ch !== ",";
  }

  while (pos < cookiesString.length) {
    start = pos;
    cookiesSeparatorFound = false;

    while (skipWhitespace()) {
      ch = cookiesString.charAt(pos);
      if (ch === ",") {
        // ',' is a cookie separator if we have later first '=', not ';' or ','
        lastComma = pos;
        pos += 1;

        skipWhitespace();
        nextStart = pos;

        while (pos < cookiesString.length && notSpecialChar()) {
          pos += 1;
        }

        // currently special character
        if (pos < cookiesString.length && cookiesString.charAt(pos) === "=") {
          // we found cookies separator
          cookiesSeparatorFound = true;
          // pos is inside the next cookie, so back up and return it.
          pos = nextStart;
          cookiesStrings.push(cookiesString.substring(start, lastComma));
          start = pos;
        } else {
          // in param ',' or param separator ';',
          // we continue from that comma
          pos = lastComma + 1;
        }
      } else {
        pos += 1;
      }
    }

    if (!cookiesSeparatorFound || pos >= cookiesString.length) {
      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));
    }
  }

  return cookiesStrings;
}

module.exports = parse;
module.exports.parse = parse;
module.exports.splitCookiesString = splitCookiesString;
```

## File: miniprogram/utils/time.js
```javascript
"use strict";
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}
const Time = function (hour = 0, minute = 0, second = 0) {
  this.hour = hour
  this.minute = minute
  this.second = second
  this.string = function (num = 2) {
    let ret = []
    if (num > 0) {
      ret.push(this.hour)
    }
    if (num > 1) {
      ret.push(this.minute)
    }
    if (num > 2) {
      ret.push(this.second)
    }
    return ret.map(formatNumber).join(":")
  }
  this.value = function () {
    return this.hour * 3600 + this.minute * 60 + this.second
  }
  return this
}
const valueToTime = function (value) {
  const hour = Math.floor(value / 3600);
  value %= 3600
  const minute = Math.floor(value / 60);
  value %= 60
  return new Time(hour, minute, value)
}
const parseTime = function (str) {
  const t = str.split(":")
  return new Time(
    t.length > 0 ? parseInt(t[0]) : 0,
    t.length > 1 ? parseInt(t[1]) : 0,
    t.length > 2 ? parseInt(t[2]) : 0
  )
}

module.exports = {
  formatNumber: formatNumber,
  Time: Time,
  valueToTime: valueToTime,
  parseTime: parseTime
}
```

## File: miniprogram/utils/timeUtils.wxs
```
var formatTime = function(s){
  if(!s){
    s = ""
  }
  return s.length == 8 && s.substring(5, 8) == ":00" ? s.substring(0, 5) : s
}
module.exports = {
  formatTime: formatTime
}
```

## File: miniprogram/weui/weui.wxss
```
/*!
 * WeUI v1.1.1 (https://github.com/weui/weui-wxss)
 * Copyright 2017 Tencent, Inc.
 * Licensed under the MIT license
 */
page{line-height:1.6;font-family:-apple-system-font,Helvetica Neue,sans-serif}icon{vertical-align:middle}.weui-cells{position:relative;margin-top:1.17647059em;background-color:#fff;line-height:1.41176471;font-size:34rpx}.weui-cells:before{top:0;border-top:1rpx solid #d9d9d9}.weui-cells:after,.weui-cells:before{content:" ";position:absolute;left:0;right:0;height:2rpx;color:#d9d9d9}.weui-cells:after{bottom:0;border-bottom:1rpx solid #d9d9d9}.weui-cells__title{margin-top:.77em;margin-bottom:.3em;padding-left:30rpx;padding-right:30rpx;color:#999;font-size:28rpx}.weui-cells_after-title{margin-top:0}.weui-cells__tips{margin-top:.3em;color:#999;padding-left:30rpx;padding-right:30rpx;font-size:28rpx}.weui-cell{padding:20rpx 30rpx;position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.weui-cell:before{content:" ";position:absolute;left:0;top:0;right:0;height:2rpx;border-top:1rpx solid #d9d9d9;color:#d9d9d9;left:30rpx}.weui-cell:first-child:before{display:none}.weui-cell_active{background-color:#ececec}.weui-cell_primary{-webkit-box-align:start;-webkit-align-items:flex-start;align-items:flex-start}.weui-cell__bd{-webkit-box-flex:1;-webkit-flex:1;flex:1}.weui-cell__ft{text-align:right;color:#999}.weui-cell_access{color:inherit}.weui-cell__ft_in-access{padding-right:26rpx;position:relative}.weui-cell__ft_in-access:after{content:" ";display:inline-block;height:12rpx;width:12rpx;border-width:4rpx 4rpx 0 0;border-color:#c8c8cd;border-style:solid;-webkit-transform:matrix(.71,.71,-.71,.71,0,0);transform:matrix(.71,.71,-.71,.71,0,0);position:relative;top:-4rpx;position:absolute;top:50%;margin-top:-8rpx;right:4rpx}.weui-cell_link{color:#586c94;font-size:28rpx}.weui-cell_link:active{background-color:#ececec}.weui-cell_link:first-child:before{display:block}.weui-icon-radio{margin-left:6.4rpx;margin-right:6.4rpx}.weui-icon-checkbox_circle,.weui-icon-checkbox_success{margin-left:9.2rpx;margin-right:9.2rpx}.weui-check__label:active{background-color:#ececec}.weui-check{position:absolute;left:-9999rpx}.weui-check__hd_in-checkbox{padding-right:.35em}.weui-cell__ft_in-radio{padding-left:.35em}.weui-cell_input{padding-top:0;padding-bottom:0}.weui-label{width:210rpx;word-wrap:break-word;word-break:break-all}.weui-input{height:2.58823529em;min-height:2.58823529em;line-height:2.58823529em}.weui-toptips{position:fixed;-webkit-transform:translateZ(0);transform:translateZ(0);top:0;left:0;right:0;padding:10rpx;font-size:28rpx;text-align:center;color:#fff;z-index:5000;word-wrap:break-word;word-break:break-all}.weui-toptips_warn{background-color:#e64340}.weui-textarea{display:block;width:100%}.weui-textarea-counter{color:#b2b2b2;text-align:right}.weui-cell_warn,.weui-textarea-counter_warn{color:#e64340}.weui-cell_select{padding:0}.weui-select{position:relative;padding-left:30rpx;padding-right:60rpx;height:2.58823529em;min-height:2.58823529em;line-height:2.58823529em;border-right:1rpx solid #d9d9d9}.weui-select:before{content:" ";display:inline-block;height:12rpx;width:12rpx;border-width:4rpx 4rpx 0 0;border-color:#c8c8cd;border-style:solid;-webkit-transform:matrix(.71,.71,-.71,.71,0,0);transform:matrix(.71,.71,-.71,.71,0,0);position:relative;top:-4rpx;position:absolute;top:50%;right:30rpx;margin-top:-8rpx}.weui-select_in-select-after{padding-left:0}.weui-cell__bd_in-select-before,.weui-cell__hd_in-select-after{padding-left:30rpx}.weui-cell_vcode{padding-right:0}.weui-vcode-btn,.weui-vcode-img{margin-left:10rpx;height:2.58823529em;vertical-align:middle}.weui-vcode-btn{display:inline-block;padding:0 .6em 0 .7em;border-left:1rpx solid #e5e5e5;line-height:2.58823529em;font-size:34rpx;color:#3cc51f;white-space:nowrap}.weui-vcode-btn:active{color:#52a341}.weui-cell_switch{padding-top:12rpx;padding-bottom:12rpx}.weui-uploader__hd{display:-webkit-box;display:-webkit-flex;display:flex;padding-bottom:20rpx;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.weui-uploader__title{-webkit-box-flex:1;-webkit-flex:1;flex:1}.weui-uploader__info{color:#b2b2b2}.weui-uploader__bd{margin-bottom:-8rpx;margin-right:-18rpx;overflow:hidden}.weui-uploader__file{float:left;margin-right:18rpx;margin-bottom:18rpx}.weui-uploader__img{display:block;width:158rpx;height:158rpx}.weui-uploader__file_status{position:relative}.weui-uploader__file_status:before{content:" ";position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.weui-uploader__file-content{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);color:#fff}.weui-uploader__input-box{float:left;position:relative;margin-right:18rpx;margin-bottom:18rpx;width:154rpx;height:154rpx;border:1rpx solid #d9d9d9}.weui-uploader__input-box:after,.weui-uploader__input-box:before{content:" ";position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background-color:#d9d9d9}.weui-uploader__input-box:before{width:4rpx;height:79rpx}.weui-uploader__input-box:after{width:79rpx;height:4rpx}.weui-uploader__input-box:active{border-color:#999}.weui-uploader__input-box:active:after,.weui-uploader__input-box:active:before{background-color:#999}.weui-uploader__input{position:absolute;z-index:1;top:0;left:0;width:100%;height:100%;opacity:0}.weui-article{padding:40rpx 30rpx;font-size:30rpx}.weui-article__section{margin-bottom:1.5em}.weui-article__h1{font-size:36rpx;font-weight:400;margin-bottom:.9em}.weui-article__h2{font-size:32rpx;font-weight:400;margin-bottom:.34em}.weui-article__h3{font-weight:400;font-size:30rpx;margin-bottom:.34em}.weui-article__p{margin:0 0 .8em}.weui-msg{padding-top:72rpx;text-align:center}.weui-msg__link{display:inline;color:#586c94}.weui-msg__icon-area{margin-bottom:60rpx}.weui-msg__text-area{margin-bottom:50rpx;padding:0 40rpx}.weui-msg__title{margin-bottom:10rpx;font-weight:400;font-size:40rpx}.weui-msg__desc{font-size:28rpx;color:#999}.weui-msg__opr-area{margin-bottom:100rpx}.weui-msg__extra-area{margin-bottom:30rpx;font-size:28rpx;color:#999}@media screen and (min-height:876rpx){.weui-msg__extra-area{position:fixed;left:0;bottom:0;width:100%;text-align:center}}.weui-btn{margin-top:30rpx}.weui-btn:first-child{margin-top:0}.weui-btn-area{margin:1.17647059em 30rpx .3em}.weui-agree{display:block;padding:.5em 30rpx;font-size:26rpx}.weui-agree__text{color:#999}.weui-agree__link{display:inline;color:#586c94}.weui-agree__checkbox{position:absolute;left:-9999rpx}.weui-agree__checkbox-icon{position:relative;top:4rpx;display:inline-block;border:1rpx solid #d1d1d1;background-color:#fff;border-radius:6rpx;width:22rpx;height:22rpx}.weui-agree__checkbox-icon-check{position:absolute;top:2rpx;left:2rpx}.weui-footer{color:#999;font-size:28rpx;text-align:center}.weui-footer_fixed-bottom{position:fixed;bottom:.52em;left:0;right:0}.weui-footer__links{font-size:0}.weui-footer__link{display:inline-block;vertical-align:top;margin:0 .62em;position:relative;font-size:28rpx;color:#586c94}.weui-footer__link:before{content:" ";position:absolute;left:0;top:0;width:2rpx;bottom:0;border-left:1rpx solid #c7c7c7;color:#c7c7c7;left:-.65em;top:.36em;bottom:.36em}.weui-footer__link:first-child:before{display:none}.weui-footer__text{padding:0 .34em;font-size:24rpx}.weui-grids{border-top:1rpx solid #d9d9d9;border-left:1rpx solid #d9d9d9;overflow:hidden}.weui-grid{position:relative;float:left;padding:40rpx 20rpx;width:33.33333333%;box-sizing:border-box;border-right:1rpx solid #d9d9d9;border-bottom:1rpx solid #d9d9d9}.weui-grid_active{background-color:#ececec}.weui-grid__icon{display:block;width:56rpx;height:56rpx;margin:0 auto}.weui-grid__label{margin-top:10rpx;display:block;text-align:center;color:#000;font-size:28rpx;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.weui-loading{margin:0 10rpx;width:40rpx;height:40rpx;display:inline-block;vertical-align:middle;-webkit-animation:a 1s steps(12) infinite;animation:a 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}.weui-loading.weui-loading_transparent{background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect xmlns='http://www.w3.org/2000/svg' width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.56)' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.5)' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.43)' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.38)' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.32)' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.28)' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.25)' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.2)' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.17)' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.14)' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.1)' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.03)' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E")}@-webkit-keyframes a{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes a{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.weui-badge{display:inline-block;padding:.15em .4em;min-width:16rpx;border-radius:36rpx;background-color:#e64340;color:#fff;line-height:1.2;text-align:center;font-size:24rpx;vertical-align:middle}.weui-badge_dot{padding:.4em;min-width:0}.weui-loadmore{width:65%;margin:1.5em auto;line-height:1.6em;font-size:28rpx;text-align:center}.weui-loadmore__tips{display:inline-block;vertical-align:middle}.weui-loadmore_line{border-top:1rpx solid #e5e5e5;margin-top:2.4em}.weui-loadmore__tips_in-line{position:relative;top:-.9em;padding:0 .55em;background-color:#fff;color:#999}.weui-loadmore__tips_in-dot{position:relative;padding:0 .16em;width:8rpx;height:1.6em}.weui-loadmore__tips_in-dot:before{content:" ";position:absolute;top:50%;left:50%;margin-top:-2rpx;margin-left:-4rpx;width:8rpx;height:8rpx;border-radius:50%;background-color:#e5e5e5}.weui-panel{margin-top:20rpx;position:relative;overflow:hidden}.weui-media-box{padding:30rpx;position:relative}.weui-cells_in-small-appmsg{margin-top:0}.weui-cells_in-small-appmsg:before{display:none}.weui-progress{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.weui-progress__bar{-webkit-box-flex:1;-webkit-flex:1;flex:1}.weui-progress__opr{margin-left:30rpx;font-size:0}.weui-navbar{display:-webkit-box;display:-webkit-flex;display:flex;position:absolute;z-index:500;top:0;width:100%;border-bottom:1rpx solid #ccc}.weui-navbar__item{position:relative;display:block;-webkit-box-flex:1;-webkit-flex:1;flex:1;padding:26rpx 0;text-align:center;font-size:0}.weui-navbar__item.weui-bar__item_on{color:#242424}.weui-navbar__slider{position:absolute;content:" ";left:0;bottom:0;width:6em;height:6rpx;background-color:#242424;-webkit-transition:-webkit-transform .3s;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}.weui-navbar__title{display:inline-block;font-size:30rpx;max-width:8em;width:auto;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-wrap:normal}.weui-tab{position:relative;height:100%}.weui-tab__panel{box-sizing:border-box;height:100%;padding-top:100rpx;overflow:auto;-webkit-overflow-scrolling:touch}.weui-search-bar{position:relative;padding:16rpx 20rpx;display:-webkit-box;display:-webkit-flex;display:flex;box-sizing:border-box;background-color:#efeff4;border-top:1rpx solid #d7d6dc;border-bottom:1rpx solid #d7d6dc}.weui-icon-search{margin-right:16rpx;font-size:inherit}.weui-icon-search_in-box{position:absolute;left:20rpx;top:14rpx}.weui-search-bar__text{display:inline-block;font-size:28rpx;vertical-align:middle}.weui-search-bar__form{position:relative;-webkit-box-flex:1;-webkit-flex:auto;flex:auto;border-radius:10rpx;background:#fff;border:1rpx solid #e6e6ea}.weui-search-bar__box{position:relative;padding-left:60rpx;padding-right:60rpx;width:100%;box-sizing:border-box;z-index:1}.weui-search-bar__input{height:56rpx;line-height:56rpx;font-size:28rpx}.weui-icon-clear{position:absolute;top:0;right:0;padding:14rpx 16rpx;font-size:0}.weui-search-bar__label{position:absolute;top:0;right:0;bottom:0;left:0;z-index:2;border-radius:6rpx;text-align:center;color:#9b9b9b;background:#fff;line-height:56rpx}.weui-search-bar__cancel-btn{margin-left:20rpx;line-height:56rpx;color:#09bb07;white-space:nowrap}
```

## File: miniprogram/app.js
```javascript
"use strict";
//app.js
const api = require("./utils/api.js")
const request = require("./utils/request.js")
const time = require('./utils/time.js')
const meetings = require('./utils/meetings.js')
App({
  api: api,
  time: time,
  meetings: meetings,
  onLaunch: function (options) {
    if (wx.getUpdateManager){
      const updateManager = wx.getUpdateManager()
      updateManager.onUpdateReady(function (e) {
        updateManager.applyUpdate()
      })
    }
    // 获取用户信息
    this.userInfo()
  },
  onShow: function(options){
    request.reconnectApiViews(5)
  },
  onHide: function (options) {
  },
  globalData: {
    config: null,
    userInfo: null,
    getUserInfoing: false,
    getUserInfoPromise: [],
    logining: false,
    loginPromise: [],
    timeDifference: 0,
  },
  nowDate: function(){
    return new Date(new Date().getTime() + this.globalData.timeDifference)
  },
  config: function(){
    return new Promise((resolve, reject) => {
      if (this.globalData.config != null){
        resolve(this.globalData.config)
        return
      }
      api.api_meeting_config().then(res => {
        this.globalData.config = res
        resolve(this.globalData.config)
      }).catch(res => {
        reject(res)
      })
    })
  },
  userInfo: function () {
    return new Promise((resolve, reject) => {
      this.globalData.getUserInfoPromise.push([resolve, reject])
      if (this.globalData.getUserInfoing) {
        return
      }
      this.globalData.getUserInfoing = true
      let callback = (index, info) => {
        this.globalData.getUserInfoing = false
        while (this.globalData.getUserInfoPromise.length > 0) {
          let pro = this.globalData.getUserInfoPromise.pop()
          pro[index](info);
        }
        if (index == 0 && info.need_refresh){
          this.getUserInfo()
        }
      }
      if (this.globalData.userInfo != null) {
        callback(0, this.globalData.userInfo)
        return
      }
      api.api_wechat_user_info().then(data => {
        if (!data.avatarurl){
          this.getUserInfo().then(res => {
            callback(0, res)
          }).catch(res => {
            callback(1, res)
          })
        }else{
          this.globalData.userInfo = data
          callback(0, this.globalData.userInfo)
        }
      }).catch(res => {
        callback(1, res)
      })
    })
  },
  getUserInfo: function () {
    return new Promise((resolve, reject) => {
      this.login().then(res => {
        wx.getUserInfo({
          withCredentials: true,
          lang: 'zh_CN',
          success: res => {
            this.updateUserInfo(res.encryptedData, res.iv).then(res => {
              resolve(res)
            }).catch(res => {
              reject(res)
            })

          },
          fail(res) {
            reject(res.errMsg);
          }
        })
      })
    })
  },
  updateUserInfo: function (encryptedData, iv){
    return new Promise((resolve, reject) => {
      api.api_wechat_user_info({ encrypted_data: encryptedData, iv: iv }).then(data => {
        this.globalData.userInfo = data
        resolve(this.globalData.userInfo)
      }).catch(msg => {
        this.login().then(res => {
          reject(msg)
        })
      })
    })
  },
  onGetPhoneNumber: function (e) {
    if (e.detail.errMsg == 'getPhoneNumber:ok') {
      return this.updateUserInfo(e.detail.encryptedData, e.detail.iv)
    }else{
      return new Promise((resolve, reject) => { reject("获取失败") });
    }
  },

  onGetUserInfo: function (e) {
    if (e.detail.errMsg == 'getUserInfo:ok') {
      return this.updateUserInfo(e.detail.encryptedData, e.detail.iv)
    } else {
      return new Promise((resolve, reject) => { reject("获取失败") });
    }
  },
  gotoHome: function(){
    wx.reLaunch({
      url: '/pages/room/list',
    })
  },
  login: function() {
    return new Promise((resolve, reject) => {
      this.globalData.loginPromise.push([resolve, reject])
      if (this.globalData.logining) {
        return
      }
      this.globalData.logining = true
      let callback = (index, data) => {
        this.globalData.logining = false
        while (this.globalData.loginPromise.length > 0) {
          let pro = this.globalData.loginPromise.pop()
          pro[index](data);
        }
      }
      wx.login({
        success: res => {
          api.api_wechat_login({js_code: res.code}).then(data => {
            callback(0, data)
          })
          // 发送 res.code 到后台换取 openId, sessionKey, unionId
        },
        fail: res => {
          callback(1, res)
        }
      })
    })
  }
})
```

## File: miniprogram/app.json
```json
{
    "pages": [
      "pages/room/list",
      "pages/room/meeting",
      "pages/my/my",
      "pages/room/detail",
      "pages/room/edit",
      "pages/meeting/reserve",
      "pages/meeting/detail",
      "pages/meeting/edit",
      "pages/room/history"
    ],
    "window": {
      "backgroundTextStyle": "light",
      "navigationBarBackgroundColor": "#fff",
      "navigationBarTitleText": "",
      "navigationBarTextStyle": "black"
    },
    "tabBar": {
      "list": [
        {
          "pagePath": "pages/room/list",
          "text": "首页",
          "iconPath": "images/icon-index.png",
          "selectedIconPath": "images/icon-index-after.png"
        },
        {
          "pagePath": "pages/room/meeting",
          "text": "预约",
          "iconPath": "images/icon-meeting.png",
          "selectedIconPath": "images/icon-meeting-after.png"
        },
        {
          "pagePath": "pages/my/my",
          "text": "我的",
          "iconPath": "images/icon-my.png",
          "selectedIconPath": "images/icon-my-after.png"
        }
      ],
      "color": "#a9b7b7",
      "selectedColor": "#11cd6e",
      "backgroundColor": "#ffffff",
      "borderStyle": "black"
    }
  }
```

## File: miniprogram/app.wxss
```
/**app.wxss**/

  
button {
  padding-left:28rpx;
  padding-right:28rpx;
  font-size:36rpx;
}

button[size=mini] {
  border: 1rpx solid #ccc;
  padding:0 1em;
  font-size: 24rpx;
  white-space: nowrap;
}
button[size=mini]::after {
  border: none;
}
@import 'weui/weui.wxss';

.page{
  font-size: 32rpx;
  font-family: -apple-system-font,Helvetica Neue,Helvetica,sans-serif;
}
.page__hd {
  padding: 30rpx;
}
.page__bd {
  padding-bottom: 20rpx;
}
.page__bd_spacing {
  margin-top: 20px;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.page__ft{
  padding-bottom: 20rpx;
  text-align: center;
}

.page__title {
  margin-top: 15%;
  text-align: left;
  font-size: 40rpx;
  font-weight: 400;
}

.page__desc {
  margin-top: 10rpx;
  color: #888888;
  text-align: left;
  font-size: 28rpx;
}
.footer{
  background: #fff;
  position:fixed;
  bottom:0;
  left:0;
  right:0;
  height: 100rpx;
  padding-top: 20rpx;
}
.body-with-footer{
    margin-top: 10px;
  padding-bottom: 20rpx
}
```

## File: miniprogram/project.config.json
```json
{
    "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html",
    "packOptions": {
        "ignore": [],
        "include": []
    },
    "setting": {
        "urlCheck": false,
        "es6": true,
        "postcss": true,
        "minified": true,
        "newFeature": true,
        "autoAudits": false,
        "checkInvalidKey": true,
        "coverView": true,
        "lazyloadPlaceholderEnable": false,
        "preloadBackgroundData": false,
        "uglifyFileName": false,
        "uploadWithSourceMap": true,
        "enhance": true,
        "useMultiFrameRuntime": true,
        "showShadowRootInWxmlPanel": true,
        "packNpmManually": false,
        "packNpmRelationList": [],
        "minifyWXSS": true,
        "useStaticServer": true,
        "showES6CompileOption": false,
        "babelSetting": {
            "ignore": [],
            "disablePlugins": [],
            "outputPath": ""
        },
        "disableUseStrict": false,
        "useCompilerPlugins": false,
        "minifyWXML": true
    },
    "compileType": "miniprogram",
    "libVersion": "3.3.4",
    "appid": "wxcae796699d942bc4",
    "projectname": "meeting",
    "simulatorType": "wechat",
    "simulatorPluginLibVersion": {},
    "condition": {},
    "editorSetting": {
        "tabIndent": "insertSpaces",
        "tabSize": 4
    }
}
```

## File: miniprogram/project.private.config.json
```json
{
    "projectname": "meeting",
    "setting": {
        "compileHotReLoad": true,
        "urlCheck": false
    },
    "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html"
}
```

## File: miniprogram/sitemap.json
```json
{
  "desc": "关于本文件的更多信息，请参考文档 https://developers.weixin.qq.com/miniprogram/dev/framework/sitemap.html",
  "rules": [{
  "action": "allow",
  "page": "*"
  }]
}
```
