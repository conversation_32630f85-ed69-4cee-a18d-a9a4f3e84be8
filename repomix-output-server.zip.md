This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
server/
  apps/
    meetings/
      migrations/
        0001_initial.py
      __init__.py
      admin.py
      apps.py
      constants.py
      models.py
      serializer.py
      views.py
    wechat/
      migrations/
        0001_initial.py
      __init__.py
      admin.py
      apps.py
      backends.py
      biz.py
      constants.py
      models.py
      serializer.py
      views.py
    __init__.py
  core/
    __init__.py
    authentication.py
    constants.py
    parsers.py
    renderers.py
    storages.py
    utils.py
  meeting/
    __init__.py
    asgi.py
    celery_annotations.py
    celery.py
    constance.py
    local_settings.py
    routing.py
    settings.py
    urls.py
    views.py
    wsgi.py
  templates/
    error.html
  .gitignore
  manage.py
  requirements.txt
  setup.cfg
```

# Files

## File: server/apps/meetings/migrations/0001_initial.py
```python
# Generated by Django 3.2.9 on 2023-06-27 04:22

import cool.model.models
import core.utils
from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wechat', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='修改时间')),
                ('delete_status', models.BooleanField(choices=[(False, '正常'), (True, '已经删除')], db_index=True, default=False, verbose_name='删除状态')),
                ('remark', models.TextField(blank=True, default='', null=True, verbose_name='备注说明')),
                ('name', models.CharField(default='', max_length=64, verbose_name='名称')),
                ('description', models.CharField(blank=True, default='', max_length=255, verbose_name='描述')),
                ('date', models.DateField(db_index=True, editable=False, verbose_name='会议日期')),
                ('start_time', models.TimeField(editable=False, verbose_name='开始时间')),
                ('end_time', models.TimeField(editable=False, verbose_name='结束时间')),
            ],
            options={
                'verbose_name': '会议',
                'verbose_name_plural': '会议',
            },
            bases=(cool.model.models.ModelFieldChangeMixin, cool.model.models.ModelCacheMixin, cool.model.models.SearchModelMixin, models.Model),
            managers=[
                ('default_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='修改时间')),
                ('delete_status', models.BooleanField(choices=[(False, '正常'), (True, '已经删除')], db_index=True, default=False, verbose_name='删除状态')),
                ('remark', models.TextField(blank=True, default='', null=True, verbose_name='备注说明')),
                ('name', models.CharField(default='', max_length=64, verbose_name='名称')),
                ('description', models.CharField(blank=True, default='', max_length=255, verbose_name='描述')),
                ('qr_code', models.ImageField(default='', editable=False, max_length=512, upload_to='%Y/%m/%d/', verbose_name='二维码')),
                ('create_user_manager', models.BooleanField(default=False, help_text='会议室创建人可以管理该会议室内所有会议', verbose_name='创建人管理权限')),
                ('create_user', core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='create_rooms', to='wechat.user', verbose_name='创建人')),
            ],
            options={
                'verbose_name': '会议室',
                'verbose_name_plural': '会议室',
            },
            bases=(cool.model.models.ModelFieldChangeMixin, cool.model.models.ModelCacheMixin, cool.model.models.SearchModelMixin, models.Model),
            managers=[
                ('default_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='MeetingTrace',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='修改时间')),
                ('delete_status', models.BooleanField(choices=[(False, '正常'), (True, '已经删除')], db_index=True, default=False, verbose_name='删除状态')),
                ('remark', models.TextField(blank=True, default='', null=True, verbose_name='备注说明')),
                ('owner', models.BooleanField(verbose_name='是否发起人自己操作')),
                ('type', models.IntegerField(choices=[(10, '修改'), (20, '取消')], verbose_name='操作类型')),
                ('data', models.CharField(default='', max_length=4096, verbose_name='详细信息')),
                ('meeting', core.utils.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.DO_NOTHING, to='meetings.meeting', verbose_name='会议')),
                ('user', core.utils.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.DO_NOTHING, to='wechat.user', verbose_name='操作人')),
            ],
            options={
                'verbose_name': '会议操作历史',
                'verbose_name_plural': '会议操作历史',
            },
            bases=(cool.model.models.ModelFieldChangeMixin, cool.model.models.ModelCacheMixin, cool.model.models.SearchModelMixin, models.Model),
            managers=[
                ('default_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.AddField(
            model_name='meeting',
            name='room',
            field=core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, to='meetings.room', verbose_name='会议室'),
        ),
        migrations.AddField(
            model_name='meeting',
            name='user',
            field=core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='reserve_meetings', to='wechat.user', verbose_name='发起人'),
        ),
        migrations.CreateModel(
            name='UserFollowRoom',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='修改时间')),
                ('delete_status', models.BooleanField(choices=[(False, '正常'), (True, '已经删除')], db_index=True, default=False, verbose_name='删除状态')),
                ('remark', models.TextField(blank=True, default='', null=True, verbose_name='备注说明')),
                ('room', core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='follows', to='meetings.room', verbose_name='会议室')),
                ('user', core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, to='wechat.user', verbose_name='关注人')),
            ],
            options={
                'verbose_name': '用户关注会议室',
                'verbose_name_plural': '用户关注会议室',
                'unique_together': {('user', 'room')},
            },
            bases=(cool.model.models.ModelFieldChangeMixin, cool.model.models.ModelCacheMixin, cool.model.models.SearchModelMixin, models.Model),
            managers=[
                ('default_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='MeetingAttendee',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='修改时间')),
                ('delete_status', models.BooleanField(choices=[(False, '正常'), (True, '已经删除')], db_index=True, default=False, verbose_name='删除状态')),
                ('remark', models.TextField(blank=True, default='', null=True, verbose_name='备注说明')),
                ('meeting', core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, to='meetings.meeting', verbose_name='会议')),
                ('user', core.utils.ForeignKey(db_constraint=False, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, to='wechat.user', verbose_name='参与人')),
            ],
            options={
                'verbose_name': '参会人',
                'verbose_name_plural': '参会人',
                'unique_together': {('user', 'meeting')},
            },
            bases=(cool.model.models.ModelFieldChangeMixin, cool.model.models.ModelCacheMixin, cool.model.models.SearchModelMixin, models.Model),
            managers=[
                ('default_manager', django.db.models.manager.Manager()),
            ],
        ),
    ]
```

## File: server/apps/meetings/__init__.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

default_app_config = 'apps.meetings.apps.MeetingsConfig'
```

## File: server/apps/meetings/admin.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
```

## File: server/apps/meetings/apps.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from django.apps import AppConfig


class MeetingsConfig(AppConfig):
    name = 'apps.meetings'
    verbose_name = '会议室模块'
```

## File: server/apps/meetings/constants.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool.core import Constants


class MeetingTraceTypeCode(Constants):
    EDIT = (10, '修改')
    DELETE = (20, '取消')
```

## File: server/apps/meetings/models.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from django.db import models

from apps.wechat.models import User
from core import utils
from core.constants import DeleteCode
from core.utils import admin_register

from . import constants


@admin_register(addable=False, changeable=False, list_display=['create_user', ])
class Room(utils.BaseModel):
    name = models.CharField(verbose_name='名称', default='', max_length=64)
    description = models.CharField(verbose_name='描述', default='', max_length=255, blank=True)
    create_user = utils.ForeignKey(User, verbose_name='创建人', related_name='create_rooms', editable=False)
    qr_code = models.ImageField('二维码', upload_to="%Y/%m/%d/", max_length=512, null=False, default='', editable=False)
    create_user_manager = models.BooleanField(verbose_name='创建人管理权限', default=False,
                                              help_text='会议室创建人可以管理该会议室内所有会议')

    class Meta:
        verbose_name = verbose_name_plural = "会议室"


@admin_register(addable=False, changeable=False, list_display=['room', 'user'], list_filter=['room', ])
class UserFollowRoom(utils.BaseModel):
    user = utils.ForeignKey(User, verbose_name='关注人', editable=False)
    room = utils.ForeignKey(Room, verbose_name='会议室', related_name='follows', editable=False)

    class Meta:
        unique_together = ('user', 'room')
        verbose_name = verbose_name_plural = "用户关注会议室"


@admin_register(addable=False, changeable=False, list_display=['room', 'user'], list_filter=['date', 'room'])
class Meeting(utils.BaseModel):
    name = models.CharField(verbose_name='名称', default='', max_length=64)
    description = models.CharField(verbose_name='描述', default='', max_length=255, blank=True)
    user = utils.ForeignKey(User, verbose_name='发起人', related_name='reserve_meetings', editable=False)
    room = utils.ForeignKey(Room, verbose_name='会议室', editable=False)
    date = models.DateField(verbose_name='会议日期', db_index=True, editable=False)
    start_time = models.TimeField(verbose_name='开始时间', editable=False)
    end_time = models.TimeField(verbose_name='结束时间', editable=False)

    @property
    def attendees(self):
        return User.objects.filter(
            meetingattendee__meeting_id=self.pk, meetingattendee__delete_status=DeleteCode.NORMAL.code
        )

    class Meta:
        verbose_name = verbose_name_plural = "会议"


@admin_register(addable=False, changeable=False, list_display=['meeting', 'user'])
class MeetingAttendee(utils.BaseModel):
    meeting = utils.ForeignKey(Meeting, verbose_name='会议', editable=False)
    user = utils.ForeignKey(User, verbose_name='参与人', editable=False)

    class Meta:
        unique_together = ('user', 'meeting')
        verbose_name = verbose_name_plural = "参会人"


@admin_register(addable=False, changeable=False, list_display=['meeting', 'user'], list_filter=['owner', 'type'])
class MeetingTrace(utils.BaseModel):
    meeting = utils.ForeignKey(Meeting, verbose_name='会议')
    user = utils.ForeignKey(User, verbose_name='操作人')
    owner = models.BooleanField(verbose_name='是否发起人自己操作')
    type = models.IntegerField(verbose_name='操作类型', choices=constants.MeetingTraceTypeCode.get_choices_list())
    data = models.CharField(verbose_name='详细信息', max_length=4096, default='')

    class Meta:
        verbose_name = verbose_name_plural = "会议操作历史"
```

## File: server/apps/meetings/serializer.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from rest_framework import serializers

from apps.wechat.models import User
from apps.wechat.serializer import UserSerializer
from core import utils

from . import models


class RoomSerializer(utils.BaseSerializer):
    class Meta:
        model = models.Room
        fields = ('id', 'name', 'description')


class RoomDetailSerializer(utils.BaseSerializer):
    is_follow = serializers.SerializerMethodField("func_is_follow")

    def func_is_follow(self, obj):
        assert isinstance(self.request.user, User)
        return models.UserFollowRoom.objects.filter(user_id=self.request.user.pk, room_id=obj.pk).exists()

    class Meta:
        model = models.Room
        fields = ('id', 'name', 'description', 'qr_code', 'create_user', 'is_follow', 'create_user_manager')


class MeetingSerializer(utils.BaseSerializer):
    class Meta:
        model = models.Meeting
        fields = ('id', 'user', 'room', 'date', 'start_time', 'end_time', 'name', 'description')


class MeetingDetailSerializer(utils.BaseSerializer):
    user = UserSerializer(many=False, read_only=True)
    room = RoomSerializer(many=False, read_only=True)
    attendees = UserSerializer(many=True, read_only=True)
    is_manager = serializers.SerializerMethodField("func_is_manager")

    def func_is_manager(self, obj):
        if self.request is None or not isinstance(self.request.user, User):
            return False
        return self.request.user.pk == obj.user_id or (
                obj.room.create_user_manager and self.request.user.pk == obj.room.create_user_id
        )

    class Meta:
        model = models.Meeting
        fields = (
            'id', 'user', 'room', 'date', 'start_time', 'end_time', 'attendees', 'name', 'description', 'is_manager'
        )
```

## File: server/apps/meetings/views.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

import datetime
import json

from cool.views import ViewSite, CoolAPIException, ErrorCode, mixins
from cool.views.fields import SplitCharField
from rest_framework import fields
from constance import config
from django.db import transaction
from django.db.models import Q

from apps.wechat import biz
from apps.wechat.views import UserBaseView
from core import utils, constants as core_constants

from . import models, serializer, constants


site = ViewSite(name='meetings', app_name='meetings')


class BaseView(UserBaseView):

    @staticmethod
    def get_room_follow(room_id, user_id):
        follow, _ = models.UserFollowRoom.default_manager.get_or_create(
            room_id=room_id, user_id=user_id
        )
        return follow

    @classmethod
    def response_info_date_time_settings(cls):
        return {
            'start_time': '开始时间',
            'end_time': '结束时间',
            'start_date': '开始日期',
            'end_date': '结束日期',
            'history_start_date': '历史开始日期',
            'history_end_date': '历史结束日期'
        }

    @staticmethod
    def get_date_time_settings():
        today = datetime.date.today()
        return {
            'start_time': config.RESERVE_START_TIME,
            'end_time': config.RESERVE_END_TIME,
            'start_date': today,
            'end_date': today + datetime.timedelta(days=config.SELECT_DATE_DAYS),
            'history_start_date': today - datetime.timedelta(days=config.MAX_HISTORY_DAYS),
            'history_end_date': today
        }

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    class Meta:
        path = '/'


@site
class Config(BaseView):
    name = "配置信息"

    def get_context(self, request, *args, **kwargs):
        return {
            'reserve_start_time': config.RESERVE_START_TIME,
            'reserve_end_time': config.RESERVE_END_TIME,
            'select_date_days': config.SELECT_DATE_DAYS
        }


@site
class RoomCreate(mixins.AddMixin, BaseView):
    model = models.Room
    response_info_serializer_class = serializer.RoomSerializer
    add_fields = ['name', 'description', 'create_user_manager']

    def init_fields(self, request, obj):
        obj.create_user_id = request.user.pk
        super().init_fields(request, obj)

    def save_obj(self, request, obj):
        super().save_obj(request, obj)
        try:
            obj.qr_code = biz.get_wxa_code_unlimited_file(
                "room_%d.jpg" % obj.pk, scene="room_id=%d" % obj.pk, page="pages/room/detail"
            )
            obj.save(update_fields=['qr_code', ], force_update=True)
        except Exception:
            utils.exception_logging.exception("get_wxa_code_unlimited_file", extra={'request': request})
        self.get_room_follow(obj.pk, request.user.pk)


class RoomBase(BaseView):
    check_manager = False

    def check_api_permissions(self, request, *args, **kwargs):
        super(RoomBase, self).check_api_permissions(request, *args, **kwargs)
        room = models.Room.get_obj_by_pk_from_cache(request.params.room_id)
        if room is None:
            raise CoolAPIException(ErrorCode.ERR_MEETING_ROOM_NOT_FOUND)
        setattr(self, 'room', room)
        if self.check_manager:
            if room.create_user_id != request.user.pk:
                raise CoolAPIException(ErrorCode.ERROR_PERMISSION)

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    class Meta:
        path = '/'
        param_fields = (
            ('room_id', fields.IntegerField(label='会议室ID')),
        )


@site
class RoomEdit(RoomBase):
    name = "修改会议室"
    check_manager = True
    response_info_serializer_class = serializer.RoomSerializer

    def get_context(self, request, *args, **kwargs):
        self.room.name = request.params.name
        self.room.description = request.params.description
        update_fields = ['name', 'description']
        if request.params.create_user_manager is not None:
            self.room.create_user_manager = request.params.create_user_manager
            update_fields.append('create_user_manager')
        self.room.save(force_update=True, update_fields=update_fields)
        return self.room

    class Meta:
        param_fields = (
            ('name', fields.CharField(label='名称', max_length=64)),
            ('description', fields.CharField(label='描述', max_length=255, allow_blank=True, default="")),
            ('create_user_manager', fields.NullBooleanField(label='创建人管理权限', default=None)),
        )


@site
class RoomDelete(RoomBase):
    name = "删除会议室"
    check_manager = True

    def get_context(self, request, *args, **kwargs):
        self.room.delete()
        return {}


@site
class RoomInfo(RoomBase):
    name = "会议室信息"
    response_info_serializer_class = serializer.RoomDetailSerializer

    def get_context(self, request, *args, **kwargs):
        return self.room


@site
class RoomFollow(BaseView):
    name = "关注会议室"

    def get_context(self, request, *args, **kwargs):
        if len(request.params.room_id) > 50:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        for room_id in request.params.room_id:
            self.get_room_follow(room_id, request.user.pk).un_delete()
        return {}

    class Meta:
        param_fields = (
            ('room_id', SplitCharField(label='会议室ID列表', sep=',', child=fields.IntegerField())),
        )


@site
class RoomUnFollow(BaseView):
    name = "取消关注会议室"

    def get_context(self, request, *args, **kwargs):
        follow = models.UserFollowRoom.objects.filter(room_id=request.params.room_id, user_id=request.user.pk).first()
        if follow is None:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        follow.delete()
        return {}

    class Meta:
        param_fields = (
            ('room_id', fields.IntegerField(label='会议室ID')),
        )


@site
class FollowRooms(BaseView):
    name = "已关注会议室列表"
    response_info_serializer_class = serializer.RoomSerializer
    response_many = True

    def get_context(self, request, *args, **kwargs):
        return models.Room.objects.filter(
            follows__user_id=request.user.pk, follows__delete_status=core_constants.DeleteCode.NORMAL.code
        )


@site
class CreateRooms(BaseView):
    name = "创建会议室列表"
    response_info_serializer_class = serializer.RoomSerializer
    response_many = True

    def get_context(self, request, *args, **kwargs):
        return models.Room.objects.filter(create_user_id=request.user.pk)


@site
class RoomMeetings(BaseView):
    name = "会议室预约列表"

    @classmethod
    def response_info_data(cls):
        from cool.views.utils import get_serializer_info
        ret = cls.response_info_date_time_settings()
        ret.update({
            'rooms': get_serializer_info(serializer.RoomSerializer(), True),
            'meetings': get_serializer_info(serializer.MeetingSerializer(), True)
        })
        return ret

    def get_context(self, request, *args, **kwargs):
        if len(request.params.room_ids) > 10:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        d = datetime.date.today()
        if request.params.date is not None:
            d = request.params.date
        rooms = list(sorted(models.Room.objects.filter(
            id__in=request.params.room_ids), key=lambda x: request.params.room_ids.index(x.id)
        ))
        meetings = models.Meeting.objects.filter(room_id__in=request.params.room_ids, date=d).order_by('start_time')
        ret = self.get_date_time_settings()
        ret.update({
            'rooms': serializer.RoomSerializer(rooms, request=request, many=True).data,
            'meetings': serializer.MeetingSerializer(meetings, request=request, many=True).data
        })
        return ret

    class Meta:
        param_fields = (
            ('room_ids', SplitCharField(label='会议室ID列表', sep=',', child=fields.IntegerField())),
            ('date', utils.DateField(label='日期', default=None)),
        )


@site
class HistoryMeetings(RoomBase):
    name = "会议室预约历史"
    check_manager = True

    @classmethod
    def response_info_data(cls):
        from cool.views.utils import get_serializer_info
        ret = cls.response_info_date_time_settings()
        ret.update({'meetings': get_serializer_info(serializer.MeetingSerializer(), True)})
        return ret

    def get_context(self, request, *args, **kwargs):
        meetings = models.Meeting.objects.filter(
            room_id=self.room.pk,
            date__gte=request.params.start_date,
            date__lte=request.params.end_date
        ).order_by('date', 'start_time')
        ret = self.get_date_time_settings()
        ret.update({'meetings': serializer.MeetingSerializer(meetings, request=request, many=True).data})
        return ret

    class Meta:
        param_fields = (
            ('start_date', utils.DateField(label='开始日期')),
            ('end_date', utils.DateField(label='结束日期')),
        )


@site
class MyMeetings(BaseView):
    name = "我参与的会议列表"

    @classmethod
    def response_info_data(cls):
        from cool.views.utils import get_serializer_info
        ret = cls.response_info_date_time_settings()
        ret.update({
            'rooms': get_serializer_info(serializer.RoomSerializer(), True),
            'meetings': get_serializer_info(serializer.MeetingSerializer(), True)
        })
        return ret

    def get_context(self, request, *args, **kwargs):
        d = datetime.date.today()
        if request.params.date is not None:
            d = request.params.date
        meetings = list(models.Meeting.objects.filter(
            id__in=models.MeetingAttendee.objects.filter(
                user_id=request.user.pk, meeting__date=d
            ).values_list('meeting', flat=True)
        ))
        rooms = list(models.Room.objects.filter(id__in=set(map(lambda x: x.room_id, meetings))))
        ret = self.get_date_time_settings()
        ret.update({
            'rooms': serializer.RoomSerializer(rooms, request=request, many=True).data,
            'meetings': serializer.MeetingSerializer(meetings, request=request, many=True).data
        })
        return ret

    class Meta:
        param_fields = (
            ('date', utils.DateField(label='日期', default=None)),
        )


@site
class Reserve(BaseView):
    name = "预约会议"
    response_info_serializer_class = serializer.MeetingDetailSerializer

    @staticmethod
    def time_ok(t):
        return t.second == 0 and t.microsecond == 0 and t.minute in (0, 30)

    def get_context(self, request, *args, **kwargs):
        if request.params.start_time >= request.params.end_time:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        if not self.time_ok(request.params.start_time) or not self.time_ok(request.params.end_time):
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        now = datetime.datetime.now()
        if request.params.date == now.date() and request.params.start_time < now.time():
            raise CoolAPIException(ErrorCode.ERR_MEETING_ROOM_TIMEOVER)

        with transaction.atomic():
            if models.Meeting.objects.filter(room_id=request.params.room_id, date=request.params.date).filter(
                    (Q(start_time__lte=request.params.start_time) & Q(end_time__gt=request.params.start_time))
                    | (Q(start_time__lt=request.params.end_time) & Q(end_time__gte=request.params.end_time))
                    | (Q(start_time__lte=request.params.start_time) & Q(start_time__gt=request.params.end_time))
                    | (Q(end_time__lt=request.params.start_time) & Q(end_time__gte=request.params.end_time))
            ).select_for_update().exists():
                raise CoolAPIException(ErrorCode.ERR_MEETING_ROOM_INUSE)
            meeting = models.Meeting.objects.create(
                user_id=request.user.pk,
                room_id=request.params.room_id,
                name=request.params.name,
                description=request.params.description,
                date=request.params.date,
                start_time=request.params.start_time,
                end_time=request.params.end_time,
            )
            models.MeetingAttendee.objects.create(
                user_id=request.user.pk,
                meeting_id=meeting.pk
            )
        self.get_room_follow(request.params.room_id, request.user.pk)
        return meeting

    class Meta:
        param_fields = (
            ('room_id', fields.IntegerField(label='会议室ID')),
            ('name', fields.CharField(label='名称', max_length=64)),
            ('description', fields.CharField(label='描述', max_length=255, allow_blank=True, default="")),
            ('date', fields.DateField(label='预定日期')),
            ('start_time', fields.TimeField(label='开始时间')),
            ('end_time', fields.TimeField(label='结束时间')),
        )


class MeetingBase(BaseView):
    check_manager = False
    check_meeting_time = True
    response_info_serializer_class = serializer.MeetingDetailSerializer

    def check_api_permissions(self, request, *args, **kwargs):
        super(MeetingBase, self).check_api_permissions(request, *args, **kwargs)
        meeting = models.Meeting.get_obj_by_pk_from_cache(request.params.meeting_id)
        if meeting is None:
            raise CoolAPIException(ErrorCode.ERR_MEETING_NOT_FOUND)
        setattr(self, 'meeting', meeting)
        if self.check_manager:
            if meeting.user_id != request.user.pk and (
                    not meeting.room.create_user_manager or request.user.pk != meeting.room.create_user_id
            ):
                raise CoolAPIException(ErrorCode.ERROR_PERMISSION)
        if self.check_meeting_time:
            if datetime.datetime.combine(meeting.date, meeting.end_time) < datetime.datetime.now():
                raise CoolAPIException(ErrorCode.ERR_MEETING_FINISHED)

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    class Meta:
        path = '/'
        param_fields = (
            ('meeting_id', fields.IntegerField(label='会议ID')),
        )


@site
class Info(MeetingBase):
    name = "会议详情"
    check_meeting_time = False

    def get_context(self, request, *args, **kwargs):
        return self.meeting


@site
class Edit(MeetingBase):
    name = "会议修改"
    check_manager = True

    def get_context(self, request, *args, **kwargs):
        data = dict()
        update_fields = list()
        if self.meeting.name != request.params.name:
            data['name'] = {'from': self.meeting.name, 'to': request.params.name}
            update_fields.append('name')
            self.meeting.name = request.params.name
        if self.meeting.description != request.params.description:
            data['description'] = {'from': self.meeting.description, 'to': request.params.description}
            update_fields.append('description')
            self.meeting.description = request.params.description
        if update_fields:
            with transaction.atomic():
                self.meeting.save(force_update=True, update_fields=update_fields)
                models.MeetingTrace.objects.create(
                    meeting_id=self.meeting.pk,
                    user_id=request.user.pk,
                    owner=request.user.pk == self.meeting.user_id,
                    type=constants.MeetingTraceTypeCode.EDIT.code,
                    data=json.dumps(data, ensure_ascii=False)
                )
        return self.meeting

    class Meta:
        param_fields = (
            ('name', fields.CharField(label='名称', max_length=64)),
            ('description', fields.CharField(label='描述', max_length=255, allow_blank=True, default="")),
        )


@site
class Cancel(MeetingBase):
    name = "取消会议"
    check_manager = True
    response_info_serializer_class = None

    def get_context(self, request, *args, **kwargs):
        with transaction.atomic():
            self.meeting.delete()
            models.MeetingTrace.objects.create(
                meeting_id=self.meeting.pk,
                user_id=request.user.pk,
                owner=request.user.pk == self.meeting.user_id,
                type=constants.MeetingTraceTypeCode.DELETE.code
            )
        return {}


@site
class Join(MeetingBase):
    name = "参加会议"

    def get_context(self, request, *args, **kwargs):
        attendee, _ = models.MeetingAttendee.default_manager.get_or_create(
            meeting_id=request.params.meeting_id,
            user_id=request.user.pk
        )
        attendee.un_delete()
        self.get_room_follow(self.meeting.room_id, request.user.pk)
        return self.meeting


@site
class Leave(MeetingBase):
    name = "取消参加会议"

    def get_context(self, request, *args, **kwargs):
        attendee = models.MeetingAttendee.objects.filter(
            meeting_id=request.params.meeting_id,
            user_id=request.user.pk
        )
        if attendee is None:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        attendee.delete()
        return self.meeting


urlpatterns = site.urlpatterns
app_name = site.app_name
```

## File: server/apps/wechat/migrations/0001_initial.py
```python
# Generated by Django 3.2.9 on 2023-06-27 04:22

import cool.model.models
from django.db import migrations, models
import django.db.models.manager


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='修改时间')),
                ('delete_status', models.BooleanField(choices=[(False, '正常'), (True, '已经删除')], db_index=True, default=False, verbose_name='删除状态')),
                ('remark', models.TextField(blank=True, default='', null=True, verbose_name='备注说明')),
                ('openid', models.CharField(default='', max_length=64, unique=True, verbose_name='openId')),
                ('unionid', models.CharField(db_index=True, default='', max_length=64, verbose_name='unionId')),
                ('session_key', models.CharField(max_length=256, verbose_name='session_key')),
                ('nickname', models.CharField(default='', max_length=64, verbose_name='昵称')),
                ('gender', models.IntegerField(choices=[(0, '未知'), (1, '男'), (2, '女')], default=0, verbose_name='性别')),
                ('language', models.CharField(default='', max_length=64, verbose_name='语言')),
                ('country', models.CharField(default='', max_length=64, verbose_name='国家')),
                ('province', models.CharField(default='', max_length=64, verbose_name='省份')),
                ('city', models.CharField(default='', max_length=64, verbose_name='城市')),
                ('avatarurl', models.ImageField(default='', max_length=512, upload_to='', verbose_name='头像')),
                ('mobile', models.CharField(max_length=32, verbose_name='小程序授权手机号')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
            },
            bases=(cool.model.models.ModelFieldChangeMixin, cool.model.models.ModelCacheMixin, cool.model.models.SearchModelMixin, models.Model, cool.model.models.AbstractUserMixin),
            managers=[
                ('default_manager', django.db.models.manager.Manager()),
            ],
        ),
    ]
```

## File: server/apps/wechat/__init__.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

default_app_config = 'apps.wechat.apps.WechatConfig'
```

## File: server/apps/wechat/admin.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
```

## File: server/apps/wechat/apps.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from django.apps import AppConfig


class WechatConfig(AppConfig):
    name = 'apps.wechat'
    verbose_name = '微信模块'
```

## File: server/apps/wechat/backends.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from . import models


class WechatBackend(object):
    def authenticate(self, request, openid=None, **kwargs):
        if openid is None:
            return None
        user = models.User.get_obj_by_unique_key_from_cache(openid=openid)
        return user

    def get_user(self, user_id):
        user = models.User.get_obj_by_pk_from_cache(user_id)
        return user
```

## File: server/apps/wechat/biz.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile
from redis import Redis
from six import BytesIO
from wechatpy import WeChatClient
from wechatpy.crypto import WeChatWxaCrypto
from wechatpy.session.redisstorage import RedisStorage

redis_client = Redis.from_url(settings.REDIS_CACHE_URL)
wechat = WeChatClient(settings.WECHAT_APPID, settings.WECHAT_APPSECRET, session=RedisStorage(
    redis_client, prefix="wechat_session::%s" % settings.WECHAT_APPID
))


def decrypt_message(session_key, iv, encrypted_data):
    crypto = WeChatWxaCrypto(session_key, iv, settings.WECHAT_APPID)
    return crypto.decrypt_message(encrypted_data)


def get_wxa_code_unlimited_file(file_name, scene, **kwargs):
    file = BytesIO()
    kw = dict()
    for k in ('width', 'auto_color', 'line_color', 'page', 'is_hyaline'):
        if k in kwargs:
            kw[k] = kwargs[k]
    content = wechat.wxa.get_wxa_code_unlimited(scene, **kw)
    file.write(content.content)
    file.seek(0)
    return InMemoryUploadedFile(
        file=file,
        field_name="",
        name=file_name,
        content_type="image/jpeg",
        size=0,
        charset="",
        content_type_extra=""
    )
```

## File: server/apps/wechat/constants.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool.core import Constants


class GenderCode(Constants):
    UNKNOWN = (0, '未知')
    MALE = (1, '男')
    FEMALE = (2, '女')
```

## File: server/apps/wechat/models.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool import model
from django.db import models
from django.utils import timezone

from core import utils
from core.utils import admin_register

from . import constants


@admin_register(addable=False, changeable=False)
class User(utils.BaseModel, model.AbstractUserMixin):
    openid = models.CharField('openId', max_length=64, null=False, default='', unique=True)
    unionid = models.CharField('unionId', max_length=64, null=False, default='', db_index=True)
    session_key = models.CharField(verbose_name='session_key', max_length=256)
    nickname = models.CharField('昵称', max_length=64, null=False, default='')
    gender = models.IntegerField('性别', choices=constants.GenderCode.get_choices_list(),
                                 default=constants.GenderCode.UNKNOWN.code, null=False)
    language = models.CharField('语言', max_length=64, null=False, default='')
    country = models.CharField('国家', max_length=64, null=False, default='')
    province = models.CharField('省份', max_length=64, null=False, default='')
    city = models.CharField('城市', max_length=64, null=False, default='')
    avatarurl = models.ImageField('头像', max_length=512, null=False, default='')
    mobile = models.CharField(verbose_name='小程序授权手机号', max_length=32)

    def __str__(self):
        return self.nickname

    @classmethod
    def ex_search_fields(cls):
        ret = super(User, cls).ex_search_fields()
        ret.add('nickname')
        return ret

    def set_info(self, user_info, save=True):
        for k, v in user_info.items():
            k = k.lower()
            if k in ('subscribe', 'unionid', 'nickname', 'gender', 'language',
                     'country', 'province', 'city', 'avatarurl', 'session_key', 'mobile'):
                setattr(self, k, v)
        if save:
            self.save_changed()

    @property
    def need_refresh(self):
        """需要重新获取"""
        return (timezone.now() - self.modify_time).total_seconds() > 86400

    class Meta:
        verbose_name = verbose_name_plural = "用户"
```

## File: server/apps/wechat/serializer.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from core import utils

from . import models


class UserSerializer(utils.BaseSerializer):

    class Meta:
        model = models.User
        fields = ('id', 'nickname', 'avatarurl', 'need_refresh')
```

## File: server/apps/wechat/views.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool.views import ViewSite, CoolAPIException, ErrorCode
from rest_framework import fields
from django.contrib.auth import authenticate, login

from core import utils

from . import biz, models, serializer


site = ViewSite(name='wechat', app_name='wechat')


@site
class Login(utils.APIBase):
    name = "小程序登录"
    response_info_serializer_class = serializer.UserSerializer

    def get_context(self, request, *args, **kwargs):
        session = biz.wechat.wxa.code_to_session(request.params.js_code)
        wxa_user, new = models.User.objects.get_or_create(openid=session['openid'])
        wxa_user.set_info(session)
        login_user = authenticate(request, openid=wxa_user.openid)
        if login_user is None:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        login(request, login_user)
        return wxa_user

    class Meta:
        param_fields = (
            ('js_code', fields.CharField(label='小程序登录code', required=True)),
        )


class UserBaseView(utils.APIBase):

    def check_api_permissions(self, request, *args, **kwargs):
        super(UserBaseView, self).check_api_permissions(request, *args, **kwargs)
        if not isinstance(request.user, models.User):
            raise CoolAPIException(ErrorCode.ERR_WECHAT_LOGIN)

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    class Meta:
        path = '/'


@site
class UserInfo(UserBaseView):
    name = "小程序用户信息"
    response_info_serializer_class = serializer.UserSerializer

    def get_context(self, request, *args, **kwargs):
        if request.params.encrypted_data or request.params.iv:
            try:
                data = biz.decrypt_message(request.user.session_key, request.params.iv, request.params.encrypted_data)
            except Exception:
                utils.exception_logging.exception("decrypt_message", extra={'request': request})
                raise CoolAPIException(ErrorCode.ERROR_SYSTEM)
            request.user.set_info(data)
        return request.user

    class Meta:
        param_fields = (
            ('encrypted_data', fields.CharField(label='完整用户信息的加密数据', required=False, default=None)),
            ('iv', fields.CharField(label='加密算法的初始向量', required=False, default=None)),
        )


urlpatterns = site.urlpatterns
app_name = site.app_name
```

## File: server/apps/__init__.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
```

## File: server/core/__init__.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
```

## File: server/core/authentication.py
```python
# encoding: utf-8
from rest_framework.authentication import SessionAuthentication


class SessionAuthenticationWithOutCSRF(SessionAuthentication):

    def enforce_csrf(self, request):
        pass
```

## File: server/core/constants.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool.core import Constants


class DeleteCode(Constants):
    NORMAL = (False, '正常')
    DELETED = (True, '已经删除')


class ChangeLogStatusCode(Constants):
    PROCESSING = (0, '处理中')
    SUCCESS = (10, '成功')
    FAIL = (11, '失败')
```

## File: server/core/parsers.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from rest_framework import parsers


class RawParser(parsers.BaseParser):
    """
    Parses Raw data.
    """
    media_type = '*/*'

    def parse(self, stream, media_type=None, parser_context=None):
        return stream
```

## File: server/core/renderers.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from rest_framework import renderers


class PlainHtmlRenderer(renderers.BaseRenderer):
    media_type = 'text/html'
    format = 'html'

    def render(self, data, media_type=None, renderer_context=None):
        return data.encode(self.charset)


class PlainTextRenderer(renderers.BaseRenderer):
    media_type = 'text/plain'
    format = 'txt'

    def render(self, data, media_type=None, renderer_context=None):
        if not isinstance(data, str):
            data = str(data)
        return data.encode(self.charset)
```

## File: server/core/storages.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from django.core.files.storage import FileSystemStorage


class EnableUrlFileSystemStorage(FileSystemStorage):
    def url(self, name):
        if name.startswith("https://") or name.startswith("http://"):
            return name
        return super(EnableUrlFileSystemStorage, self).url(name)
```

## File: server/core/utils.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

import logging
import os
import tempfile
import hashlib

from django import forms
from django.db import models
from django.contrib.auth import models as auth_models
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils.encoding import force_bytes
from rest_framework import fields
from rest_framework.response import Response

from cool import model, admin
from cool.views import CoolBFFAPIView, serializer, ErrorCode, CoolAPIException, ResponseData, get_api_doc

from . import constants, renderers


exception_logging = logging.getLogger('exception')


class DeletedManager(models.Manager):

    def get_queryset(self):
        queryset = super(DeletedManager, self).get_queryset()
        return queryset.filter(delete_status=constants.DeleteCode.NORMAL.code)

    def get_all_queryset(self):
        return super(DeletedManager, self).get_queryset()


class BaseModel(model.BaseModel):
    id = models.BigAutoField('主键ID', primary_key=True)
    create_time = models.DateTimeField('创建时间', auto_now_add=True, db_index=True, editable=False)
    modify_time = models.DateTimeField('修改时间', auto_now=True, db_index=True, editable=False)
    delete_status = models.BooleanField('删除状态', choices=constants.DeleteCode.get_choices_list(),
                                        default=constants.DeleteCode.NORMAL.code, null=False, db_index=True)
    remark = models.TextField('备注说明', null=True, blank=True, default='')

    default_manager = models.Manager()
    objects = DeletedManager()

    def __str__(self):
        if hasattr(self, 'name'):
            return self.name
        else:
            return super(BaseModel, self).__str__()

    class Meta:
        abstract = True

    @classmethod
    def ex_search_fields(cls):
        ret = set()
        for field in cls._meta.fields:
            if field.name == 'name' and isinstance(field, models.CharField):
                ret.add(field.name)
        return ret

    @classmethod
    def get_search_fields(cls):
        ret = super(BaseModel, cls).get_search_fields()
        return ret.union(cls.ex_search_fields())

    def delete(self, using=None, keep_parents=False):
        if self.delete_status == constants.DeleteCode.DELETED.code:
            return
        self.delete_status = constants.DeleteCode.DELETED.code
        return self.save(using=using, force_update=True, update_fields=['delete_status', ])
        # return super(BaseModel, self).delete(using=using, keep_parents=keep_parents)

    def un_delete(self, using=None):
        if self.delete_status == constants.DeleteCode.NORMAL.code:
            return
        self.delete_status = constants.DeleteCode.NORMAL.code
        return self.save(using=using, force_update=True, update_fields=['delete_status', ])


class ChangeLogBase(BaseModel):
    status = models.IntegerField('状态', choices=constants.ChangeLogStatusCode.get_choices_list(),
                                 default=constants.ChangeLogStatusCode.PROCESSING.code, null=False, db_index=True)
    reason = models.CharField('原因', max_length=255, null=False, blank=True, default='')
    reason_type = models.CharField('关联表类型', max_length=32, null=False, blank=True, default='')
    reason_id = models.BigIntegerField('关联表ID', null=False, blank=True, default=0)
    change_value = models.DecimalField('变化数值', max_digits=12, decimal_places=2,
                                       null=False, default=0, help_text='正加负减')

    class Meta:
        index_together = ('reason_id', 'reason_type')
        abstract = True


class ForeignKey(model.ForeignKey):

    def __init__(self, to, on_delete=models.DO_NOTHING, **kwargs):
        kwargs.setdefault('db_constraint', False)
        super().__init__(to, on_delete, **kwargs)


class BaseAdmin(admin.BaseModelAdmin):
    extend_normal_fields = True
    exclude_list_display = ['remark', 'modify_time']
    list_display = []
    heads = ['id']
    tails = ['create_time', 'delete_status', 'remark']
    readonly_fields = ['create_time', 'modify_time']
    change_view_readonly_fields = []
    editable_fields = forms.ALL_FIELDS
    list_filter = ['create_time', ]
    limits = None
    advanced_filter_fields = []

    def __init__(self, *args, **kwargs):
        super(BaseAdmin, self).__init__(*args, **kwargs)

    def formfield_for_manytomany(self, db_field, request, **kwargs):
        db_field.remote_field.through._meta.auto_created = True
        return super(BaseAdmin, self).formfield_for_manytomany(db_field, request, **kwargs)

    def delete_queryset(self, request, queryset):
        # 单独调用每个model的delete，可以同时清空缓存
        for obj in queryset:
            self.delete_model(request, obj)


def handle_options(dismiss_create_time=False, **options):
    if 'list_filter' in options and not dismiss_create_time and 'create_time' not in options['list_filter']:
        options['list_filter'] = ['create_time', ] + list(options['list_filter'])
    return options


def site_register(model_or_iterable, admin_class=BaseAdmin, site=None, dismiss_create_time=False, **options):
    options = handle_options(dismiss_create_time=dismiss_create_time, **options)
    admin.site_register(model_or_iterable, admin_class, site, **options)


def admin_register(func=None, *, admin_class=BaseAdmin, site=None, dismiss_create_time=False, **options):
    options = handle_options(dismiss_create_time=dismiss_create_time, **options)
    return admin.admin_register(func=func, admin_class=admin_class, site=site, **options)


class DateField(fields.DateField):

    def validate_empty_values(self, data):
        (is_empty_value, data) = super().validate_empty_values(data)
        if not is_empty_value and data == '':
            is_empty_value = True
            data = self.get_default()
        return is_empty_value, data


class APIBase(CoolBFFAPIView):

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    @staticmethod
    def get_req_body(request):
        return request.body if request.method == 'POST' else None

    @staticmethod
    def get_appid(request):
        host = request.get_host()
        if not host.startswith("wx"):
            return None
        return host[:host.find('.')]

    def log_exception(self, request, exc, context):
        super().log_exception(request, exc, context)
        exception_logging.exception(str(exc), exc_info=exc, extra={'request': request})

    class Meta:
        path = '/'
        param_fields = (
            ('channel', fields.CharField(label='渠道码', required=False)),
            ('version', fields.CharField(label='版本号', required=False)),
        )


class AdminApi(APIBase):

    need_superuser = True

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    def check_api_permissions(self, request, *args, **kwargs):
        if not isinstance(request.user, auth_models.AbstractUser):
            raise CoolAPIException(ErrorCode.ERR_AUTH_NOLOGIN)
        if not request.user.is_active or not request.user.is_staff:
            raise CoolAPIException(ErrorCode.ERR_AUTH_PERMISSION)
        if self.need_superuser:
            if not request.user.is_superuser:
                raise CoolAPIException(ErrorCode.ERR_AUTH_PERMISSION)

    class Meta:
        path = '/'


class TextApiView(APIBase):

    def __init__(self, *args, **kwargs):
        super(TextApiView, self).__init__(*args, **kwargs)
        self.renderer_classes = (renderers.PlainTextRenderer, )

    def get_response(self, context):
        status_code = 200
        if isinstance(context, HttpResponse):
            return context
        elif isinstance(context, ResponseData):
            status_code = context.status_code
            if context.code != ErrorCode.SUCCESS:
                context = 'error: %d %s' % (context.code, context.message)
            else:
                context = context.data
        return Response(context, status=status_code)

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    class Meta:
        path = '/'


class HtmlApiView(APIBase):

    error_template = 'error.html'

    def __init__(self, *args, **kwargs):
        super(HtmlApiView, self).__init__(*args, **kwargs)
        self.renderer_classes = (renderers.PlainHtmlRenderer, )

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    def get_response(self, context):
        status_code = 200
        if isinstance(context, HttpResponse):
            return context
        elif isinstance(context, ResponseData):
            status_code = context.status_code
            if context.code != ErrorCode.SUCCESS:
                if self.error_template:
                    context = render_to_string(self.request, self.error_template, context)
                else:
                    context = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出错了</title>
</head>
<body>
    {message}
</body>
</html>
""".format(message=context.message)
            else:
                context = context.data
        return Response(context, status=status_code)

    class Meta:
        path = '/'


class BaseSerializer(serializer.BaseSerializer):
    pass


def get_temp_file(content):
    content = force_bytes(content)
    m = hashlib.md5()
    m.update(content)
    filename = "%s.tmp" % m.hexdigest()
    filename = os.path.join(tempfile.gettempdir(), filename)
    if not os.path.exists(filename):
        with open(filename, 'wb') as f:
            f.write(content)
    return filename


def get_api_js(
    request,
    *args,
    base_view=APIBase,
    exclude_params=(),
    exclude_base_view_params=True,
    exclude_views=(),
    **kwargs
):
    return HttpResponse(get_api_doc(
        request=request,
        template_name='cool/views/api.js',
        base_view=base_view,
        exclude_params=exclude_params,
        exclude_base_view_params=exclude_base_view_params,
        exclude_views=exclude_views
    ), 'application/javascript; charset=utf-8', 200)
```

## File: server/meeting/__init__.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
```

## File: server/meeting/asgi.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

import os
import django
from channels.routing import get_default_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "meeting.settings")
django.setup()
application = get_default_application()
```

## File: server/meeting/celery_annotations.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
import logging


logger = logging.getLogger('celery')


def on_bound(*args, **kwargs):
    logger.info("celery on_bound %s %s", args, kwargs)


def on_failure(task, exc, task_id, args, kwargs, einfo):
    logger.error("celery on_failure %s %s", (task, exc, task_id, args, kwargs, einfo), {}, exc_info=exc)


def on_retry(*args, **kwargs):
    logger.info("celery on_retry %s %s", args, kwargs)


def on_success(*args, **kwargs):
    logger.info("celery on_success %s %s", args, kwargs)


def after_return(*args, **kwargs):
    logger.info("celery after_return %s %s", args, kwargs)


celery_annotations_dict = {
    'on_failure': on_failure, 'on_retry': on_retry, 'on_success': on_success, 'after_return': after_return
}
```

## File: server/meeting/celery.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
import os
from celery import Celery, signals
from django.utils.log import configure_logging


# set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'meeting.settings')


def configure_logger(conf=None, **kwargs):
    from django.conf import settings
    configure_logging(settings.LOGGING_CONFIG, settings.LOGGING)


signals.worker_process_init.connect(configure_logger)
signals.beat_init.connect(configure_logger)

app = Celery('meeting')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()


@app.task(bind=True)
def _async_call(self, func, args, kwargs):
    return func(*args, **kwargs)


def async_call(func, *args, **kwargs):
    return _async_call.apply_async((func, args, kwargs), time_limit=3600, soft_time_limit=3600)
```

## File: server/meeting/constance.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

import datetime
from collections import OrderedDict


CONSTANCE_CONFIG = OrderedDict((
    ('IS_PRODUCTION', (False, '是否生产环境')),
    ('RESERVE_START_TIME', (datetime.time(7, 0), '预约起始时间')),
    ('RESERVE_END_TIME', (datetime.time(22, 30), '预约结束时间')),
    ('SELECT_DATE_DAYS', (19, '日期选项今天之后可选天数')),
    ('MAX_HISTORY_DAYS', (366, '查看历史支持天数')),
))
```

## File: server/meeting/local_settings.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

SECRET_KEY = 'eb1dmEZG90E9x7tMcvZ/sIT9D5CEvT49jVD8C8uwF+4='

REDIS_HOST = '127.0.0.1'
REDIS_PASSWORD = ''
REDIS_PORT = '6379'
REDIS_CACHE_DB = 5
REDIS_SESSION_DB = 6
REDIS_CELERY_DB = 7
REDIS_CONSTANCE_DB = 8
REDIS_CHANNEL_DB = 9

# CREATE SCHEMA `meeting` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ;

MYSQL_HOST = '127.0.0.1'
MYSQL_PORT = '3306'
MYSQL_DBNAME = 'meeting'
MYSQL_USERNAME = 'meeting'
MYSQL_PASSWORD = 'gogogo000'

WECHAT_APPID = 'wxcae796699d942bc4'
WECHAT_APPSECRET = 'f83f53f65c3431f7d2144a890f2d2bfa'
```

## File: server/meeting/routing.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from channels.auth import AuthMiddlewareStack
from channels.routing import ProtocolTypeRouter, URLRouter
from django.urls import path

from cool.views.websocket import CoolBFFAPIConsumer


class MeetingConsumer(CoolBFFAPIConsumer):

    def accept(self, subprotocol=None):
        return super().accept('apiview')


def get_app():
    if hasattr(MeetingConsumer, 'as_asgi'):
        return MeetingConsumer.as_asgi()
    else:
        return MeetingConsumer


application = ProtocolTypeRouter({
    'websocket': AuthMiddlewareStack(
        URLRouter(
            [path('wsapi', get_app())],
        )
    ),
})
```

## File: server/meeting/settings.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
"""
Django settings for meeting project.

Generated by 'django-admin startproject' using Django 1.11.22.

For more information on this file, see
https://docs.djangoproject.com/en/dev/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/dev/ref/settings/
"""

import os
from django.conf import global_settings
from kombu import Exchange, Queue
from rest_framework import ISO_8601

from . import local_settings as ls
from .local_settings import *  # NOQA
from .constance import CONSTANCE_CONFIG  # NOQA
from .celery_annotations import celery_annotations_dict


# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/dev/howto/deployment/checklist/

# SECURITY WARNING: SECRET_KEY is in local_settings

DEBUG = os.environ.get('IS_DEBUG', '1') != '0'

ALLOWED_HOSTS = ['*', ]

REDIS_CACHE_URL = 'redis://%s%s@%s:%s/%d' % (
    ':' if ls.REDIS_PASSWORD else '',
    ls.REDIS_PASSWORD,
    ls.REDIS_HOST,
    ls.REDIS_PORT,
    ls.REDIS_CACHE_DB)


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'channels',
    'rest_framework',
    'constance',
    'cool',
    'apps.wechat',
    'apps.meetings',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

SESSION_ENGINE = "redis_sessions.session"

SESSION_REDIS = {
    'host': ls.REDIS_HOST,
    'port': ls.REDIS_PORT,
    'db': ls.REDIS_SESSION_DB,
    'password': ls.REDIS_PASSWORD,
    'prefix': 'session',
    'socket_timeout': 1
}

CACHES = {
    'default': {
        'BACKEND': 'redis_cache.RedisCache',
        'LOCATION': [
            '%s:%s' % (ls.REDIS_HOST, ls.REDIS_PORT),
        ],
        'OPTIONS': {
            'DB': ls.REDIS_CACHE_DB,
            'PASSWORD': ls.REDIS_PASSWORD,
            'PARSER_CLASS': 'redis.connection.HiredisParser',
            'CONNECTION_POOL_CLASS': 'redis.BlockingConnectionPool',
            'CONNECTION_POOL_CLASS_KWARGS': {
                'max_connections': 50,
                'timeout': 20,
            },
            'MAX_CONNECTIONS': 1000,
            'PICKLE_VERSION': -1,
        },
    },
}


WSGI_APPLICATION = 'meeting.wsgi.application'
ASGI_APPLICATION = "meeting.routing.application"

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [
                'redis://%s%s@%s:%s/%d' % (
                    ':' if ls.REDIS_PASSWORD else '',
                    ls.REDIS_PASSWORD,
                    ls.REDIS_HOST,
                    ls.REDIS_PORT,
                    ls.REDIS_CHANNEL_DB
                )
            ]
        }
    },
}

# Database
# https://docs.djangoproject.com/en/dev/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': ls.MYSQL_DBNAME,
        'USER': ls.MYSQL_USERNAME,
        'PASSWORD': ls.MYSQL_PASSWORD,
        'HOST': ls.MYSQL_HOST,
        'PORT': ls.MYSQL_PORT,
        'TEST_CHARSET': "utf8mb4",
        'TEST_COLLATION': "utf8mb4_unicode_ci",
        'STORAGE_ENGINE': 'INNODB',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# Password validation
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'apps.wechat.backends.WechatBackend',
)

# Internationalization
# https://docs.djangoproject.com/en/dev/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/dev/howto/static-files/


CONSTANCE_BACKEND = 'constance.backends.redisd.RedisBackend'

CONSTANCE_REDIS_PREFIX = 'constance:'

CONSTANCE_REDIS_CONNECTION = {
    'host': ls.REDIS_HOST,
    'port': ls.REDIS_PORT,
    'db': ls.REDIS_CONSTANCE_DB,
    'password': ls.REDIS_PASSWORD,
}
DATE_FORMAT = '%Y-%m-%d'
TIME_FORMAT = '%H:%M:%S'
DATETIME_FORMAT = DATE_FORMAT + ' ' + TIME_FORMAT

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'core.authentication.SessionAuthenticationWithOutCSRF',
    ),
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
        'rest_framework.parsers.JSONParser',
        'core.parsers.RawParser',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PERMISSION_CLASSES': ('rest_framework.permissions.AllowAny',),
    'DATE_FORMAT': DATE_FORMAT,
    'DATE_INPUT_FORMATS': [ISO_8601] + global_settings.DATE_INPUT_FORMATS,

    'DATETIME_FORMAT': DATETIME_FORMAT,
    'DATETIME_INPUT_FORMATS': [ISO_8601] + global_settings.DATETIME_INPUT_FORMATS,

    'TIME_FORMAT': TIME_FORMAT,
    'TIME_INPUT_FORMATS': [ISO_8601] + global_settings.TIME_INPUT_FORMATS,
}

if DEBUG:
    REST_FRAMEWORK['DEFAULT_RENDERER_CLASSES'].append('rest_framework.renderers.BrowsableAPIRenderer')

EMAIL_SUBJECT_PREFIX = '[meeting]'

ROOT_URLCONF = 'meeting.urls'

DEFAULT_FILE_STORAGE = 'core.storages.EnableUrlFileSystemStorage'

TEMPLATE_DIR = os.path.join(BASE_DIR, 'templates/')

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [TEMPLATE_DIR],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.static',
            ],
        },
    },
]
LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'formatters': {
        'standard': {
            'format': '%(asctime)s %(process)s.%(thread)s %(levelname)s %(module)s.%(funcName)s %(message)s',
            'datefmt': "%y/%m/%d %H:%M:%S",
        },
    },
    'handlers': {
        'mail_admins': {
            'level': 'ERROR',
            'class': 'django.utils.log.AdminEmailHandler',
            'include_html': True,
            'formatter': 'standard'
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
    },
    'loggers': {
        'daphne': {
            'handlers': ['console', ],
            'level': 'DEBUG',
            'propagate': True
        },
        'django': {
            'handlers': ['console', ],
            'level': 'INFO',
            'propagate': True
        },
        'django.db': {
            'handlers': ['console', ],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console', 'mail_admins', ],
            'level': 'ERROR',
            'propagate': False,
        },
        'exception': {
            'handlers': ['console', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False
        },
        '': {
            'handlers': ['console', ],
            'level': 'DEBUG',
            'propagate': True
        }
    },
    'root': {
        'handlers': ['console', ],
        'level': 'DEBUG',
        'propagate': True
    }
}
STATIC_URL = '/static/'
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'upload')
STATIC_ROOT = os.path.join(BASE_DIR, 'www_static')

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static/'),
)
# celery settings

CELERY_BROKER_URL = 'redis://%s%s@%s:%s/%d' % (
    ':' if ls.REDIS_PASSWORD else '',
    ls.REDIS_PASSWORD,
    ls.REDIS_HOST,
    ls.REDIS_PORT,
    ls.REDIS_CELERY_DB)

CELERY_RESULT_BACKEND = CELERY_BROKER_URL

CELERY_WORKER_HIJACK_ROOT_LOGGER = False

# 任务执行最长时间20分钟
CELERY_TASK_SOFT_TIME_LIMIT = 1200
CELERY_TASK_TIME_LIMIT = 1200

CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_DEFAULT_QUEUE = 'default'

CELERY_TASK_SERIALIZER = 'pickle'
CELERY_RESULT_SERIALIZER = 'pickle'
CELERY_ACCEPT_CONTENT = ['pickle', 'json']

# 定义执行队列
CELERY_TASK_QUEUES = (
    Queue('default', Exchange('default'), routing_key='default'),
    Queue('crontab', Exchange('crontab'), routing_key='crontab'),
    Queue('async', Exchange('async'), routing_key='async')
)

# 制定特定任务路由到特定执行队列
CELERY_TASK_ROUTES = {
    'meeting.celery._async_call': {'queue': 'async', 'routing_key': 'async'},
}

CELERY_TASK_ANNOTATIONS = {'*': celery_annotations_dict}

DJANGO_COOL = {
    'API_WS_REQ_ID_NAME': 'reqid',
    'API_EXCEPTION_DEFAULT_STATUS_CODE': 200,
    'API_PARAM_ERROR_STATUS_CODE': 200,
    'API_SYSTEM_ERROR_STATUS_CODE': 200,

    'API_ERROR_CODES': (
        ('ERR_WECHAT_LOGIN', (10001, '需要登录')),

        ('ERR_MEETING_ROOM_TIMEOVER', (20001, '时间已过')),
        ('ERR_MEETING_ROOM_INUSE', (20002, '时间冲突')),
        ('ERR_MEETING_ROOM_NOT_FOUND', (20003, '会议室未找到')),
        ('ERR_MEETING_NOT_FOUND', (20004, '会议未找到')),
        ('ERR_MEETING_FINISHED', (20005, '会议已结束')),

    )
}
```

## File: server/meeting/urls.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from django.conf.urls.static import static

"""meeting URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/dev/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  url(r'^$', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  url(r'^$', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.conf.urls import url, include
    2. Add a URL to urlpatterns:  url(r'^blog/', include('blog.urls'))
"""
from django.conf import settings
from django.urls import include, path
from django.contrib import admin
from core.utils import get_api_js
from .views import home

api_patterns = [
    path('wechat/', include("apps.wechat.views")),
    path('meeting/', include("apps.meetings.views")),
]

urlpatterns = [
    path('cool/', include('cool.urls')),
    path('sysadmin/', admin.site.urls),
    path('api/', include(api_patterns)),
    path('', home, name='home'),
]
if settings.DEBUG:
    urlpatterns.append(path('api.js', get_api_js))
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

## File: server/meeting/views.py
```python
from django.http import HttpResponse

def home(request):
    return HttpResponse('<h1>Hello world!</h1>')
```

## File: server/meeting/wsgi.py
```python
# encoding: utf-8
from __future__ import absolute_import, unicode_literals
"""
WSGI config for meeting project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/dev/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "meeting.settings")

application = get_wsgi_application()
```

## File: server/templates/error.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出错了</title>
</head>
<body>
    {{ message }}
</body>
</html>
```

## File: server/.gitignore
```
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# IPython Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# dotenv
.env

# virtualenv
venv/
ENV/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

.idea/
.DS_Store
www_static/
upload/
migrations/
```

## File: server/manage.py
```python
#!/usr/bin/env python
import os
import sys

if __name__ == "__main__":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "meeting.settings")
    try:
        from django.core.management import execute_from_command_line
    except ImportError:
        # The above import may fail for some other reason. Ensure that the
        # issue is really that Django is missing to avoid masking other
        # exceptions on Python 2.
        try:
            import django
        except ImportError:
            raise ImportError(
                "Couldn't import Django. Are you sure it's installed and "
                "available on your PYTHONPATH environment variable? Did you "
                "forget to activate a virtual environment?"
            )
        raise
    execute_from_command_line(sys.argv)
```

## File: server/requirements.txt
```
Pillow==8.4.0
mysqlclient==2.0.3
cryptography==35.0.0
redis==3.5.3
hiredis==2.0.0
gevent==21.8.0
celery==5.2.0
requests==2.26.0
Django==3.2.9
channels==3.0.4
channels-redis==3.3.1
django-constance==2.8.0
django-redis-sessions==0.6.2
django-redis-cache==3.0.0
djangorestframework==3.12.4
django-cool==1.1.4
wechatpy==1.8.18
```

## File: server/setup.cfg
```
[flake8]
exclude = .svn,CVS,.bzr,.hg,.git,__pycache,.ropeproject,venv,migrations
max-line-length = 120
```
