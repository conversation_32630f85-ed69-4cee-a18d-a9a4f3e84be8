/* pages/room/detail.wxss */

.weui-cell {
top: 0px;
margin: 0px;
}

.background-image {

    height: 160px;
    width: 210px;
    margin-left: 150px;
    margin-bottom: -150px;
}
.weui-ico-edit {
    border-radius: 18px; /* 圆角 */
    padding: 0; /* 移除内边距 */
    width: 42px;
    height: 30px;
    border: 1px solid #ffffff; /* 设置边框 */
    display: flex; /* 使用 Flexbox 布局 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
}

.icon-edit {
    width: 20px; /* 图标宽度 */
    height: 20px; /* 图标高度 */
}


  .weui-flex{
    background-color: #37C470; /* 淡绿色 */
    border-radius: 18px;
    display: flex;
    justify-content: space-between; /* 两端对齐，确保左边的查看历史和右边的按钮分开 */
    align-items: center; /* 垂直居中 */
    padding: 10px; /* 根据需要调整 */
    margin-top: 10px;
    margin-left: 0px;
  }

  .history-btn {
    padding: 0; /* 移除内边距 */
    border-radius: 18px; /* 圆角 */
    width: 81px;
    height: 30px;
    color: #ffffff; /* 文字颜色 */
    border: 1px solid #ffffff; /* 边框样式 */
    background-color: transparent; /* 移除背景颜色 */
    font-size: 12px; /* 字体大小 */
    margin-left: 126px;
    line-height: 30px; /* 文字垂直居中 */
    text-align: center; /* 文字水平居中 */
  }
  
  

  .follow-btn,
  .unfollow-btn {
    padding: 0px 10px; /* 调整内边距以缩小按钮 */
    width: 81px;
    height: 30px;
    border-radius: 18px; /* 圆角 */
    border: none; /* 去除边框 */
    font-size: 12px; /* 文字大小 */
    transition: background-color 0.8s, color 0.3s; /* 过渡动画 */
  }
  
  .unfollow-btn {
    background-color: #ffffff; /* 淡绿色 */
    color: #131313; /* 淡绿色文字 */
  }
  
  .follow-btn {
    background-color: #181818; /* 关注按钮背景色 */
    color: #ffffff; /* 关注按钮文字颜色 */
  }
  
  .follow-btn:active,
  .unfollow-btn:active {
    background-color: #666666; /* 点击时的背景色 */
    color: #ffffff; /* 点击时的文字颜色 */
  }
  
  
  .qrcode-icon {
    width: 24px; /* 图标宽度 */
    height: 24px; /* 图标高度 */
    display: block; /* 确保图像块级显示 */
    margin-right: 10px; /* 右边距，确保贴着右边显示 */
  }

  .container {
    border-top-left-radius: 24px; /* 左上角圆角 */
    border-top-right-radius: 24px; /* 右上角圆角 */
    padding: 6px 0 240px; /* 顶部内边距为 6px */
    background-color: rgb(247, 247, 247); /* 白色背景 */
    margin-top: 10px;
  }
  
  .placeholder {
    height: 30px; /* 占位符高度 */
    bottom: 46px; /* 距离底部的距离，应为容器底部内边距 + 提示信息高度 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度占满容器 */
    text-align: center; /* 文本水平居中 */
    color: #888; /* 提示信息颜色 */
    font-size: 14px; /* 提示信息字体大小 */
    line-height: 30px; /* 行高与容器高度一致 */
  }
  
  .icon {
    width: 18px; /* 图标宽度 */
    height: 18px; /* 图标高度 */
    margin-right: 6px; /* 图标与文字间距 */
    margin-bottom: -4px;
  }

  .meeting-item {
    background-color: #ffffff; /* 白色背景 */
    border-radius: 12px; /* 圆角边缘 */
    box-shadow: 0 3px 20px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
    margin: 15px; /* 添加外边距，确保卡片之间有间隔 */
    padding: 15px; /* 内边距，确保内容不会紧贴边缘 */
  }
  
  
  
  .weui-form-preview__item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px; /* 每项之间的间隔 */
  }
  
  .weui-form-preview__label {
    color: rgb(44, 44, 44); /* 标签颜色 */
    font-size: 14px; /* 标签字体大小 */
  }
  
  .weui-form-preview-time {
    color: #333; /* 值的颜色 */
    font-size: 21px; /* 值的字体大小 */
    text-align: right; /* 值的对齐方式 */
  }

  .weui-form-preview__value {
    color: #333; /* 值的颜色 */
    font-size: 16px; /* 值的字体大小 */
    margin-top: 0px;
    margin-bottom: 5px;
    text-align: right; /* 值的对齐方式 */
  }
  
.qr_code{
  width: 100%
}
.weui-footer-detail {
    position: fixed; /* 固定定位 */
    bottom: 0; /* 底部对齐 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度占满整个屏幕 */
    background-color: #ffffff; /* 底部导航栏背景色 */
    display: flex; /* 使用 Flexbox 布局 */
    justify-content: space-around; /* 水平居中并平均分配空间 */
    padding: 20px 0; /* 上下内边距 */
  }

.footer-reserve,
.footer-share {
    padding: 5px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    width: 120px;
    height: 45px;
    border: 1px solid #000000; /* 边框 */
    font-size: 14px; /* 文字大小 */
    background-color: transparent; /* 透明背景 */
    color: #000000; /* 文字颜色 */
}

.not-room-image {
    margin-top: 30px;
    display: block; /* 设置为块级元素 */
    margin-left: auto; /* 居中 */
    margin-right: auto; /* 居中 */
    transform: scale(0.8); /* 缩小图片尺寸 */
    filter: grayscale(100%) brightness(110%); /* 应用灰度滤镜并增加亮度 */
}




.not-room-desc {
    color: #666; /* 描述文字使用较深的灰色 */
    font-size: 14px; /* 调整字体大小 */
    margin-top: 10px;
    text-align: center; /* 文字水平居中 */
  }