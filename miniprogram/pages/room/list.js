// pages/room/list.js
const app = getApp();

Page({
  data: {
    rooms: [], // 存放会议室信息
  },

  onLoad: function() {
    this.loadRooms(); // 初次加载时调用加载会议室信息
  },

  onShow: function() {
    this.loadRooms(); // 每次页面显示时重新加载会议室信息
  },

  loadRooms: function() {
    app.api.api_meeting_follow_rooms().then(res => {
      console.log("API响应数据:", res); // 打印API响应的数据
      this.setData({
        rooms: res,
      });
    }).catch(err => {
      console.log('加载会议室信息失败', err);
      // 可以添加用户反馈，例如一个错误提示
      wx.showToast({
        title: '加载会议室信息失败',
        icon: 'none'
      });
    });
  },

  detail: function(e) {
    var roomId = e.currentTarget.dataset.id; // 通过data-id传递的会议室ID
    wx.navigateTo({
        url: 'detail?room_id=' + roomId
    });
  },

  create: function(e){
    wx.navigateTo({
      url: 'edit',
    })
  },

  onPullDownRefresh: function () {
    this.loadRooms(); // 下拉刷新时也重新加载数据
    wx.stopPullDownRefresh(); // 停止下拉刷新动作
  },

  onShareAppMessage: function () {
    // 分享逻辑，如果您有选择会议室进行分享的功能，确保逻辑与数据结构一致
    if (this.data.shareSelect && this.data.shareSelect.length === 0) {
      return {};
    }
    let title = this.data.rooms.filter(room => this.data.shareSelect.includes(room.id)).map(room => room.name);
    return {
      title: title.join(", "),
      path: '/pages/room/list?room_ids=' + this.data.shareSelect.join(",")
    };
  },

  // 其他生命周期函数和事件处理函数留空，按需要实现
});
