<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<!--pages/room/detail.wxml-->
<view class="page__hd">
  <view class='weui-cell'>
    <view class='weui-cell__bd'>
        <view class="weui-flex__item" bindtap='show_qrcode'>
        <image src="/images/icon-qrcode.png" class="qrcode-icon"></image>
      </view>
      <image class="background-image" src="/images/bg-meetings.svg"></image>
      <view class="page__title">{{info.name}}</view>
      <view class="page__desc">{{info.description}}</view>
  </view>
</view>


    <!-- 这里放置背景图片下的其他内容 -->
    <!-- 修改开始：确保所有视图标签正确闭合 -->
    <view class="weui-flex">

    <view class="weui-flex__item">
    <button class="follow-btn" bindtap='follow' wx:if="{{!info.is_follow}}">关注</button>
    <button class="unfollow-btn" bindtap='unfollow' wx:else>取消关注</button>
    </view>

      <view class="weui-flex__item">
        <button class="history-btn" bindtap='history' wx:if="{{owner}}">查看历史</button>
      </view>
      <block wx:if="{{owner}}">
        <view class="weui-ico-edit">
          <image src="/images/icon-edit.png" class="icon-edit" bindtap='edit'></image>
        </view>
      </block>
    </view>

</view>

<date_select id="date_select" bindchange="date_select_change"></date_select>

  
<view class="container">
  <block wx:if="{{meetings.length > 0}}">
    <block wx:for="{{meetings}}" wx:key="id">
      <view class='meeting-item' id="{{item.id}}" bindtap='detail'>
        <view class="weui-form-preview__item">
          <view class="weui-form-preview__label">
            <image src="/images/ic_clock.png" class="icon"></image>预定时间</view>
          <view class="weui-form-preview-time">{{timeUtils.formatTime(item.start_time)}} - {{timeUtils.formatTime(item.end_time)}}</view>
        </view>
        <view class="weui-form-preview__bd">
          <view class="weui-form-preview__item">
            <view class="weui-form-preview__label">
              <image src="/images/ic_daipingjia.png" class="icon"></image>用途</view>
            <view class="weui-form-preview__value">{{item.name}}</view>
          </view>
          <view class="weui-form-preview__item">
            <view class="weui-form-preview__label">
              <image src="/images/ic_edit.png" class="icon"></image>备注</view>
            <view class="weui-form-preview__value">{{item.description}}</view>
          </view>
        </view>
      </view>
    </block>
  </block>
  <block wx:else>
    <image class="not-room-image" src="/images/icon-meetings-not.svg"></image>
    <view class="placeholder">暂无人预约~</view>
  </block>
</view>


  <view class="weui-footer-detail">
        <button class="footer-reserve" bindtap='reserve'>预约</button>
        <button class="footer-share" open-type="share">分享</button>
  </view>
  
<modal class="modal" hidden="{{!show_qr_code}}" no-cancel bindconfirm="hide_qrcode" confirmText="确定" title="{{info.name}} 专属二维码">
  <view class="dew">  
    <image class="qr_code" src="{{info.qr_code}}" mode="aspectFit" show-menu-by-longpress></image>
    <view class="vedw">长按二维码可保存到相册</view>   
  </view>
</modal>