// pages/meeting/meeting.js
const app = getApp();
Page({
    data: {
      rooms: [], // 存放会议室信息
      meetings: [], // 存放用户的会议信息
      meetingId: [], // 存放用户的会议信息
    },
  
    onLoad: function() {
      this.loadMeetings();
    },
  
    loadMeetings: function() {
      app.api.api_meeting_my_meetings().then(res => {
      console.log("API响应数据:", res); // 这里打印API响应的数据
        // 映射会议数组，为每个会议添加会议室名称
        const meetingsWithRoomName = res.meetings.map(meeting => {
          const room = res.rooms.find(r => r.id === meeting.room);
          const meetingId = res.meetings.id;
          return {
            ...meeting,
            roomName: room ? room.name : '未知会议室', // 如果找不到对应的会议室，使用"未知会议室"
          };
        });
        
        this.setData({
          meetings: meetingsWithRoomName,
          rooms: res.rooms,
        });
      }).catch(err => {
        console.log('加载会议信息失败', err);
      });
    },
  
    detail: function(e) {
        wx.navigateTo({
            url: '../meeting/detail?meeting_id=' + e.currentTarget.id
          })
    },
  });
  