/* pages/room/list.wxss */
.content-wrapper {
    padding: 60px 0 10px; /* 顶部内边距为 24px */
    position: relative; /* 相对定位 */
    overflow: hidden; /* 隐藏超出卡片边界的内容 */
  }

  .page {
    border-top-left-radius: 24px; /* 左上角圆角 */
    border-top-right-radius: 24px; /* 右上角圆角 */
    padding: 6px 0 70px; /* 顶部内边距为 6px */
    background-color: rgb(247, 247, 247); /* 白色背景 */

}


/* 在你的 CSS 文件中添加以下样式 */
.background-image {
    position: fixed; /* 设置为 fixed，相对于视口固定位置 */
    width: 440px;
    height: 440px;
    margin-left: 90px;
    margin-top: -165px;
    z-index: -1; /* 将背景图放在最底层 */
}

  .grey-background {
    margin-top: 10px;
  }
  

  .create-room-btn {
    position: relative; /* 相对定位 */
    margin-top: 1%;
    margin-left: 15px;
    padding: 0px 0px; /* 调整内边距以缩小按钮 */
    width: 33px;
    height: 33px;
    border-radius: 10px; /* 圆角 */
    border: none; /* 去除边框 */
    text-align: center; /* 文本水平居中 */
    line-height: 30px; /* 文字垂直居中 */
    font-size: 27px; /* 文字大小 */
    background-color: #131313d5;
    color: #e7e7e7;
  }

  

  /* 会议室小卡片样式 */
  .weui-media-box {
      position: relative; /* 相对定位 */
      overflow: hidden; /* 隐藏超出卡片边界的内容 */
      background-color: #ffffff; /* 白色背景 */
      border-radius: 12px; /* 圆角边缘 */
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.02); /* 添加阴影效果 */
      margin: 15px; /* 添加一些外边距，确保卡片之间有间隔 */
      padding: 27px; /* 内边距，确保内容不会紧贴边缘 */
      margin-bottom: 20px;
    }
/* 在你的 CSS 文件中添加以下样式 */
  
.icon {
    position: absolute; /* 绝对定位 */
    bottom: -80px; /* 距离卡片底部的距离 */
    right: -100px; /* 距离卡片右侧的距离 */
    max-width: calc(190% - 20px); /* 图标最大宽度，考虑了右边距和左边距 */
    max-height: calc(190% - 20px); /* 图标最大高度，考虑了上边距和下边距 */
    transform: rotate(0deg); /* 旋转45度 */
    opacity: 0.6; /* 设置透明度为50% */
  }
  
    .weui-media-list__title {
      font-weight: bold; /* 标题加粗 */
      font-size: 24px;
      margin-top: 70px;
      margin-left: 15px;
    }
    
    .weui-media-list__desc {
      color: rgb(80, 80, 80); /* 描述文字使用较深的灰色 */
      font-size: 18px; /* 调整字体大小 */
      margin-top: 10px;
      margin-left: 15px;
    }
  
    .weui-panel__hd {
      font-weight: bold;
      color: rgb(20, 20, 20); /* 描述文字使用较深的灰色 */
      font-size: 15px; /* 调整字体大小 */
      margin-top: 20px;
      margin-left: 15px;
      margin-bottom: -12px;
    }
    .weui-panel-title {
        font-size: 16px;
        margin-left: 15px;
        margin-top: 10px;
        font-weight: bold; /* 标题加粗 */
      }

    .weui-media-box__title {
      font-size: 16px;
      font-weight: bold; /* 标题加粗 */
    }
    
    .weui-media-box__desc {
      color: #666; /* 描述文字使用较深的灰色 */
      font-size: 14px; /* 调整字体大小 */
      margin-top: 10px;
    }
    .not-room-image {
        margin-top: 30px;
        display: block; /* 设置为块级元素 */
        margin-left: auto; /* 居中 */
        margin-right: auto; /* 居中 */
        transform: scale(0.8); /* 缩小图片尺寸 */
    }

    .not-room-desc {
        color: #666; /* 描述文字使用较深的灰色 */
        font-size: 14px; /* 调整字体大小 */
        margin-top: 10px;
        text-align: center; /* 文字水平居中 */
      }
  
      .placeholder {
        bottom: 10px; /* 距离底部的距离，应为容器底部内边距 + 提示信息高度 */
        width: 100%; /* 宽度占满容器 */
        text-align: center; /* 文本水平居中 */
        color: #888; /* 提示信息颜色 */
        font-size: 14px; /* 提示信息字体大小 */
      }
  