/* pages/room/history.wxss */
.history .page_bd .history-cell {
    background-color: #eeeeee;
    border-radius: 18px; /* 圆角 */
    margin: 10px;
    display: flex; /* 启用 flexbox 布局 */
    align-items: center; /* 垂直居中 */
    justify-content: space-between; /* 子元素间距平均分布 */
  }
  
  .weui-cell__bd, .weui-cell__ft {
    padding: 10px; /* 为子元素添加内边距 */
    margin: 0px 10px;
  }

  .weui-cell__bd-title {
    padding: 10px; /* 为子元素添加内边距 */
  }

  
.history .flag{
  padding-top: 30rpx;
  padding-left: 30rpx;
  font-weight: bold;
}

.weui-flex {
    display: flex; /* 确保使用flex布局 */
    align-items: center; /* 垂直居中所有子元素 */
}

.weui-flex__item {
    flex: 1; /* 确保所有子元素平分空间 */
    display: flex; /* 同样使用flex布局 */
    justify-content: center; /* 水平居中对齐 */
    align-items: center; /* 垂直居中对齐 */
}

.weui-flex__item:first-child {
    justify-content: flex-end; /* 第一个元素内的内容靠右对齐 */
}

.weui-flex__item:last-child {
    justify-content: flex-start; /* 最后一个元素内的内容靠左对齐 */
}

