<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<!--pages/meeting/edit.wxml-->
<view class="page">
  <view class="page_bd page__bd_spacing weui-cells weui-cells_after-title">
    <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
            <view class="weui-label">名称</view>
        </view>
        <view class="weui-cell__bd">
            <input class="weui-input" placeholder="请输入名称" value="{{name}}" data-obj="name" bindinput='bindKeyInput'/>
        </view>
    </view>
    <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
            <view class="weui-label">描述</view>
        </view>
        <view class="weui-cell__bd">
            <input class="weui-input" placeholder="请输入描述" value="{{description}}" data-obj="description" bindinput='bindKeyInput'/>
        </view>
    </view>
    <view class="weui-cells weui-cells_after-title">
        <view class="weui-cell">
            <view class="weui-cell__bd">会议室</view>
            <view class="weui-cell__ft">{{room.name}}</view>
        </view>
        <view class="weui-cell">
            <view class="weui-cell__bd">时间</view>
            <view class="weui-cell__ft">{{date}} {{timeUtils.formatTime(start_time)}} - {{timeUtils.formatTime(end_time)}}</view>
        </view>
    </view>
    
  </view>

  <block wx:if="{{!user_info.avatarurl}}">
      <view class="weui-cells__title">需要通过微信授权获得你的微信名和头像</view>
      <button class="save-btn" open-type="getUserInfo" lang="zh_CN" bindgetuserinfo="onGetUserInfo">授权登录</button>
    </block>
    <button class="save-btn" bindtap="save" wx:else>保存</button>
    
</view>

