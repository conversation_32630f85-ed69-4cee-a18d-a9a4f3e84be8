<wxs module="timeUtils" src="../../utils/timeUtils.wxs"></wxs>
<!--pages/meeting/detail.wxml-->
<view class="page">
  <view class="page__hd">
    <view class='weui-cell'>
      <view class='weui-cell__bd'>
        <view class="page__title">{{info.name}}</view>
        <view class="page__desc">{{info.description}}</view>
      </view>


      <button class="join-meeting-btn" bindtap='join' wx:if="{{!joined}}">参与</button>
      <button class="leave-meeting-btn" bindtap='leave' wx:else>退出</button>

    </view>
  </view>
  <view class="page_bd page__bd_spacing body-with-footer">
    <view class="weui-cells weui-cells_after-title">
        <view class="weui-cell">
            <view class="weui-cell__bd">会议室</view>
            <view class="weui-cell__ft">{{info.room.name}}</view>
        </view>
        <view class="weui-cell">
            <view class="weui-cell__bd">时间</view>
            <view class="weui-cell__ft">{{info.date}} {{timeUtils.formatTime(info.start_time)}} - {{timeUtils.formatTime(info.end_time)}}</view>
        </view>
        <view class="weui-cell">
            <view class="weui-cell__bd">发起人</view>
            <view class="weui-cell__ft"><image src="{{info.user.avatarurl}}" style="margin-right: 10rpx;vertical-align: middle;width:48rpx; height: 48rpx;"></image>{{info.user.nickname}}</view>
        </view>
        <view class="weui-cell" bindtap="attendees_show_change">
          <view class="weui-cell__bd">参与人</view>
          <view class="weui-cell__ft attendees {{attendees_show ? 'attendees_detail' : 'attendees_info'}}">
            <view wx:for="{{info.attendees}}" wx:key="id" wx:if="{{item.avatarurl}}">
              <image src="{{item.avatarurl}}" style="margin-right: 1rpx;vertical-align: middle;width:32rpx; height: 32rpx;"></image>
              <block wx:if="{{attendees_show && item.nickname}}">
                {{item.nickname}}
              </block>
            </view>
            <view wx:if="{{!attendees_show}}">
              ({{info.attendees.length}})
            </view>
            <view wx:elif="{{no_icon_attendee_num > 0}}">
              及其他{{no_icon_attendee_num}}人
            </view>
          </view>
        </view>
    </view>
  </view>
  
  <view class="weui-footer-detail">
    <block wx:if="{{owner}}">
        <button class="footer-edit" bindtap='edit'>修改预约</button>
        <button class="footer-del" bindtap='del'>取消预约</button>
    </block>
  </view>
  
</view>
