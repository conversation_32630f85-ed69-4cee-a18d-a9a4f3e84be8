/* pages/meeting/detail.wxss */
.attendees_detail view{
  float: none;
}
.attendees_info view{
  float: left;
}

.join-meeting-btn,
.leave-meeting-btn {
  padding: 0px 10px; /* 调整内边距以缩小按钮 */
  width: 81px;
  height: 30px;
  border-radius: 16px; /* 圆角 */
  border: none; /* 去除边框 */
  font-size: 12px; /* 文字大小 */
  transition: background-color 0.8s, color 0.3s; /* 过渡动画 */
}

.leave-meeting-btn {
  background-color: #44c97b; /* 淡绿色 */
  color: #ffffff; /* 淡绿色文字 */
}

.join-meeting-btn {
  background-color: #181818; /* 关注按钮背景色 */
  color: #ffffff; /* 关注按钮文字颜色 */
}

.weui-footer-detail {
    position: fixed; /* 固定定位 */
    bottom: 0; /* 底部对齐 */
    left: 0; /* 左侧对齐 */
    width: 100%; /* 宽度占满整个屏幕 */
    background-color: #ffffff; /* 底部导航栏背景色 */
    display: flex; /* 使用 Flexbox 布局 */
    justify-content: space-around; /* 水平居中并平均分配空间 */
    padding: 20px 0; /* 上下内边距 */
  }

.footer-edit {
    padding: 5px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    width: 120px;
    height: 45px;
    border: 1px solid #242424; /* 边框 */
    font-size: 14px; /* 文字大小 */
    background-color: #242424; /* 透明背景 */
    color: #ffffff; /* 文字颜色 */
}

.footer-del {
    padding: 5px 10px; /* 调整按钮内边距 */
    border-radius: 24px; /* 圆角 */
    width: 120px;
    height: 45px;
    border: 1px solid #181818; /* 边框 */
    font-size: 14px; /* 文字大小 */
    background-color: #24242400;
    color: #272727; /* 文字颜色 */
}