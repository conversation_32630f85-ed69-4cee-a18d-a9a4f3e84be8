<!--pages/meeting/reserve.wxml-->
<view class='page' hidden="{{loading}}">
  <date_select id="date_select" bindchange="date_select_change" start_date="{{start_date}}" end_date="{{end_date}}"></date_select>
  <view class='page_table'>
    <time_table
      id="time_table"
      bindtitle_label_click="title_label_click"
      bindtitle_click="title_click"
      binddata_click="data_click"
      no_title_desc="请先添加会议室"
      title_label="添加"></time_table>
  </view>
  <view class="footer">
    <button class="weui-btn" bindtap="reserve" wx:if="{{select.selected}}">预约时间({{select.start}}-{{select.end_real}})</button>
  </view>
</view>
