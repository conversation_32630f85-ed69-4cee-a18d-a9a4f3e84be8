// pages/my/my.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    userStats: null,
    loading: true,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
  },

  onLoad: function() {
    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },

  onShow: function() {
    this.loadUserInfo()
    this.loadUserStats()
  },

  // 加载用户信息
  loadUserInfo: function() {
    this.setData({ loading: true })

    app.userInfo((status, userInfo) => {
      if (status === 0) {
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true,
          loading: false
        })
      } else {
        this.setData({
          hasUserInfo: false,
          loading: false
        })
      }
    })
  },

  // 加载用户统计信息
  loadUserStats: function() {
    app.api.api_wechat_user_stats().then(stats => {
      this.setData({
        userStats: stats
      })
    }).catch(err => {
      console.error('加载用户统计失败:', err)
    })
  },

  // 获取用户信息授权
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        app.onGetUserInfo(res).then(() => {
          this.loadUserInfo()
        }).catch(err => {
          wx.showToast({
            title: '授权失败',
            icon: 'none'
          })
        })
      },
      fail: () => {
        wx.showToast({
          title: '授权失败',
          icon: 'none'
        })
      }
    })
  },

  // 获取手机号授权
  getPhoneNumber: function(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      app.onGetPhoneNumber(e).then(() => {
        this.loadUserInfo()
        wx.showToast({
          title: '手机号获取成功',
          icon: 'success'
        })
      }).catch(err => {
        wx.showToast({
          title: '手机号获取失败',
          icon: 'none'
        })
      })
    }
  },

  // 跳转到我的会议
  goToMyMeetings: function() {
    wx.navigateTo({
      url: '/pages/room/meeting'
    })
  },

  // 跳转到我创建的会议室
  goToMyRooms: function() {
    wx.navigateTo({
      url: '/pages/room/list?type=created'
    })
  },

  // 跳转到我关注的会议室
  goToFollowedRooms: function() {
    wx.navigateTo({
      url: '/pages/room/list?type=followed'
    })
  },

  // 关于我们
  showAbout: function() {
    wx.showModal({
      title: '关于我们',
      content: '会议预约小程序\n版本：1.0.0\n\n一个简单易用的会议室预约系统',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 意见反馈
  showFeedback: function() {
    wx.showModal({
      title: '意见反馈',
      content: '如有问题或建议，请联系管理员',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadUserInfo()
    this.loadUserStats()
    wx.stopPullDownRefresh()
  }
})