<!--pages/my/my.wxml-->
<view class="page" wx:if="{{!loading}}">

  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <view class="user-avatar-container">
      <image
        class="user-avatar"
        src="{{userInfo.avatarurl || '/images/unkonw.jpg'}}"
        mode="aspectFill"
      ></image>
    </view>

    <view class="user-details">
      <view class="user-name">{{userInfo.nickname || '未设置昵称'}}</view>
      <view class="user-location" wx:if="{{userInfo.province || userInfo.city}}">
        {{userInfo.province}} {{userInfo.city}}
      </view>
      <view class="user-phone" wx:if="{{userInfo.mobile}}">
        手机号：{{userInfo.mobile}}
      </view>
    </view>

    <!-- 授权按钮 -->
    <view class="auth-buttons" wx:if="{{!hasUserInfo}}">
      <button
        class="auth-btn"
        wx:if="{{canIUseGetUserProfile}}"
        bindtap="getUserProfile"
      >
        获取头像昵称
      </button>
      <button
        class="auth-btn"
        wx:else
        open-type="getUserInfo"
        bindgetuserinfo="getUserProfile"
      >
        获取头像昵称
      </button>
    </view>

    <!-- 手机号授权 -->
    <view class="phone-auth" wx:if="{{hasUserInfo && !userInfo.mobile}}">
      <button
        class="phone-btn"
        open-type="getPhoneNumber"
        bindgetphonenumber="getPhoneNumber"
      >
        获取手机号
      </button>
    </view>
  </view>

  <!-- 统计信息区域 -->
  <view class="stats-section" wx:if="{{userStats}}">
    <view class="stats-title">我的统计</view>
    <view class="stats-grid">
      <view class="stats-item">
        <view class="stats-number">{{userStats.today_meetings_count}}</view>
        <view class="stats-label">今日会议</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{userStats.week_meetings_count}}</view>
        <view class="stats-label">本周会议</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{userStats.attended_meetings_count}}</view>
        <view class="stats-label">参与会议</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{userStats.created_rooms_count}}</view>
        <view class="stats-label">创建会议室</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToMyMeetings">
        <view class="menu-icon">
          <image src="/images/icon-meeting.png" class="icon"></image>
        </view>
        <view class="menu-text">我的会议</view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="goToMyRooms">
        <view class="menu-icon">
          <image src="/images/icon-index.png" class="icon"></image>
        </view>
        <view class="menu-text">我创建的会议室</view>
        <view class="menu-badge" wx:if="{{userStats.created_rooms_count > 0}}">
          {{userStats.created_rooms_count}}
        </view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="goToFollowedRooms">
        <view class="menu-icon">
          <image src="/images/icon-index.png" class="icon"></image>
        </view>
        <view class="menu-text">我关注的会议室</view>
        <view class="menu-badge" wx:if="{{userStats.followed_rooms_count > 0}}">
          {{userStats.followed_rooms_count}}
        </view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="showFeedback">
        <view class="menu-icon">📝</view>
        <view class="menu-text">意见反馈</view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="showAbout">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-text">加载中...</view>
</view>