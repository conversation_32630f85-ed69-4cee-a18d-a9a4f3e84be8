/* pages/my/my.wxss */

.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

/* 用户信息区域 */
.user-info-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  position: relative;
}

.user-avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  text-align: center;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-location {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.user-phone {
  font-size: 26rpx;
  opacity: 0.7;
}

/* 授权按钮 */
.auth-buttons {
  margin-top: 30rpx;
  text-align: center;
}

.auth-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.auth-btn::after {
  border: none;
}

.phone-auth {
  margin-top: 20rpx;
  text-align: center;
}

.phone-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 40rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

.phone-btn::after {
  border: none;
}

/* 统计信息区域 */
.stats-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 15rpx;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单区域 */
.menu-section {
  margin: 20rpx;
}

.menu-group {
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f5f5f5;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}

.menu-icon .icon {
  width: 40rpx;
  height: 40rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-badge {
  background-color: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}