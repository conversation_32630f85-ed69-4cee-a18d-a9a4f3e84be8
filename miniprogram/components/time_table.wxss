/* components/time_table.wxss */
.time_table{
  height: 100%;
}
.table {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  font-size: 32rpx;
  line-height: 32rpx;
  border-right: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
}
.table .thead{
  height: 48rpx;
}
.table .tbody{
  position: absolute;
  top: 49rpx;
  bottom: 0;
  width: 100%;
  overflow: auto;
}
.table .tr{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.tr{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.table .th, .table .td{
  -webkit-box-flex:1;
  -webkit-flex:1;
  flex:1;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  margin-right:-1rpx;
  margin-bottom:-1rpx;
}
.table .th {
  font-weight: bold;
  border: 1rpx solid #ddd;
  background-color: #f5fafe;
  line-height: 48rpx;
  height: 48rpx;
}

.table .td {
  border-left: 1rpx solid #ddd;
  border-right: 1rpx solid #ddd;
  height: 36rpx;
}
.table .td.expire{
  border-top: 1rpx solid #f5f5f5;
  background-color: #f5f5f5;
}
.table .td.in_use{
  border-top: 1rpx solid #fcf8e3;
  background-color: #fcf8e3;
}
.table .td.selected{
  border-top: 1rpx solid #dfffd8;
  background-color: #dfffd8;
}
.table .td.top{
  border-top: 1rpx solid #ddd;
}
.table .td.bottom{
  border-bottom: 1rpx solid #ddd;
}
.label_item{
  font-size: 28rpx;
}
view.label{
  -webkit-box-flex:none!important;
  -webkit-flex:none!important;
  flex:none!important;
  width: 100rpx;
  background-color: #f5fafe;
  float: left;
}