/**app.wxss**/

  
button {
  padding-left:28rpx;
  padding-right:28rpx;
  font-size:36rpx;
}

button[size=mini] {
  border: 1rpx solid #ccc;
  padding:0 1em;
  font-size: 24rpx;
  white-space: nowrap;
}
button[size=mini]::after {
  border: none;
}
@import 'weui/weui.wxss';

.page{
  font-size: 32rpx;
  font-family: -apple-system-font,Helvetica Neue,Helvetica,sans-serif;
}
.page__hd {
  padding: 30rpx;
}
.page__bd {
  padding-bottom: 20rpx;
}
.page__bd_spacing {
  margin-top: 20px;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.page__ft{
  padding-bottom: 20rpx;
  text-align: center;
}

.page__title {
  margin-top: 15%;
  text-align: left;
  font-size: 40rpx;
  font-weight: 400;
}

.page__desc {
  margin-top: 10rpx;
  color: #888888;
  text-align: left;
  font-size: 28rpx;
}
.footer{
  background: #fff;
  position:fixed;
  bottom:0;
  left:0;
  right:0;
  height: 100rpx;
  padding-top: 20rpx;
}
.body-with-footer{
    margin-top: 10px;
  padding-bottom: 20rpx
}