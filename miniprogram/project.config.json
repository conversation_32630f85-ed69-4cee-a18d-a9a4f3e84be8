{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "newFeature": true, "autoAudits": false, "checkInvalidKey": true, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "3.8.10", "appid": "wxcae796699d942bc4", "projectname": "meeting", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}}