{"name": "vue-antd-pro", "version": "3.0.4", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --no-module", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "build:preview": "vue-cli-service build --no-module --mode preview", "lint:nofix": "vue-cli-service lint --no-fix", "lint:js": "eslint src/**/*.js --fix", "lint:css": "stylelint src/**/*.*ss --fix  --custom-syntax postcss-less", "prepare": "husky install"}, "dependencies": {"@ant-design-vue/pro-layout": "^1.0.12", "@antv/data-set": "^0.10.2", "ant-design-vue": "^1.7.8", "axios": "^0.26.1", "babel-loader": "8", "core-js": "^3.21.1", "enquire.js": "^2.1.6", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.3.0", "mockjs2": "1.0.8", "moment": "^2.29.2", "nprogress": "^0.2.0", "store": "^2.0.12", "viser-vue": "^2.4.8", "vue": "^2.6.14", "vue-clipboard2": "^0.2.1", "vue-cropper": "0.4.9", "vue-i18n": "^8.27.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.3", "vue-svg-component-runtime": "^1.0.1", "vue-template-compiler": "^2.6.14", "vuex": "^3.6.2", "wangeditor": "^3.1.1"}, "devDependencies": {"@ant-design/colors": "^3.2.2", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-unit-jest": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.3.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-remove-console": "^6.9.4", "commitizen": "^4.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.0.0", "eslint-plugin-html": "^5.0.5", "eslint-plugin-vue": "^5.2.3", "file-loader": "^6.2.0", "git-revision-webpack-plugin": "^3.0.6", "husky": "^6.0.0", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "^12.5.0", "postcss": "^8.3.5", "postcss-less": "^6.0.0", "regenerator-runtime": "^0.13.9", "stylelint": "^14.8.5", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-recommended": "^7.0.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "vue-svg-icon-loader": "^2.1.1", "vue-svg-loader": "0.16.0", "webpack-theme-color-replacer": "^1.3.26"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "gitHooks": {"pre-commit": "lint-staged"}}