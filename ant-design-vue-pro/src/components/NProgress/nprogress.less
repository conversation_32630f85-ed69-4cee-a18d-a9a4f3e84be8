@import url('../index.less');

/* Make clicks pass-through */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1031;
  width: 100%;
  height: 2px;
  background: @primary-color;
}

/* Fancy blur effect */
#nprogress .peg {
  position: absolute;
  right: 0;
  display: block;
  width: 100px;
  height: 100%;
  opacity: 1;
  transform: rotate(3deg) translate(0, -4px);
  transform: rotate(3deg) translate(0, -4px);
  transform: rotate(3deg) translate(0, -4px);
  box-shadow: 0 0 10px @primary-color, 0 0 5px @primary-color;
}

/* Remove these to get rid of the spinner */
#nprogress .spinner {
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 1031;
  display: block;
}

#nprogress .spinner-icon {
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: solid 2px transparent;
  border-top-color: @primary-color;
  border-left-color: @primary-color;
  border-radius: 50%;
  animation: nprogress-spinner 400ms linear infinite;
  animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
  position: relative;
  overflow: hidden;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@keyframes nprogress-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
@keyframes nprogress-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
