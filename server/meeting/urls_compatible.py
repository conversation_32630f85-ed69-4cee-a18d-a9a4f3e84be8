# encoding: utf-8
# 兼容性URL配置
from __future__ import absolute_import, unicode_literals

from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
import json

def health_check(request):
    """健康检查接口"""
    return JsonResponse({'status': 'ok', 'message': 'Django server is running'})

@method_decorator(csrf_exempt, name='dispatch')
class MockWechatLoginView(View):
    """模拟微信登录API"""
    def post(self, request):
        try:
            # 模拟登录成功响应
            response_data = {
                'code': 0,
                'msg': 'success',
                'data': {
                    'id': 1,
                    'openid': 'mock_openid_123',
                    'nickname': '测试用户',
                    'avatarurl': '/images/unkonw.jpg',
                    'need_refresh': False
                }
            }
            return JsonResponse(response_data)
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'msg': str(e),
                'data': None
            })

@method_decorator(csrf_exempt, name='dispatch')
class MockWechatUserInfoView(View):
    """模拟微信用户信息API"""
    def post(self, request):
        try:
            response_data = {
                'code': 0,
                'msg': 'success',
                'data': {
                    'id': 1,
                    'nickname': '测试用户',
                    'avatarurl': '/images/unkonw.jpg',
                    'avatar_url': '/images/unkonw.jpg',
                    'display_name': '测试用户',
                    'gender': 0,
                    'gender_display': '未知',
                    'language': 'zh_CN',
                    'country': '中国',
                    'province': '北京',
                    'city': '北京',
                    'mobile': '',
                    'need_refresh': False
                }
            }
            return JsonResponse(response_data)
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'msg': str(e),
                'data': None
            })

@method_decorator(csrf_exempt, name='dispatch')
class MockUserStatsView(View):
    """模拟用户统计API"""
    def get(self, request):
        try:
            response_data = {
                'code': 0,
                'msg': 'success',
                'data': {
                    'created_rooms_count': 2,
                    'followed_rooms_count': 5,
                    'initiated_meetings_count': 8,
                    'attended_meetings_count': 15,
                    'today_meetings_count': 1,
                    'week_meetings_count': 3,
                }
            }
            return JsonResponse(response_data)
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'msg': str(e),
                'data': None
            })

@method_decorator(csrf_exempt, name='dispatch')
class MockFollowRoomsView(View):
    """模拟关注会议室列表API"""
    def get(self, request):
        try:
            response_data = {
                'code': 0,
                'msg': 'success',
                'data': [
                    {
                        'id': 1,
                        'name': '会议室A',
                        'description': '大型会议室，可容纳50人',
                        'create_user': {
                            'id': 1,
                            'nickname': '管理员'
                        }
                    },
                    {
                        'id': 2,
                        'name': '会议室B',
                        'description': '小型会议室，可容纳10人',
                        'create_user': {
                            'id': 2,
                            'nickname': '用户B'
                        }
                    }
                ]
            }
            return JsonResponse(response_data)
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'msg': str(e),
                'data': None
            })

@method_decorator(csrf_exempt, name='dispatch')
class MockCreateRoomsView(View):
    """模拟创建的会议室列表API"""
    def get(self, request):
        try:
            response_data = {
                'code': 0,
                'msg': 'success',
                'data': [
                    {
                        'id': 1,
                        'name': '我的会议室',
                        'description': '我创建的会议室',
                        'create_user': {
                            'id': 1,
                            'nickname': '测试用户'
                        }
                    }
                ]
            }
            return JsonResponse(response_data)
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'msg': str(e),
                'data': None
            })

def mock_wsapi(request):
    """模拟WebSocket API"""
    return JsonResponse({
        'code': 0,
        'msg': 'WebSocket API mock',
        'data': {'status': 'connected'}
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/health/', health_check, name='health_check'),
    
    # 微信相关API
    path('api/wechat/login', MockWechatLoginView.as_view(), name='wechat_login'),
    path('api/wechat/user/info', MockWechatUserInfoView.as_view(), name='wechat_user_info'),
    path('api/wechat/user/stats', MockUserStatsView.as_view(), name='wechat_user_stats'),
    
    # 会议相关API
    path('api/meeting/follow/rooms', MockFollowRoomsView.as_view(), name='meeting_follow_rooms'),
    path('api/meeting/create/rooms', MockCreateRoomsView.as_view(), name='meeting_create_rooms'),
    
    # WebSocket API
    path('wsapi', mock_wsapi, name='wsapi'),
]
