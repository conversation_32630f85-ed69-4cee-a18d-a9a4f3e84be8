# encoding: utf-8
# 测试环境配置文件（不依赖Redis）
from __future__ import absolute_import, unicode_literals

SECRET_KEY = 'eb1dmEZG90E9x7tMcvZ/sIT9D5CEvT49jVD8C8uwF+4='

# MySQL配置
MYSQL_HOST = '127.0.0.1'
MYSQL_PORT = '3306'
MYSQL_DBNAME = 'meeting'
MYSQL_USERNAME = 'root'  # 使用root用户进行测试
MYSQL_PASSWORD = ''  # 请根据您的MySQL配置修改密码

# 微信配置
WECHAT_APPID = 'wxcae796699d942bc4'
WECHAT_APPSECRET = 'f83f53f65c3431f7d2144a890f2d2bfa'

# Redis配置（测试时可以设置为空，使用内存缓存）
REDIS_HOST = '127.0.0.1'
REDIS_PASSWORD = ''
REDIS_PORT = '6379'
REDIS_CACHE_DB = 5
REDIS_SESSION_DB = 6
REDIS_CELERY_DB = 7
REDIS_CONSTANCE_DB = 8
REDIS_CHANNEL_DB = 9
