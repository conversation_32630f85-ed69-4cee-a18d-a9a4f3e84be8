# encoding: utf-8
# 最小化URL配置
from __future__ import absolute_import, unicode_literals

from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse

def health_check(request):
    """健康检查接口"""
    return JsonResponse({'status': 'ok', 'message': 'Django server is running'})

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/health/', health_check, name='health_check'),
]
