#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接脚本
"""

import pymysql

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        # 连接配置
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '',  # 请根据您的MySQL配置修改
            'charset': 'utf8mb4'
        }
        
        print("正在测试MySQL连接...")
        print(f"连接配置: {config}")
        
        # 尝试连接
        connection = pymysql.connect(**config)
        print("✅ MySQL连接成功!")
        
        # 检查是否存在meeting数据库
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES LIKE 'meeting'")
        result = cursor.fetchone()
        
        if result:
            print("✅ meeting数据库已存在")
        else:
            print("⚠️  meeting数据库不存在，正在创建...")
            cursor.execute("CREATE DATABASE meeting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ meeting数据库创建成功!")
        
        # 检查是否可以连接到meeting数据库
        connection.close()
        config['database'] = 'meeting'
        connection = pymysql.connect(**config)
        print("✅ 成功连接到meeting数据库!")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n请检查以下配置:")
        print("1. MySQL服务是否已启动")
        print("2. 用户名和密码是否正确")
        print("3. 主机和端口是否正确")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("MySQL数据库连接测试")
    print("=" * 50)
    
    success = test_mysql_connection()
    
    if success:
        print("\n🎉 数据库连接测试通过!")
        print("现在可以运行Django迁移命令了")
    else:
        print("\n💡 请先解决数据库连接问题，然后重新运行此脚本")
