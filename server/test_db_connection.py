#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接脚本
"""

import pymysql

def setup_mysql_database():
    """设置MySQL数据库和用户"""
    try:
        # 首先尝试使用root用户连接
        root_config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'gogogo000',  # 使用您提供的root密码
            'charset': 'utf8mb4'
        }

        print("正在使用root用户连接MySQL...")
        print(f"连接配置: {root_config}")

        connection = pymysql.connect(**root_config)
        print("✅ root用户连接成功!")

        cursor = connection.cursor()

        # 检查并创建meeting数据库
        cursor.execute("SHOW DATABASES LIKE 'meeting'")
        result = cursor.fetchone()

        if not result:
            print("⚠️  meeting数据库不存在，正在创建...")
            cursor.execute("CREATE DATABASE meeting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ meeting数据库创建成功!")
        else:
            print("✅ meeting数据库已存在")

        # 检查并创建meeting用户
        cursor.execute("SELECT User FROM mysql.user WHERE User = 'meeting'")
        user_result = cursor.fetchone()

        if not user_result:
            print("⚠️  meeting用户不存在，正在创建...")
            cursor.execute("CREATE USER 'meeting'@'localhost' IDENTIFIED BY 'gogogo000'")
            cursor.execute("GRANT ALL PRIVILEGES ON meeting.* TO 'meeting'@'localhost'")
            cursor.execute("FLUSH PRIVILEGES")
            print("✅ meeting用户创建成功并授权!")
        else:
            print("✅ meeting用户已存在")
            # 确保用户有正确的权限
            cursor.execute("GRANT ALL PRIVILEGES ON meeting.* TO 'meeting'@'localhost'")
            cursor.execute("FLUSH PRIVILEGES")
            print("✅ meeting用户权限已更新!")

        connection.close()

        # 现在测试meeting用户连接
        return test_meeting_user_connection()

    except Exception as e:
        print(f"❌ 数据库设置失败: {e}")
        print("\n请检查以下配置:")
        print("1. MySQL服务是否已启动")
        print("2. root用户密码是否正确")
        print("3. 主机和端口是否正确")
        return False

def test_meeting_user_connection():
    """测试meeting用户连接"""
    try:
        # meeting用户连接配置
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'meeting',
            'password': 'gogogo000',
            'database': 'meeting',
            'charset': 'utf8mb4'
        }

        print("\n正在测试meeting用户连接...")
        connection = pymysql.connect(**config)
        print("✅ meeting用户连接成功!")

        # 测试基本操作
        cursor = connection.cursor()
        cursor.execute("SELECT DATABASE()")
        db_name = cursor.fetchone()[0]
        print(f"✅ 当前数据库: {db_name}")

        connection.close()
        return True

    except Exception as e:
        print(f"❌ meeting用户连接失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("MySQL数据库设置和连接测试")
    print("=" * 50)

    success = setup_mysql_database()

    if success:
        print("\n🎉 数据库设置和连接测试通过!")
        print("现在可以运行Django迁移命令了")
        print("\n下一步:")
        print("1. 运行: python manage.py migrate --settings=meeting.settings_test")
        print("2. 创建超级用户: python manage.py createsuperuser --settings=meeting.settings_test")
        print("3. 启动开发服务器: python manage.py runserver --settings=meeting.settings_test")
    else:
        print("\n💡 请先解决数据库连接问题，然后重新运行此脚本")
