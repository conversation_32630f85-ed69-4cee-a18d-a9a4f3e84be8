#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Django迁移脚本
"""

import os
import sys
import django
from django.core.management import execute_from_command_line
from django.conf import settings

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'meeting.settings_simple')
    django.setup()

def test_basic_migration():
    """测试基本迁移"""
    try:
        print("=" * 50)
        print("Django基本迁移测试")
        print("=" * 50)
        
        # 设置Django环境
        setup_django()
        
        print("✅ Django环境设置成功!")
        print(f"✅ 使用数据库: {settings.DATABASES['default']['NAME']}")
        
        # 检查迁移状态
        print("\n正在检查迁移状态...")
        execute_from_command_line(['manage.py', 'showmigrations'])
        
        # 创建基本迁移
        print("\n正在创建基本迁移...")
        execute_from_command_line(['manage.py', 'migrate', 'contenttypes'])
        execute_from_command_line(['manage.py', 'migrate', 'auth'])
        execute_from_command_line(['manage.py', 'migrate', 'admin'])
        execute_from_command_line(['manage.py', 'migrate', 'sessions'])
        
        print("\n✅ 基本迁移完成!")
        
        # 测试数据库连接
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM django_migrations")
        migration_count = cursor.fetchone()[0]
        print(f"✅ 数据库中已有 {migration_count} 个迁移记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_migration()
    
    if success:
        print("\n🎉 Django基本迁移测试通过!")
        print("\n下一步:")
        print("1. 手动创建应用迁移文件")
        print("2. 运行完整迁移")
        print("3. 创建超级用户")
        print("4. 启动开发服务器")
    else:
        print("\n💡 请检查错误信息并解决问题")
