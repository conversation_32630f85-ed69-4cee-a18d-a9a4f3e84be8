version: '3.8'
services:
  meeting:
    build:
      context: ./server
    container_name: meeting
    restart: always
    environment:
      # MYSQL数据库配置
      MYSQL_HOST: mysql  # MySQL容器的名称
      MYSQL_PORT: 3306
      MYSQL_DBNAME: meeting  # 数据库名称
      MYSQL_USERNAME: meeting  # 你的MySQL用户
      MYSQL_PASSWORD: 2x4D6PND6pKKcbbh  # 你的MySQL密码
      # REDIS数据库配置
      REDIS_HOST: redis  # REDIS容器的名称
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis_ssXNma  # 你的REDIS密码
      # 小程序信息配置
      WECHAT_APPID: wxcae796699d942bc4  # 小程序ID
      WECHAT_APPSECRET: f83f53f65c3431f7d2144a890f2d2bfa  # 你的小程序APPSECRET

    ports:
      - "5858:8000"

    networks:
      - 1panel-network
      
networks:
  1panel-network:
    external: true