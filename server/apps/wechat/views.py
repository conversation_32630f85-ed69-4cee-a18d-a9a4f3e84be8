# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool.views import ViewSite, CoolAPIException, ErrorCode
from rest_framework import fields
from django.contrib.auth import authenticate, login

from core import utils

from . import biz, models, serializer


site = ViewSite(name='wechat', app_name='wechat')


@site
class Login(utils.APIBase):
    name = "小程序登录"
    response_info_serializer_class = serializer.UserSerializer

    def get_context(self, request, *args, **kwargs):
        session = biz.wechat.wxa.code_to_session(request.params.js_code)
        wxa_user, new = models.User.objects.get_or_create(openid=session['openid'])
        wxa_user.set_info(session)
        login_user = authenticate(request, openid=wxa_user.openid)
        if login_user is None:
            raise CoolAPIException(ErrorCode.ERROR_BAD_PARAMETER)
        login(request, login_user)
        return wxa_user

    class Meta:
        param_fields = (
            ('js_code', fields.CharField(label='小程序登录code', required=True)),
        )


class UserBaseView(utils.APIBase):

    def check_api_permissions(self, request, *args, **kwargs):
        super(UserBaseView, self).check_api_permissions(request, *args, **kwargs)
        if not isinstance(request.user, models.User):
            raise CoolAPIException(ErrorCode.ERR_WECHAT_LOGIN)

    def get_context(self, request, *args, **kwargs):
        raise NotImplementedError

    class Meta:
        path = '/'


@site
class UserInfo(UserBaseView):
    name = "小程序用户信息"
    response_info_serializer_class = serializer.UserSerializer

    def get_context(self, request, *args, **kwargs):
        if request.params.encrypted_data and request.params.iv:
            try:
                # 检查session_key是否存在
                if not request.user.session_key:
                    raise CoolAPIException(ErrorCode.ERR_WECHAT_LOGIN, "用户session_key不存在，请重新登录")

                data = biz.decrypt_message(request.user.session_key, request.params.iv, request.params.encrypted_data)

                # 验证解密后的数据
                if not isinstance(data, dict):
                    raise CoolAPIException(ErrorCode.ERROR_SYSTEM, "解密数据格式错误")

                # 验证openid是否匹配
                if 'openId' in data and data['openId'] != request.user.openid:
                    raise CoolAPIException(ErrorCode.ERROR_SYSTEM, "用户身份验证失败")

                request.user.set_info(data)

            except CoolAPIException:
                raise
            except Exception as e:
                utils.exception_logging.exception("decrypt_message", extra={
                    'request': request,
                    'error': str(e),
                    'user_id': request.user.id,
                    'has_session_key': bool(request.user.session_key)
                })
                raise CoolAPIException(ErrorCode.ERROR_SYSTEM, "用户信息解密失败，请重新授权")
        return request.user

    class Meta:
        param_fields = (
            ('encrypted_data', fields.CharField(label='完整用户信息的加密数据', required=False, default=None)),
            ('iv', fields.CharField(label='加密算法的初始向量', required=False, default=None)),
        )


@site
class UserStats(UserBaseView):
    name = "用户统计信息"

    def get_context(self, request, *args, **kwargs):
        from apps.meetings.models import Meeting, Room, MeetingAttendee
        from django.db.models import Count, Q
        import datetime

        user = request.user
        today = datetime.date.today()

        # 统计用户创建的会议室数量
        created_rooms_count = Room.objects.filter(create_user=user).count()

        # 统计用户关注的会议室数量
        followed_rooms_count = Room.objects.filter(
            follows__user=user,
            follows__delete_status=False
        ).count()

        # 统计用户发起的会议数量
        initiated_meetings_count = Meeting.objects.filter(user=user).count()

        # 统计用户参与的会议数量
        attended_meetings_count = MeetingAttendee.objects.filter(
            user=user,
            delete_status=False
        ).count()

        # 统计今日会议数量
        today_meetings_count = MeetingAttendee.objects.filter(
            user=user,
            meeting__date=today,
            delete_status=False
        ).count()

        # 统计本周会议数量
        week_start = today - datetime.timedelta(days=today.weekday())
        week_end = week_start + datetime.timedelta(days=6)
        week_meetings_count = MeetingAttendee.objects.filter(
            user=user,
            meeting__date__gte=week_start,
            meeting__date__lte=week_end,
            delete_status=False
        ).count()

        return {
            'created_rooms_count': created_rooms_count,
            'followed_rooms_count': followed_rooms_count,
            'initiated_meetings_count': initiated_meetings_count,
            'attended_meetings_count': attended_meetings_count,
            'today_meetings_count': today_meetings_count,
            'week_meetings_count': week_meetings_count,
        }


urlpatterns = site.urlpatterns
app_name = site.app_name
