# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from cool import model
from django.db import models
from django.utils import timezone

from core import utils
from core.utils import admin_register

from . import constants


@admin_register(addable=False, changeable=False)
class User(utils.BaseModel, model.AbstractUserMixin):
    openid = models.CharField('openId', max_length=64, null=False, default='', unique=True)
    unionid = models.CharField('unionId', max_length=64, null=False, default='', db_index=True)
    session_key = models.Char<PERSON>ield(verbose_name='session_key', max_length=256, default='', blank=True)
    nickname = models.CharField('昵称', max_length=64, null=False, default='')
    gender = models.IntegerField('性别', choices=constants.GenderCode.get_choices_list(),
                                 default=constants.GenderCode.UNKNOWN.code, null=False)
    language = models.Char<PERSON>ield('语言', max_length=64, null=False, default='')
    country = models.Char<PERSON>ield('国家', max_length=64, null=False, default='')
    province = models.CharField('省份', max_length=64, null=False, default='')
    city = models.CharField('城市', max_length=64, null=False, default='')
    avatarurl = models.URLField('头像', max_length=512, null=False, default='', blank=True)
    mobile = models.CharField(verbose_name='小程序授权手机号', max_length=32, default='', blank=True)

    def __str__(self):
        return self.nickname

    @classmethod
    def ex_search_fields(cls):
        ret = super(User, cls).ex_search_fields()
        ret.add('nickname')
        return ret

    def set_info(self, user_info, save=True):
        """设置用户信息，支持微信用户信息和手机号信息"""
        for k, v in user_info.items():
            k = k.lower()
            if k in ('subscribe', 'unionid', 'nickname', 'gender', 'language',
                     'country', 'province', 'city', 'avatarurl', 'session_key', 'mobile'):
                # 处理特殊字段
                if k == 'avatarurl' and v:
                    # 确保头像URL是有效的
                    if isinstance(v, str) and (v.startswith('http://') or v.startswith('https://')):
                        setattr(self, k, v)
                elif k == 'mobile' and v:
                    # 处理手机号，去除可能的加密前缀
                    if isinstance(v, str):
                        setattr(self, k, v)
                elif k == 'session_key' and v:
                    # 确保session_key是字符串
                    if isinstance(v, str):
                        setattr(self, k, v)
                else:
                    setattr(self, k, v)
        if save:
            self.save_changed()

    @property
    def display_name(self):
        """获取显示名称"""
        return self.nickname or f"用户{self.id}"

    @property
    def avatar_url(self):
        """获取头像URL，如果没有则返回默认头像"""
        if self.avatarurl:
            return self.avatarurl
        return '/images/unkonw.jpg'  # 默认头像路径

    @property
    def need_refresh(self):
        """需要重新获取"""
        return (timezone.now() - self.modify_time).total_seconds() > 86400

    class Meta:
        verbose_name = verbose_name_plural = "用户"
