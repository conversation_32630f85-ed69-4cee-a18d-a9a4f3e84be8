# encoding: utf-8
from __future__ import absolute_import, unicode_literals

from rest_framework import serializers
from core import utils

from . import models


class UserSerializer(utils.BaseSerializer):
    display_name = serializers.ReadOnlyField()
    avatar_url = serializers.ReadOnlyField()
    gender_display = serializers.SerializerMethodField()

    def get_gender_display(self, obj):
        """获取性别显示文本"""
        return obj.get_gender_display()

    class Meta:
        model = models.User
        fields = ('id', 'nickname', 'avatarurl', 'avatar_url', 'display_name',
                 'gender', 'gender_display', 'language', 'country', 'province',
                 'city', 'mobile', 'need_refresh')
